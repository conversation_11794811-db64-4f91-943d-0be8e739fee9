#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL支付测试用例最终版输出生成器
生成文本格式和XMind格式的最终版测试用例
"""

import json
import os
import zipfile
import tempfile
import xml.etree.ElementTree as ET
from datetime import datetime

def create_final_text_format(json_file, output_file):
    """创建最终版文本格式"""
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    lines = []
    
    # 添加标题和版本信息
    lines.append("BNPL支付测试用例最终版")
    lines.append("=" * 50)
    lines.append("")
    lines.append(f"版本: {data['metadata']['version']}")
    lines.append(f"创建日期: {data['metadata']['create_date']}")
    lines.append(f"测试用例总数: {data['metadata']['total_cases']}个")
    lines.append(f"业务范围: {data['metadata']['business_scope']}")
    lines.append("")
    
    # BNPL分期区间
    lines.append("BNPL分期区间:")
    for provider, range_info in data['metadata']['test_ranges'].items():
        lines.append(f"- {provider}: {range_info}")
    lines.append("")
    
    # 测试原则
    lines.append("测试原则:")
    for principle in data['metadata']['test_principles']:
        lines.append(f"- {principle}")
    lines.append("")
    
    # 测试模块统计
    lines.append("测试模块分布:")
    for module_name, cases in data['test_modules'].items():
        lines.append(f"- {module_name}: {len(cases)}个用例")
    lines.append("")
    
    # 详细测试用例
    lines.append("详细测试用例:")
    lines.append("-" * 50)
    
    for module_name, cases in data['test_modules'].items():
        lines.append(f"\n{module_name}")
        lines.append("=" * len(module_name))
        
        # 按平台分组显示
        platform_groups = {}
        for case in cases:
            platform = case['level2_platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(case)
        
        for platform, platform_cases in platform_groups.items():
            lines.append(f"\n{platform}平台:")
            
            # 按用户类型分组
            user_groups = {}
            for case in platform_cases:
                user = case['level3_user']
                if user not in user_groups:
                    user_groups[user] = []
                user_groups[user].append(case)
            
            for user, user_cases in user_groups.items():
                lines.append(f"  {user}:")

                # 显示所有测试用例，不省略任何一个
                for case in user_cases:
                    lines.append(f"    {case['id']}: {case['name']}")
                    lines.append(f"      平台: {case['level2_platform']}")
                    lines.append(f"      用户: {case['level3_user']}")
                    lines.append(f"      场景: {case['level4_scenario']}")
                    lines.append(f"      操作: {case['level5_operation']}")
                    lines.append(f"      步骤: {case['level6_step']}")
                    lines.append(f"      子步骤: {case['level7_substep']}")
                    lines.append(f"      预期结果: {case['level8_expected']}")

                    # 添加完整的测试数据信息
                    if 'test_data' in case and case['test_data']:
                        test_data = case['test_data']
                        lines.append(f"      测试数据:")
                        if 'amount' in test_data:
                            lines.append(f"        - 金额: US${test_data['amount']}")
                        if 'expected_bnpl' in test_data:
                            lines.append(f"        - 可用BNPL: {test_data['expected_bnpl']}")
                        if 'discount_type' in test_data:
                            lines.append(f"        - 优惠类型: {test_data['discount_type']}")
                        if 'description' in test_data:
                            lines.append(f"        - 说明: {test_data['description']}")
                        if 'scenario_type' in test_data:
                            lines.append(f"        - 场景类型: {test_data['scenario_type']}")
                    lines.append("")
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    return lines

def create_final_xmind_content(json_file):
    """创建最终版XMind内容XML"""
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建根元素
    root = ET.Element('xmap-content')
    root.set('xmlns', "urn:xmind:xmap:xmlns:content:2.0")
    root.set('xmlns:fo', "http://www.w3.org/1999/XSL/Format")
    root.set('version', "2.0")
    
    # 创建工作表
    sheet = ET.SubElement(root, 'sheet', id="sheet1", theme="theme1")
    
    # 创建根主题
    root_topic = ET.SubElement(sheet, 'topic', id="root", **{"structure-class": "org.xmind.ui.logic.right"})
    root_title = ET.SubElement(root_topic, 'title')
    root_title.text = "BNPL支付测试用例最终版"
    
    # 添加子主题容器
    root_children = ET.SubElement(root_topic, 'children')
    main_topics = ET.SubElement(root_children, 'topics', type="attached")
    
    # 添加版本信息
    version_topic = ET.SubElement(main_topics, 'topic', id="version")
    version_title = ET.SubElement(version_topic, 'title')
    version_title.text = f"版本信息 {data['metadata']['version']}"
    
    version_children = ET.SubElement(version_topic, 'children')
    version_topics = ET.SubElement(version_children, 'topics', type="attached")
    
    version_details = [
        f"创建日期: {data['metadata']['create_date']}",
        f"测试用例总数: {data['metadata']['total_cases']}个",
        f"业务范围: {data['metadata']['business_scope']}",
        "测试重点: 平台核心逻辑，不测试第三方功能"
    ]
    
    for i, detail in enumerate(version_details):
        detail_topic = ET.SubElement(version_topics, 'topic', id=f"version_detail_{i}")
        detail_title = ET.SubElement(detail_topic, 'title')
        detail_title.text = detail
    
    # 添加BNPL分期区间
    range_topic = ET.SubElement(main_topics, 'topic', id="ranges")
    range_title = ET.SubElement(range_topic, 'title')
    range_title.text = "BNPL分期区间"
    
    range_children = ET.SubElement(range_topic, 'children')
    range_topics = ET.SubElement(range_children, 'topics', type="attached")
    
    for i, (provider, range_info) in enumerate(data['metadata']['test_ranges'].items()):
        provider_topic = ET.SubElement(range_topics, 'topic', id=f"range_{i}")
        provider_title = ET.SubElement(provider_topic, 'title')
        provider_title.text = f"{provider}: {range_info}"
    
    # 添加测试模块
    module_id = 0
    for module_name, cases in data['test_modules'].items():
        module_id += 1
        module_topic = ET.SubElement(main_topics, 'topic', id=f"module_{module_id}")
        module_title = ET.SubElement(module_topic, 'title')
        module_title.text = f"{module_name} ({len(cases)}个用例)"
        
        module_children = ET.SubElement(module_topic, 'children')
        module_topics = ET.SubElement(module_children, 'topics', type="attached")
        
        # 按平台分组
        platform_groups = {}
        for case in cases:
            platform = case['level2_platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(case)
        
        platform_id = 0
        for platform, platform_cases in platform_groups.items():
            platform_id += 1
            platform_topic = ET.SubElement(module_topics, 'topic', id=f"platform_{module_id}_{platform_id}")
            platform_title = ET.SubElement(platform_topic, 'title')
            platform_title.text = f"{platform}平台 ({len(platform_cases)}个用例)"
            
            platform_children = ET.SubElement(platform_topic, 'children')
            platform_topics_elem = ET.SubElement(platform_children, 'topics', type="attached")

            # 显示所有用例，不省略任何一个
            for i, case in enumerate(platform_cases):
                case_topic = ET.SubElement(platform_topics_elem, 'topic', id=f"case_{module_id}_{platform_id}_{i}")
                case_title = ET.SubElement(case_topic, 'title')
                case_title.text = f"{case['id']}: {case['name']}"

                case_children = ET.SubElement(case_topic, 'children')
                case_topics = ET.SubElement(case_children, 'topics', type="attached")

                # 添加操作步骤
                operation_topic = ET.SubElement(case_topics, 'topic', id=f"operation_{module_id}_{platform_id}_{i}")
                operation_title = ET.SubElement(operation_topic, 'title')
                operation_title.text = f"操作: {case['level5_operation']}"

                # 添加测试步骤
                step_topic = ET.SubElement(case_topics, 'topic', id=f"step_{module_id}_{platform_id}_{i}")
                step_title = ET.SubElement(step_topic, 'title')
                step_title.text = f"步骤: {case['level6_step']}"

                # 添加子步骤
                substep_topic = ET.SubElement(case_topics, 'topic', id=f"substep_{module_id}_{platform_id}_{i}")
                substep_title = ET.SubElement(substep_topic, 'title')
                substep_title.text = f"子步骤: {case['level7_substep']}"

                # 添加预期结果
                expected_topic = ET.SubElement(case_topics, 'topic', id=f"expected_{module_id}_{platform_id}_{i}")
                expected_title = ET.SubElement(expected_topic, 'title')
                expected_title.text = f"预期: {case['level8_expected']}"

                # 添加测试数据（如果有）
                if 'test_data' in case and case['test_data']:
                    test_data = case['test_data']
                    if any(key in test_data for key in ['amount', 'expected_bnpl', 'discount_type']):
                        data_topic = ET.SubElement(case_topics, 'topic', id=f"data_{module_id}_{platform_id}_{i}")
                        data_title = ET.SubElement(data_topic, 'title')
                        data_info = []
                        if 'amount' in test_data:
                            data_info.append(f"金额: US${test_data['amount']}")
                        if 'expected_bnpl' in test_data:
                            data_info.append(f"可用BNPL: {test_data['expected_bnpl']}")
                        if 'discount_type' in test_data:
                            data_info.append(f"优惠类型: {test_data['discount_type']}")
                        data_title.text = f"测试数据: {'; '.join(data_info)}"
    
    return ET.tostring(root, encoding='unicode', method='xml')

def create_manifest_xml():
    """创建manifest.xml文件"""
    manifest_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
</manifest>'''
    return manifest_xml

def create_final_xmind_file(json_file, output_path):
    """创建最终版XMind文件"""
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # 创建META-INF目录
        meta_inf_dir = os.path.join(temp_dir, 'META-INF')
        os.makedirs(meta_inf_dir, exist_ok=True)
        
        # 写入content.xml
        content_path = os.path.join(temp_dir, 'content.xml')
        with open(content_path, 'w', encoding='utf-8') as f:
            f.write('<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n')
            f.write(create_final_xmind_content(json_file))
        
        # 写入manifest.xml
        manifest_path = os.path.join(meta_inf_dir, 'manifest.xml')
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write(create_manifest_xml())
        
        # 创建XMind文件（实际上是ZIP文件）
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as xmind_file:
            # 添加content.xml
            xmind_file.write(content_path, 'content.xml')
            # 添加manifest.xml
            xmind_file.write(manifest_path, 'META-INF/manifest.xml')
    
    return True

if __name__ == "__main__":
    json_file = "bnpl_payment_test_cases_final.json"
    text_output = "../输出用例/BNPL支付测试用例最终版_v11.0.txt"
    xmind_output = "../输出用例/BNPL支付测试用例最终版_v11.0.xmind"
    
    try:
        print("开始生成BNPL支付测试用例最终版输出文件...")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(text_output), exist_ok=True)
        
        # 生成文本格式
        lines = create_final_text_format(json_file, text_output)
        print(f"✅ 文本格式已生成: {text_output}")
        print(f"📋 包含 {len(lines)} 行内容")
        
        # 生成XMind格式
        success = create_final_xmind_file(json_file, xmind_output)
        if success:
            print(f"✅ XMind格式已生成: {xmind_output}")
            file_size = os.path.getsize(xmind_output)
            print(f"📊 文件大小: {file_size} 字节")
        
        print(f"\n🎉 BNPL支付测试用例最终版生成完成!")
        print(f"📁 输出文件:")
        print(f"  - 文本格式: {text_output}")
        print(f"  - XMind格式: {xmind_output}")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

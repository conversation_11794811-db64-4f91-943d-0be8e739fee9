{"title": "BNPL先享后付功能全面测试用例", "version": "v3.0", "created_date": "2025-07-01", "total_cases": 255, "test_modules": [{"module_name": "BNPL购买流程测试", "description": "验证不同平台、用户、场景下的BNPL支付完整流程", "test_cases": [{"case_id": "BNPL_001", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_002", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_003", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_004", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_005", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_006", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_007", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_008", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_009", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_010", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_011", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_012", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_013", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_014", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_015", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_016", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_017", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_018", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_019", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_020", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_021", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_022", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_023", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_024", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_025", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_026", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_027", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_028", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_029", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_030", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_031", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_032", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_033", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_034", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_035", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_036", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_037", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_038", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_039", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_040", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Affirm先享后付功能", "sub_action": "选择12个月分期方案", "expected_result": "扣款金额与分期计划一致，Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Affirm", "priority": "高"}, {"case_id": "BNPL_041", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_042", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_043", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_044", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_045", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_046", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_047", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_048", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_049", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_050", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_051", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_052", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_053", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_054", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_055", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_056", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_057", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_058", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_059", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_060", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_061", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_062", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_063", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_064", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_065", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_066", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_067", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_068", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_069", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_070", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_071", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_072", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_073", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_074", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_075", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_076", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_077", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_078", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_079", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_080", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Klarna先享后付功能", "sub_action": "选择Pay in 4分期方案", "expected_result": "扣款金额与分期计划一致，Klarna支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "高"}, {"case_id": "BNPL_081", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_082", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_083", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_084", "case_name": "支付", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_085", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_086", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_087", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_088", "case_name": "支付", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_089", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_090", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_091", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_092", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_093", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_094", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_095", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_096", "case_name": "支付", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_097", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_098", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_099", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_100", "case_name": "支付", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_101", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_102", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_103", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_104", "case_name": "支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_105", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_106", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_107", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_108", "case_name": "支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_109", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_110", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_111", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_112", "case_name": "支付", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_113", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_114", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_115", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_116", "case_name": "支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_117", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_118", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_119", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "商品页直接购买进入直接结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}, {"case_id": "BNPL_120", "case_name": "支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "立即购买进入商品页结算", "action": "点击结算并使用Afterpay先享后付功能", "sub_action": "选择4期分期方案", "expected_result": "扣款金额与分期计划一致，Afterpay支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "provider": "Afterpay", "priority": "高"}]}, {"module_name": "BNPL优惠叠加测试", "description": "验证BNPL与各种优惠活动的叠加使用场景", "test_cases": [{"case_id": "BNPL_121", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_122", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_123", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_124", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_125", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_126", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_127", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_128", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_129", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_130", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_131", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_132", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Affirm支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Affirm支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Affirm", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_133", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_134", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_135", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_136", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_137", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_138", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_139", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_140", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_141", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_142", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_143", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_144", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Klarna支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Klarna支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "<PERSON><PERSON><PERSON>", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_145", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_146", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_147", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_148", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_149", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_150", "case_name": "优惠叠加支付", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_151", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_152", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_153", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_154", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与优惠券活动的商品", "action": "应用优惠券后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "优惠券优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_155", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与O币抵扣活动的商品", "action": "应用O币抵扣后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "O币抵扣优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_156", "case_name": "优惠叠加支付", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠商品结算", "operation": "选择参与会员价活动的商品", "action": "应用会员价后使用Afterpay支付", "sub_action": "选择分期方案并确认", "expected_result": "会员价优惠正确应用，BNPL分期基于优惠后价格计算，Afterpay支付流程正常，订单总金额计算正确，优惠明细清晰显示"}, "provider": "Afterpay", "discount_type": "会员价", "priority": "中"}]}, {"module_name": "BNPL退款场景测试", "description": "验证不同时机和类型的BNPL订单退款处理", "test_cases": [{"case_id": "BNPL_157", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Affirm支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "订单确认后立即退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_158", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Affirm支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "订单确认后立即退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_159", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Affirm支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "发货前退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_160", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Affirm支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "发货前退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_161", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Affirm支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "发货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_162", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Affirm支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "发货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_163", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Affirm支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "收货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_164", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Affirm支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Affirm退款接口", "expected_result": "退款申请成功提交，Affirm接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Affirm", "refund_timing": "收货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_165", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Klarna支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "订单确认后立即退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_166", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Klarna支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "订单确认后立即退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_167", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Klarna支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "发货前退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_168", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Klarna支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "发货前退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_169", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Klarna支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "发货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_170", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Klarna支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "发货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_171", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Klarna支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "收货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_172", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Klarna支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Klarna退款接口", "expected_result": "退款申请成功提交，Klarna接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "<PERSON><PERSON><PERSON>", "refund_timing": "收货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_173", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Afterpay支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "订单确认后立即退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_174", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对Afterpay支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "订单确认后立即退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_175", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Afterpay支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "发货前退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_176", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对Afterpay支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "发货前退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_177", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Afterpay支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "发货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_178", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对Afterpay支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "发货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_179", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Afterpay支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "收货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_180", "case_name": "退款处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对Afterpay支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用Afterpay退款接口", "expected_result": "退款申请成功提交，Afterpay接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "provider": "Afterpay", "refund_timing": "收货后退款", "refund_type": "部分退款", "priority": "高"}]}, {"module_name": "BNPL异常场景测试", "description": "验证各种异常情况下的系统处理能力", "test_cases": [{"case_id": "BNPL_181", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_182", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_183", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_184", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_185", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_186", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_187", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_188", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_189", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_190", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_191", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_192", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_193", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_194", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_195", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Affirm支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Affirm", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_196", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_197", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_198", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_199", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_200", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_201", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_202", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_203", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_204", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_205", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_206", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_207", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_208", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_209", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_210", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Klarna支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "<PERSON><PERSON><PERSON>", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_211", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_212", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_213", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_214", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_215", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_216", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_217", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_218", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_219", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "审批拒绝异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发审批拒绝异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别审批拒绝异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "审批拒绝", "priority": "中"}, {"case_id": "BNPL_220", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_221", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_222", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重复提交异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发重复提交异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别重复提交异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "重复提交", "priority": "中"}, {"case_id": "BNPL_223", "case_name": "异常处理", "structure": {"platform": "Web", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_224", "case_name": "异常处理", "structure": {"platform": "H5", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_225", "case_name": "异常处理", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入Afterpay支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统响应和处理", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"}, "provider": "Afterpay", "exception_type": "支付中断", "priority": "中"}]}, {"module_name": "BNPL跨平台测试", "description": "验证跨平台操作的数据同步和用户体验", "test_cases": [{"case_id": "BNPL_226", "case_name": "跨平台支付", "structure": {"platform": "Web下单+H5支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到H5支付使用Affirm支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Affirm支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Affirm", "priority": "中"}, {"case_id": "BNPL_227", "case_name": "跨平台支付", "structure": {"platform": "H5下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在H5下单创建订单", "action": "切换到Web支付使用Affirm支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Affirm支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Affirm", "priority": "中"}, {"case_id": "BNPL_228", "case_name": "跨平台支付", "structure": {"platform": "App下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在App下单创建订单", "action": "切换到Web支付使用Affirm支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Affirm支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Affirm", "priority": "中"}, {"case_id": "BNPL_229", "case_name": "跨平台支付", "structure": {"platform": "Web下单+App查看", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到App查看使用Affirm支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Affirm支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Affirm", "priority": "中"}, {"case_id": "BNPL_230", "case_name": "跨平台支付", "structure": {"platform": "Web下单+H5支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到H5支付使用Klarna支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Klarna支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "中"}, {"case_id": "BNPL_231", "case_name": "跨平台支付", "structure": {"platform": "H5下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在H5下单创建订单", "action": "切换到Web支付使用Klarna支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Klarna支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "中"}, {"case_id": "BNPL_232", "case_name": "跨平台支付", "structure": {"platform": "App下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在App下单创建订单", "action": "切换到Web支付使用Klarna支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Klarna支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "中"}, {"case_id": "BNPL_233", "case_name": "跨平台支付", "structure": {"platform": "Web下单+App查看", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到App查看使用Klarna支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Klarna支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "<PERSON><PERSON><PERSON>", "priority": "中"}, {"case_id": "BNPL_234", "case_name": "跨平台支付", "structure": {"platform": "Web下单+H5支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到H5支付使用Afterpay支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Afterpay支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Afterpay", "priority": "中"}, {"case_id": "BNPL_235", "case_name": "跨平台支付", "structure": {"platform": "H5下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在H5下单创建订单", "action": "切换到Web支付使用Afterpay支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Afterpay支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Afterpay", "priority": "中"}, {"case_id": "BNPL_236", "case_name": "跨平台支付", "structure": {"platform": "App下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在App下单创建订单", "action": "切换到Web支付使用Afterpay支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Afterpay支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Afterpay", "priority": "中"}, {"case_id": "BNPL_237", "case_name": "跨平台支付", "structure": {"platform": "Web下单+App查看", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到App查看使用Afterpay支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台订单信息同步正确，Afterpay支付流程正常，订单状态在各平台一致显示，用户体验流畅"}, "provider": "Afterpay", "priority": "中"}]}, {"module_name": "BNPL边界值测试", "description": "验证各种边界条件下的系统处理", "test_cases": [{"case_id": "BNPL_238", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最小金额测试", "operation": "使用0.01美元进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理0.01美元边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "最小金额", "priority": "中"}, {"case_id": "BNPL_239", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最大金额测试", "operation": "使用5000美元进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理5000美元边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "最大金额", "priority": "中"}, {"case_id": "BNPL_240", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用1件商品进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理1件商品边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_241", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用50件商品进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理50件商品边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_242", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最短分期进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最短分期边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "分期期数边界", "priority": "中"}, {"case_id": "BNPL_243", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最长分期进行Affirm支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最长分期边界值，Affirm支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Affirm", "boundary_type": "分期期数边界", "priority": "中"}, {"case_id": "BNPL_244", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最小金额测试", "operation": "使用0.01美元进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理0.01美元边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "最小金额", "priority": "中"}, {"case_id": "BNPL_245", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最大金额测试", "operation": "使用5000美元进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理5000美元边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "最大金额", "priority": "中"}, {"case_id": "BNPL_246", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用1件商品进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理1件商品边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_247", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用50件商品进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理50件商品边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_248", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最短分期进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最短分期边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "分期期数边界", "priority": "中"}, {"case_id": "BNPL_249", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最长分期进行Klarna支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最长分期边界值，Klarna支付流程正常，边界值验证通过，错误提示准确"}, "provider": "<PERSON><PERSON><PERSON>", "boundary_type": "分期期数边界", "priority": "中"}, {"case_id": "BNPL_250", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最小金额测试", "operation": "使用0.01美元进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理0.01美元边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "最小金额", "priority": "中"}, {"case_id": "BNPL_251", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "最大金额测试", "operation": "使用5000美元进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理5000美元边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "最大金额", "priority": "中"}, {"case_id": "BNPL_252", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用1件商品进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理1件商品边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_253", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品数量边界测试", "operation": "使用50件商品进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理50件商品边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "商品数量边界", "priority": "中"}, {"case_id": "BNPL_254", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最短分期进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最短分期边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "分期期数边界", "priority": "中"}, {"case_id": "BNPL_255", "case_name": "边界值测试", "structure": {"platform": "Web", "user_type": "会员", "scenario": "分期期数边界测试", "operation": "使用最长分期进行Afterpay支付", "action": "验证边界值处理", "sub_action": "确认系统边界值响应", "expected_result": "系统正确处理最长分期边界值，Afterpay支付流程正常，边界值验证通过，错误提示准确"}, "provider": "Afterpay", "boundary_type": "分期期数边界", "priority": "中"}]}]}
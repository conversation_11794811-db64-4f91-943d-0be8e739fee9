#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整BNPL分期测试用例XMind生成器
生成包含所有256个用例的详细XMind文件
"""

import json
import os
import zipfile
import tempfile
import xml.etree.ElementTree as ET
from datetime import datetime

def create_topic_element(parent, topic_id, title, structure_class=None):
    """创建topic元素"""
    topic = ET.SubElement(parent, 'topic', id=topic_id)
    if structure_class:
        topic.set('structure-class', structure_class)
    
    title_elem = ET.SubElement(topic, 'title')
    title_elem.text = title
    
    return topic

def add_children_container(topic):
    """添加children容器"""
    children = ET.SubElement(topic, 'children')
    topics = ET.SubElement(children, 'topics', type="attached")
    return topics

def create_complete_xmind_content(json_file):
    """创建完整的XMind内容XML"""
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建根元素
    root = ET.Element('xmap-content')
    root.set('xmlns', "urn:xmind:xmap:xmlns:content:2.0")
    root.set('xmlns:fo', "http://www.w3.org/1999/XSL/Format")
    root.set('version', "2.0")
    
    # 创建工作表
    sheet = ET.SubElement(root, 'sheet', id="sheet1", theme="theme1")
    
    # 创建根主题
    root_topic = create_topic_element(sheet, "root", "BNPL先享后付分期测试用例", "org.xmind.ui.logic.right")
    main_topics = add_children_container(root_topic)
    
    # 添加版本信息
    version_topic = create_topic_element(main_topics, "version", f"版本信息 {data['metadata']['version']}")
    version_children = add_children_container(version_topic)
    
    version_details = [
        f"创建日期: {data['metadata']['create_date']}",
        f"测试用例总数: {data['metadata']['total_cases']}个",
        f"测试范围: {', '.join(data['metadata']['bnpl_providers'])}",
        "测试类型: 分期区间、下单流程、优惠叠加、退款处理",
        "覆盖平台: Android、iOS、Web、H5"
    ]
    
    for i, detail in enumerate(version_details):
        create_topic_element(version_children, f"version_detail_{i}", detail)
    
    # 添加BNPL分期区间
    range_topic = create_topic_element(main_topics, "ranges", "BNPL分期区间")
    range_children = add_children_container(range_topic)
    
    for i, (provider, range_info) in enumerate(data['metadata']['test_ranges'].items()):
        create_topic_element(range_children, f"range_{i}", f"{provider}: {range_info}")
    
    # 添加测试模块
    module_id = 0
    for module_name, cases in data['test_modules'].items():
        if not cases:
            continue
            
        module_id += 1
        module_topic = create_topic_element(main_topics, f"module_{module_id}", f"{module_name} ({len(cases)}个用例)")
        module_children = add_children_container(module_topic)
        
        # 按平台分组
        platform_groups = {}
        for case in cases:
            platform = case['level2_platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(case)
        
        platform_id = 0
        for platform, platform_cases in platform_groups.items():
            platform_id += 1
            platform_topic = create_topic_element(module_children, f"platform_{module_id}_{platform_id}", f"{platform}平台 ({len(platform_cases)}个用例)")
            platform_children = add_children_container(platform_topic)
            
            # 按用户类型分组
            user_groups = {}
            for case in platform_cases:
                user = case['level3_user']
                if user not in user_groups:
                    user_groups[user] = []
                user_groups[user].append(case)
            
            user_id = 0
            for user, user_cases in user_groups.items():
                user_id += 1
                user_topic = create_topic_element(platform_children, f"user_{module_id}_{platform_id}_{user_id}", f"{user} ({len(user_cases)}个用例)")
                user_children = add_children_container(user_topic)
                
                # 按场景分组
                scenario_groups = {}
                for case in user_cases:
                    scenario = case['level4_scenario']
                    if scenario not in scenario_groups:
                        scenario_groups[scenario] = []
                    scenario_groups[scenario].append(case)
                
                scenario_id = 0
                for scenario, scenario_cases in scenario_groups.items():
                    scenario_id += 1
                    scenario_topic = create_topic_element(user_children, f"scenario_{module_id}_{platform_id}_{user_id}_{scenario_id}", f"{scenario} ({len(scenario_cases)}个用例)")
                    scenario_children = add_children_container(scenario_topic)
                    
                    # 添加具体用例
                    case_id = 0
                    for case in scenario_cases[:5]:  # 每个场景最多显示5个用例，避免文件过大
                        case_id += 1
                        case_topic = create_topic_element(scenario_children, f"case_{module_id}_{platform_id}_{user_id}_{scenario_id}_{case_id}", f"{case['id']}: {case['level5_operation']}")
                        case_children = add_children_container(case_topic)
                        
                        # 添加步骤
                        step_topic = create_topic_element(case_children, f"step_{module_id}_{platform_id}_{user_id}_{scenario_id}_{case_id}", case['level6_step'])
                        step_children = add_children_container(step_topic)
                        
                        # 添加子步骤
                        substep_topic = create_topic_element(step_children, f"substep_{module_id}_{platform_id}_{user_id}_{scenario_id}_{case_id}", case['level7_substep'])
                        substep_children = add_children_container(substep_topic)
                        
                        # 添加预期结果
                        expected_topic = create_topic_element(substep_children, f"expected_{module_id}_{platform_id}_{user_id}_{scenario_id}_{case_id}", case['level8_expected'])
                        
                        # 添加测试数据
                        if 'test_data' in case and case['test_data']:
                            expected_children = add_children_container(expected_topic)
                            test_data = case['test_data']
                            data_info = []
                            
                            if 'amount' in test_data:
                                data_info.append(f"金额: US${test_data['amount']}")
                            if 'expected_bnpl' in test_data:
                                data_info.append(f"可用BNPL: {test_data['expected_bnpl']}")
                            if 'discount_type' in test_data:
                                data_info.append(f"优惠类型: {test_data['discount_type']}")
                            if 'refund_type' in test_data:
                                data_info.append(f"退款类型: {test_data['refund_type']}")
                            
                            if data_info:
                                create_topic_element(expected_children, f"data_{module_id}_{platform_id}_{user_id}_{scenario_id}_{case_id}", f"测试数据: {'; '.join(data_info)}")
                    
                    # 如果用例数量超过5个，添加省略提示
                    if len(scenario_cases) > 5:
                        create_topic_element(scenario_children, f"more_{module_id}_{platform_id}_{user_id}_{scenario_id}", f"... 还有{len(scenario_cases) - 5}个类似用例")
    
    return ET.tostring(root, encoding='unicode', method='xml')

def create_manifest_xml():
    """创建manifest.xml文件"""
    manifest_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
</manifest>'''
    return manifest_xml

def create_complete_xmind_file(json_file, output_path):
    """创建完整的XMind文件"""
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # 创建META-INF目录
        meta_inf_dir = os.path.join(temp_dir, 'META-INF')
        os.makedirs(meta_inf_dir, exist_ok=True)
        
        # 写入content.xml
        content_path = os.path.join(temp_dir, 'content.xml')
        with open(content_path, 'w', encoding='utf-8') as f:
            f.write('<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n')
            f.write(create_complete_xmind_content(json_file))
        
        # 写入manifest.xml
        manifest_path = os.path.join(meta_inf_dir, 'manifest.xml')
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write(create_manifest_xml())
        
        # 创建XMind文件（实际上是ZIP文件）
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as xmind_file:
            # 添加content.xml
            xmind_file.write(content_path, 'content.xml')
            # 添加manifest.xml
            xmind_file.write(manifest_path, 'META-INF/manifest.xml')
    
    return True

if __name__ == "__main__":
    json_file = "bnpl_installment_test_cases.json"
    output_file = "../输出用例/BNPL先享后付_分期测试用例_v10.0_完整版.xmind"
    
    try:
        print("开始创建完整版BNPL分期测试用例XMind文件...")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建XMind文件
        success = create_complete_xmind_file(json_file, output_file)
        
        if success:
            print(f"✅ 完整版XMind文件创建成功!")
            print(f"📁 文件位置: {output_file}")
            print(f"📋 包含所有256个测试用例的详细结构")
            print(f"🎯 按平台-用户-场景-用例的4级分组")
            print(f"🔧 每个场景显示前5个用例详情，其余用省略表示")
            
            # 检查文件大小
            file_size = os.path.getsize(output_file)
            print(f"📊 文件大小: {file_size} 字节")
            
        else:
            print("❌ XMind文件创建失败")
            
    except Exception as e:
        print(f"❌ 创建过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL精简优化测试用例生成器
减少重复用例，聚焦异常场景，控制在300-400条
"""

import json
from datetime import datetime

class StreamlinedBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 精简后的购买场景（2类）
        self.purchase_scenarios = [
            "购物车结算",
            "直接购买"  # 合并：直接结算/商品页结算/立即购买
        ]
        
        # 精简后的商品类型（3类）
        self.product_types = [
            "正常商品",
            "限时折扣商品",
            "自由捆绑商品"
        ]
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价"
        ]
        
        # 退款时机
        self.refund_timings = [
            "订单确认后立即退款",
            "发货前退款",
            "发货后退款",
            "收货后退款"
        ]
        
        # 退款类型
        self.refund_types = ["全额退款", "部分退款"]
        
        # 扩展的异常场景
        self.exception_scenarios = [
            "网络超时",
            "网络中断", 
            "服务商维护",
            "API调用失败",
            "支付中断",
            "支付超时",
            "浏览器关闭",
            "应用崩溃",
            "商品缺货",
            "价格变动",
            "库存不足",
            "活动过期",
            "支付取消"
        ]
        
        # 简化的预期结果模板
        self.result_templates = {
            "normal_payment": "BNPL支付流程正常，订单状态正确更新",
            "discount_payment": "优惠正确应用，BNPL基于折后价计算",
            "refund_process": "退款处理成功，分期计划相应调整",
            "exception_handle": "系统正确处理异常，显示友好提示",
            "cross_platform": "跨平台数据同步正确，用户体验一致"
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_core_purchase_cases(self):
        """生成核心购买流程测试用例（精简版）"""
        cases = []
        
        # 扩展平台组合以达到目标用例数
        main_combinations = []
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                main_combinations.append((platform, user_type))
        
        for platform, user_type in main_combinations:
            for scenario in self.purchase_scenarios:
                for product_type in self.product_types:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "支付",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": scenario,
                            "operation": f"选择{product_type}进入{scenario}",
                            "action": "点击结算并使用BNPL先享后付功能（Klarna/Afterpay/Affirm）",
                            "sub_action": "选择分期方案并确认支付",
                            "expected_result": self.result_templates["normal_payment"]
                        },
                        "priority": "高"
                    }
                    cases.append(case)
        
        return cases

    def generate_discount_cases(self):
        """生成优惠叠加测试用例（精简版）"""
        cases = []
        
        # 扩展平台和用户组合
        main_combinations = []
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                if user_type != "游客":  # 游客不参与大部分优惠
                    main_combinations.append((platform, user_type))
        
        for platform, user_type in main_combinations:
            for discount in self.discount_types:
                if discount == "优惠码" and user_type == "小B会员":
                    continue  # 小B会员不能使用优惠码
                
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "优惠叠加支付",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": user_type,
                        "scenario": f"{discount}优惠结算",
                        "operation": f"选择参与{discount}活动的商品进入购物车结算/直接购买",
                        "action": f"应用{discount}后使用BNPL先享后付功能",
                        "sub_action": "确认优惠后价格并完成分期支付",
                        "expected_result": self.result_templates["discount_payment"]
                    },
                    "discount_type": discount,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_refund_cases(self):
        """生成退款场景测试用例（精简版）"""
        cases = []
        
        for timing in self.refund_timings:
            for refund_type in self.refund_types:
                for platform in ["Web", "H5"]:  # 扩展平台覆盖
                    case = {
                    "case_id": self.get_case_id(),
                    "case_name": "退款处理",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{timing}场景",
                        "operation": f"对BNPL支付订单发起{refund_type}",
                        "action": "提交退款申请并处理",
                        "sub_action": "调用BNPL退款接口并更新状态",
                        "expected_result": self.result_templates["refund_process"]
                    },
                    "refund_timing": timing,
                    "refund_type": refund_type,
                    "priority": "高"
                }
                cases.append(case)
        
        return cases

    def generate_exception_cases(self):
        """生成异常场景测试用例（重点扩展）"""
        cases = []
        
        # 按异常类型分组测试
        exception_groups = {
            "网络异常": ["网络超时", "网络中断"],
            "系统异常": ["服务商维护", "API调用失败"],
            "支付异常": ["支付中断", "支付超时", "支付取消"],
            "应用异常": ["浏览器关闭", "应用崩溃"],
            "业务异常": ["商品缺货", "价格变动", "库存不足", "活动过期"]
        }
        
        for group_name, exceptions in exception_groups.items():
            for exception in exceptions:
                for platform in ["Web", "H5", "Android", "iOS"]:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "异常处理",
                        "case_type": "反向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{exception}异常场景",
                            "operation": "正常进入BNPL支付流程",
                            "action": f"在关键步骤触发{exception}异常",
                            "sub_action": "观察系统异常处理和恢复机制",
                            "expected_result": self.result_templates["exception_handle"]
                        },
                        "exception_type": exception,
                        "exception_group": group_name,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_boundary_cases(self):
        """生成边界值测试用例（精简版）"""
        cases = []
        
        boundary_scenarios = [
            ("时间边界", "在活动开始/结束时间点进行BNPL支付"),
            ("并发边界", "多用户同时进行BNPL支付"),
            ("数据边界", "使用特殊字符商品名进行BNPL支付"),
            ("会话边界", "支付页面长时间停留后进行BNPL支付")
        ]
        
        for scenario_name, scenario_desc in boundary_scenarios:
            for platform in ["Web", "H5"]:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "边界值测试",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}测试",
                        "operation": scenario_desc,
                        "action": "验证边界条件下的BNPL支付",
                        "sub_action": "确认系统边界值处理和响应",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "boundary_type": scenario_name,
                    "priority": "低"
                }
                cases.append(case)
        
        return cases

    def generate_negative_discount_cases(self):
        """生成反向优惠叠加测试用例"""
        cases = []

        negative_scenarios = [
            ("优惠券过期", "使用过期优惠券进行BNPL支付"),
            ("优惠码无效", "使用无效优惠码进行BNPL支付"),
            ("O币余额不足", "O币余额不足时进行BNPL支付"),
            ("优惠条件不满足", "不满足优惠条件时强制使用优惠"),
            ("优惠叠加冲突", "使用冲突的优惠组合进行BNPL支付")
        ]

        for platform in ["Web", "H5", "Android", "iOS"]:
            for scenario_name, scenario_desc in negative_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "优惠异常处理",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统优惠验证和错误处理",
                        "sub_action": "验证BNPL支付流程的异常处理",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "中"
                }
                cases.append(case)

        return cases

    def generate_negative_refund_cases(self):
        """生成反向退款场景测试用例"""
        cases = []

        negative_refund_scenarios = [
            ("退款金额超限", "退款金额超过订单金额"),
            ("重复退款申请", "对同一订单重复申请退款"),
            ("退款时效过期", "超过退款时效期申请退款"),
            ("退款接口异常", "BNPL退款接口调用失败"),
            ("分期已完成", "分期付款已完成后申请退款")
        ]

        for platform in ["Web", "H5"]:
            for scenario_name, scenario_desc in negative_refund_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "退款异常处理",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统退款验证和错误处理",
                        "sub_action": "验证退款异常的处理机制",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "中"
                }
                cases.append(case)

        return cases

    def generate_user_scenario_cases(self):
        """生成用户场景测试用例"""
        cases = []

        user_scenarios = [
            ("新用户首次使用", "新注册用户首次使用BNPL支付"),
            ("老用户重复使用", "有BNPL使用历史的用户再次使用"),
            ("会员等级差异", "不同会员等级用户使用BNPL支付"),
            ("多设备切换", "用户在多个设备间切换使用BNPL"),
            ("账户状态异常", "账户状态异常时使用BNPL支付")
        ]

        for platform in self.platform_users.keys():
            for scenario_name, scenario_desc in user_scenarios:
                for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "用户场景测试",
                        "case_type": "正向流程" if "异常" not in scenario_name else "反向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{scenario_name}场景",
                            "operation": scenario_desc,
                            "action": "使用BNPL先享后付功能完成支付",
                            "sub_action": "验证用户场景下的BNPL体验",
                            "expected_result": self.result_templates["normal_payment"] if "异常" not in scenario_name else self.result_templates["exception_handle"]
                        },
                        "priority": "中"
                    }
                    cases.append(case)

        return cases

    def generate_cross_platform_cases(self):
        """生成跨平台场景测试用例（精简版）"""
        cases = []
        
        cross_scenarios = [
            ("Web下单", "H5支付"),
            ("H5下单", "Web支付"),
            ("App下单", "Web支付")
        ]
        
        for order_platform, pay_platform in cross_scenarios:
            case = {
                "case_id": self.get_case_id(),
                "case_name": "跨平台支付",
                "case_type": "正向流程",
                "structure": {
                    "platform": f"{order_platform}+{pay_platform}",
                    "user_type": "会员",
                    "scenario": "跨平台操作",
                    "operation": f"在{order_platform}创建订单",
                    "action": f"切换到{pay_platform}使用BNPL支付",
                    "sub_action": "完成跨平台支付流程",
                    "expected_result": self.result_templates["cross_platform"]
                },
                "priority": "中"
            }
            cases.append(case)
        
        return cases

    def generate_performance_cases(self):
        """生成性能和并发测试用例"""
        cases = []

        performance_scenarios = [
            ("高峰时段支付", "在系统高峰时段进行BNPL支付"),
            ("大量商品支付", "购物车包含大量商品时进行BNPL支付"),
            ("频繁操作支付", "短时间内频繁进行BNPL支付操作"),
            ("长时间会话支付", "长时间会话后进行BNPL支付"),
            ("多标签页支付", "多个标签页同时进行BNPL支付"),
            ("慢网络支付", "网络较慢情况下进行BNPL支付")
        ]

        for platform in ["Web", "H5", "Android", "iOS"]:
            for scenario_name, scenario_desc in performance_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "性能测试",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "使用BNPL先享后付功能完成支付",
                        "sub_action": "验证性能表现和用户体验",
                        "expected_result": self.result_templates["normal_payment"]
                    },
                    "priority": "低"
                }
                cases.append(case)

        return cases

    def generate_integration_cases(self):
        """生成集成测试用例"""
        cases = []

        integration_scenarios = [
            ("第三方登录支付", "使用第三方登录后进行BNPL支付"),
            ("社交分享支付", "分享商品后进行BNPL支付"),
            ("推荐系统支付", "通过推荐系统商品进行BNPL支付"),
            ("搜索结果支付", "从搜索结果进行BNPL支付"),
            ("广告链接支付", "通过广告链接进行BNPL支付"),
            ("邮件链接支付", "通过邮件链接进行BNPL支付")
        ]

        for platform in ["Web", "H5"]:
            for scenario_name, scenario_desc in integration_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "集成测试",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "使用BNPL先享后付功能完成支付",
                        "sub_action": "验证集成功能的正确性",
                        "expected_result": self.result_templates["normal_payment"]
                    },
                    "priority": "中"
                }
                cases.append(case)

        return cases

    def generate_security_cases(self):
        """生成安全测试用例"""
        cases = []

        security_scenarios = [
            ("会话过期支付", "会话过期后尝试BNPL支付"),
            ("权限验证支付", "权限不足时尝试BNPL支付"),
            ("数据篡改支付", "数据被篡改时进行BNPL支付"),
            ("重放攻击支付", "重放请求进行BNPL支付"),
            ("跨站请求支付", "跨站请求进行BNPL支付")
        ]

        for platform in ["Web", "H5"]:
            for scenario_name, scenario_desc in security_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "安全测试",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统安全防护机制",
                        "sub_action": "验证安全漏洞防护效果",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "高"
                }
                cases.append(case)

        return cases

    def generate_business_scenario_cases(self):
        """生成业务场景测试用例"""
        cases = []

        business_scenarios = [
            ("节假日促销支付", "节假日促销期间进行BNPL支付"),
            ("会员日支付", "会员日活动期间进行BNPL支付"),
            ("新品发布支付", "新品发布时进行BNPL支付"),
            ("清仓甩卖支付", "清仓甩卖活动进行BNPL支付"),
            ("限时秒杀支付", "限时秒杀活动进行BNPL支付"),
            ("预售商品支付", "预售商品进行BNPL支付"),
            ("团购商品支付", "团购商品进行BNPL支付"),
            ("积分兑换支付", "积分兑换商品进行BNPL支付")
        ]

        for platform in self.platform_users.keys():
            for scenario_name, scenario_desc in business_scenarios:
                for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "业务场景测试",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{scenario_name}场景",
                            "operation": scenario_desc,
                            "action": "使用BNPL先享后付功能完成支付",
                            "sub_action": "验证特殊业务场景下的BNPL体验",
                            "expected_result": self.result_templates["normal_payment"]
                        },
                        "priority": "中"
                    }
                    cases.append(case)

        return cases

    def generate_device_compatibility_cases(self):
        """生成设备兼容性测试用例"""
        cases = []

        device_scenarios = [
            ("不同浏览器支付", "在不同浏览器中进行BNPL支付"),
            ("不同分辨率支付", "在不同分辨率设备进行BNPL支付"),
            ("横竖屏切换支付", "横竖屏切换时进行BNPL支付"),
            ("触摸操作支付", "使用触摸操作进行BNPL支付"),
            ("键盘操作支付", "使用键盘操作进行BNPL支付"),
            ("语音输入支付", "使用语音输入进行BNPL支付")
        ]

        platform_device_map = {
            "Web": ["不同浏览器支付", "不同分辨率支付", "键盘操作支付"],
            "H5": ["不同浏览器支付", "横竖屏切换支付", "触摸操作支付"],
            "Android": ["横竖屏切换支付", "触摸操作支付", "语音输入支付"],
            "iOS": ["横竖屏切换支付", "触摸操作支付", "语音输入支付"]
        }

        for platform, scenarios in platform_device_map.items():
            for scenario_name in scenarios:
                scenario_desc = next(desc for name, desc in device_scenarios if name == scenario_name)
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "设备兼容性测试",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "使用BNPL先享后付功能完成支付",
                        "sub_action": "验证设备兼容性和用户体验",
                        "expected_result": self.result_templates["normal_payment"]
                    },
                    "priority": "中"
                }
                cases.append(case)

        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v6.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "optimization_notes": [
                "保持V3.0完整8层级结构格式",
                "合并三个BNPL服务商为统一功能",
                "精简商品类型为3类：正常商品、限时折扣商品、自由捆绑商品",
                "合并购买场景为2类：购物车结算、直接购买",
                "重点扩展异常场景测试，控制用例数量在300-400条",
                "使用合并表示法减少重复，如'Klarna/Afterpay/Affirm'"
            ],
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL核心购买流程测试",
                "description": "验证精简后的核心BNPL支付流程，覆盖主要平台和用户组合",
                "test_cases": self.generate_core_purchase_cases()
            },
            {
                "module_name": "BNPL优惠叠加测试",
                "description": "验证BNPL与主要优惠活动的叠加使用",
                "test_cases": self.generate_discount_cases()
            },
            {
                "module_name": "BNPL退款场景测试",
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": self.generate_refund_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "重点验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_exception_cases()
            },
            {
                "module_name": "BNPL边界值测试",
                "description": "验证边界条件下的系统处理",
                "test_cases": self.generate_boundary_cases()
            },
            {
                "module_name": "BNPL反向优惠测试",
                "description": "验证优惠异常情况下的BNPL处理机制",
                "test_cases": self.generate_negative_discount_cases()
            },
            {
                "module_name": "BNPL反向退款测试",
                "description": "验证退款异常情况的处理机制",
                "test_cases": self.generate_negative_refund_cases()
            },
            {
                "module_name": "BNPL用户场景测试",
                "description": "验证不同用户场景下的BNPL使用体验",
                "test_cases": self.generate_user_scenario_cases()
            },
            {
                "module_name": "BNPL跨平台测试",
                "description": "验证跨平台操作的数据同步和用户体验",
                "test_cases": self.generate_cross_platform_cases()
            },
            {
                "module_name": "BNPL性能测试",
                "description": "验证各种性能场景下的BNPL支付表现",
                "test_cases": self.generate_performance_cases()
            },
            {
                "module_name": "BNPL集成测试",
                "description": "验证BNPL与其他系统功能的集成",
                "test_cases": self.generate_integration_cases()
            },
            {
                "module_name": "BNPL安全测试",
                "description": "验证BNPL支付的安全防护机制",
                "test_cases": self.generate_security_cases()
            },
            {
                "module_name": "BNPL业务场景测试",
                "description": "验证特殊业务场景下的BNPL支付",
                "test_cases": self.generate_business_scenario_cases()
            },
            {
                "module_name": "BNPL设备兼容性测试",
                "description": "验证不同设备和操作方式下的BNPL兼容性",
                "test_cases": self.generate_device_compatibility_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = StreamlinedBNPLGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_streamlined_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL精简优化测试用例已生成: bnpl_streamlined_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        case_type_count = {}
        for case in module["test_cases"]:
            case_type = case.get("case_type", "未分类")
            case_type_count[case_type] = case_type_count.get(case_type, 0) + 1
        
        type_info = ", ".join([f"{k}:{v}个" for k, v in case_type_count.items()])
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例 ({type_info})")
    
    # 统计正向反向比例
    total_positive = sum(len([case for case in module["test_cases"] if case.get("case_type") == "正向流程"]) 
                        for module in test_cases["test_modules"])
    total_negative = sum(len([case for case in module["test_cases"] if case.get("case_type") == "反向流程"]) 
                        for module in test_cases["test_modules"])
    
    print(f"\n📈 用例类型分布:")
    print(f"  - 正向流程: {total_positive}个 ({total_positive/test_cases['total_cases']*100:.1f}%)")
    print(f"  - 反向流程: {total_negative}个 ({total_negative/test_cases['total_cases']*100:.1f}%)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL测试用例优化生成器
精确生成300个左右的高质量测试用例
"""

import json
from datetime import datetime

class OptimalBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # BNPL服务商
        self.providers = ["Affirm", "Klarna", "Afterpay"]
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_core_purchase_cases(self):
        """生成核心购买流程测试用例（约120个）"""
        cases = []
        
        # 核心场景组合
        scenarios = [
            ("购物车结算", "添加任意商品一件"),
            ("购物车结算", "添加任意商品多件"),
            ("直接结算", "商品页直接购买"),
            ("商品页结算", "立即购买")
        ]
        
        plans = {
            "Affirm": ["12个月分期"],
            "Klarna": ["Pay in 4分期"],
            "Afterpay": ["4期分期"]
        }
        
        for provider in self.providers:
            for platform in self.platform_users.keys():
                for user_type in self.platform_users[platform]:
                    for scenario, operation in scenarios:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "支付",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": scenario,
                                "operation": f"{operation}进入{scenario}",
                                "action": f"点击结算并使用{provider}先享后付功能",
                                "sub_action": f"选择{plans[provider][0]}方案",
                                "expected_result": f"扣款金额与分期计划一致，{provider}支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"
                            },
                            "provider": provider,
                            "priority": "高"
                        }
                        cases.append(case)
        
        return cases

    def generate_discount_cases(self):
        """生成优惠叠加测试用例（约60个）"""
        cases = []
        
        discounts = ["优惠券", "O币抵扣", "会员价"]
        platforms = ["Web", "H5"]  # 主要平台
        
        for provider in self.providers:
            for platform in platforms:
                for user_type in ["会员", "小B会员"]:
                    for discount in discounts:
                        if discount == "优惠码" and user_type == "小B会员":
                            continue
                        
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "优惠叠加支付",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": "优惠商品结算",
                                "operation": f"选择参与{discount}活动的商品",
                                "action": f"应用{discount}后使用{provider}支付",
                                "sub_action": "选择分期方案并确认",
                                "expected_result": f"{discount}优惠正确应用，BNPL分期基于优惠后价格计算，{provider}支付流程正常，订单总金额计算正确，优惠明细清晰显示"
                            },
                            "provider": provider,
                            "discount_type": discount,
                            "priority": "中"
                        }
                        cases.append(case)
        
        return cases

    def generate_refund_cases(self):
        """生成退款场景测试用例（约48个）"""
        cases = []
        
        timings = ["订单确认后立即退款", "发货前退款", "发货后退款", "收货后退款"]
        refund_types = ["全额退款", "部分退款"]
        
        for provider in self.providers:
            for timing in timings:
                for refund_type in refund_types:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "退款处理",
                        "structure": {
                            "platform": "Web",
                            "user_type": "会员",
                            "scenario": f"{timing}场景",
                            "operation": f"对{provider}支付订单发起{refund_type}",
                            "action": "提交退款申请并处理",
                            "sub_action": f"调用{provider}退款接口",
                            "expected_result": f"退款申请成功提交，{provider}接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"
                        },
                        "provider": provider,
                        "refund_timing": timing,
                        "refund_type": refund_type,
                        "priority": "高"
                    }
                    cases.append(case)
        
        return cases

    def generate_exception_cases(self):
        """生成异常场景测试用例（约45个）"""
        cases = []
        
        exceptions = ["网络超时", "服务商维护", "审批拒绝", "重复提交", "支付中断"]
        platforms = ["Web", "H5", "Android"]
        
        for provider in self.providers:
            for exception in exceptions:
                for platform in platforms:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "异常处理",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{exception}异常场景",
                            "operation": f"正常进入{provider}支付流程",
                            "action": f"在关键步骤触发{exception}异常",
                            "sub_action": "观察系统响应和处理",
                            "expected_result": f"系统正确识别{exception}异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"
                        },
                        "provider": provider,
                        "exception_type": exception,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_cross_platform_cases(self):
        """生成跨平台场景测试用例（约12个）"""
        cases = []
        
        cross_scenarios = [
            ("Web下单", "H5支付"),
            ("H5下单", "Web支付"),
            ("App下单", "Web支付"),
            ("Web下单", "App查看")
        ]
        
        for provider in self.providers:
            for order_platform, pay_platform in cross_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "跨平台支付",
                    "structure": {
                        "platform": f"{order_platform}+{pay_platform}",
                        "user_type": "会员",
                        "scenario": "跨平台操作",
                        "operation": f"在{order_platform}创建订单",
                        "action": f"切换到{pay_platform}使用{provider}支付",
                        "sub_action": "完成跨平台支付流程",
                        "expected_result": f"跨平台订单信息同步正确，{provider}支付流程正常，订单状态在各平台一致显示，用户体验流畅"
                    },
                    "provider": provider,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_boundary_cases(self):
        """生成边界值测试用例（约18个）"""
        cases = []
        
        boundary_scenarios = [
            ("最小金额", "0.01美元"),
            ("最大金额", "5000美元"),
            ("商品数量边界", "1件商品"),
            ("商品数量边界", "50件商品"),
            ("分期期数边界", "最短分期"),
            ("分期期数边界", "最长分期")
        ]
        
        for provider in self.providers:
            for scenario_name, scenario_value in boundary_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "边界值测试",
                    "structure": {
                        "platform": "Web",
                        "user_type": "会员",
                        "scenario": f"{scenario_name}测试",
                        "operation": f"使用{scenario_value}进行{provider}支付",
                        "action": "验证边界值处理",
                        "sub_action": "确认系统边界值响应",
                        "expected_result": f"系统正确处理{scenario_value}边界值，{provider}支付流程正常，边界值验证通过，错误提示准确"
                    },
                    "provider": provider,
                    "boundary_type": scenario_name,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v3.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL购买流程测试",
                "description": "验证不同平台、用户、场景下的BNPL支付完整流程",
                "test_cases": self.generate_core_purchase_cases()
            },
            {
                "module_name": "BNPL优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的叠加使用场景",
                "test_cases": self.generate_discount_cases()
            },
            {
                "module_name": "BNPL退款场景测试",
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": self.generate_refund_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_exception_cases()
            },
            {
                "module_name": "BNPL跨平台测试",
                "description": "验证跨平台操作的数据同步和用户体验",
                "test_cases": self.generate_cross_platform_cases()
            },
            {
                "module_name": "BNPL边界值测试",
                "description": "验证各种边界条件下的系统处理",
                "test_cases": self.generate_boundary_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = OptimalBNPLGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_optimal_300_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL优化测试用例已生成: bnpl_optimal_300_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例")

if __name__ == "__main__":
    main()

# 测试用例编写规范

## 用例命名规范

### 新版层级结构格式（参考BNPL先享后付_全面测试用例_v2.0.txt）
**采用树状层级结构，详细展开每个测试维度**

### 层级结构示例
```
BNPL_PURCHASE_001: 支付
├── H5
    ├── 游客
        ├── 购物车结算
            ├── 添加任意商品多件进入购物车结算
                ├── 点击结算并使用BNPL先享后付功能
                    └── 扣款金额与分期计划一致Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
            ├── 添加任意商品一件进入购物车结算
                ├── 点击结算并使用BNPL先享后付功能
                    └── 扣款金额与分期计划一致...
        └── 直接结算
    ├── 小B会员
    └── 会员
```

### 层级说明
- **第1层**: 用例ID和主功能模块
- **第2层**: 平台端口（Android/iOS/Web/H5）
- **第3层**: 用户类型（游客/会员/小B会员）
- **第4层**: 操作场景（购物车结算/直接结算/商品页结算等）
- **第5层**: 具体操作步骤
- **第6层**: 详细子步骤
- **第7层**: 预期结果（包含所有验证点）

## 用例结构要求

### 1. 用例标题
- **格式**: 简洁明了，包含关键信息
- **要素**: 功能+平台+场景+结果
- **长度**: 建议不超过50字符

### 2. 基本信息
- **用例ID**: 按模块编号，如BNPL_001
- **优先级**: 高/中/低
- **测试类型**: 功能测试/接口测试/异常测试
- **执行方式**: 手工/自动化

### 3. 前置条件
- **环境准备**: 测试环境、数据库状态
- **账号准备**: 用户类型、权限状态
- **数据准备**: 商品、活动、配置等
- **系统状态**: 服务状态、接口可用性

### 4. 测试步骤
- **步骤编号**: 1、2、3...
- **操作描述**: 具体的操作动作
- **输入数据**: 明确的输入值
- **检查点**: 每步的验证要点

### 5. 预期结果
- **界面表现**: UI显示、交互反馈
- **数据变化**: 数据库、状态变更
- **系统行为**: 接口调用、日志记录
- **用户体验**: 流程顺畅性

## BNPL用例特殊要求

### 支付流程用例
```
标题: 支付-H5-Affirm支付-正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致

前置条件:
1. H5环境正常，Affirm服务可用
2. 会员账号已登录，信用状态良好
3. 购物车中有可支付商品
4. Affirm分期服务已配置

测试步骤:
1. 进入结账页面，选择Affirm支付方式
2. 选择分期方案（如12个月分期）
3. 确认分期信息和费用明细
4. 提交Affirm支付申请
5. 完成Affirm审批流程
6. 确认支付成功

预期结果:
1. Affirm支付选项正常显示
2. 分期方案清晰展示，费用透明
3. 审批流程顺畅，无异常中断
4. 支付成功后订单状态更新为已支付
5. 用户收到支付确认和分期计划通知
6. 扣款金额与分期计划一致
```

### 退款流程用例
```
标题: 退款-Web-Klarna-发货后部分退款，分期计划正确调整

前置条件:
1. 存在Klarna支付的已发货订单
2. 订单支持部分退款
3. Klarna退款接口正常
4. 管理员有退款权限

测试步骤:
1. 在订单管理系统中找到目标订单
2. 发起部分退款申请（退款50%金额）
3. 填写退款原因和金额
4. 提交退款申请
5. 系统调用Klarna退款接口
6. 验证分期计划调整

预期结果:
1. 退款申请成功提交
2. Klarna接口调用成功
3. 订单状态更新为部分退款
4. 分期计划按比例调整
5. 用户收到退款通知
6. 退款金额正确计算
```

## 平台差异化测试

### APP端特殊要求
- **用户限制**: 仅支持会员和小B会员
- **界面适配**: 移动端UI适配
- **性能要求**: 加载速度、响应时间
- **推送通知**: 支付状态推送

### Web端特殊要求
- **浏览器兼容**: Chrome、Firefox、Safari、Edge
- **响应式设计**: 不同屏幕尺寸适配
- **游客支持**: 游客用户的BNPL流程

### H5端特殊要求
- **移动浏览器**: 微信、Safari、Chrome移动版
- **触屏操作**: 手势、滑动、点击
- **网络适应**: 4G、WiFi环境切换

## 异常场景测试

### 网络异常
- **超时处理**: 请求超时的处理机制
- **断网恢复**: 网络中断后的恢复处理
- **弱网环境**: 网络不稳定时的用户体验

### 服务商异常
- **服务维护**: BNPL服务商系统维护
- **接口异常**: API返回错误的处理
- **审批拒绝**: 信用审批失败的处理

### 用户操作异常
- **重复提交**: 防止重复支付
- **中途退出**: 支付过程中退出的处理
- **浏览器关闭**: 意外关闭的数据保护

## 数据验证要点

### 金额计算
- **分期金额**: 总金额与分期金额的一致性
- **手续费**: 分期手续费的正确计算
- **汇率转换**: 不同货币的汇率处理
- **优惠叠加**: 优惠后金额的分期计算

### 状态同步
- **订单状态**: 电商平台与BNPL服务商状态同步
- **支付状态**: 实时状态更新
- **退款状态**: 退款处理状态跟踪

### 数据一致性
- **用户信息**: 用户数据在各系统间的一致性
- **商品信息**: 商品价格、库存的准确性
- **交易记录**: 交易流水的完整性

## 性能测试要求

### 响应时间
- **支付页面加载**: 3秒内完成
- **BNPL审批**: 30秒内返回结果
- **支付确认**: 10秒内完成确认

### 并发处理
- **同时支付**: 多用户同时使用BNPL
- **秒杀场景**: 高并发下的BNPL支付
- **系统稳定性**: 长时间运行的稳定性

## 安全测试要点

### 数据安全
- **敏感信息**: 用户信息的加密传输
- **支付数据**: 支付信息的安全处理
- **日志记录**: 敏感数据的脱敏处理

### 接口安全
- **身份验证**: API调用的身份验证
- **数据校验**: 输入数据的合法性校验
- **防重放**: 防止请求重放攻击

## 兼容性测试

### 浏览器兼容
- **主流浏览器**: Chrome、Firefox、Safari、Edge
- **版本兼容**: 近3个版本的兼容性
- **移动浏览器**: 移动端浏览器的兼容性

### 设备兼容
- **屏幕尺寸**: 不同分辨率的适配
- **操作系统**: iOS、Android版本兼容
- **设备性能**: 低配置设备的性能表现

## 用例评审标准

### 完整性检查
- **场景覆盖**: 是否覆盖主要业务场景
- **边界条件**: 是否包含边界值测试
- **异常处理**: 是否考虑异常情况

### 可执行性检查
- **步骤清晰**: 操作步骤是否明确
- **数据准备**: 测试数据是否充分
- **环境要求**: 环境配置是否明确

### 可维护性检查
- **用例独立**: 用例间是否相互独立
- **数据隔离**: 测试数据是否隔离
- **结果验证**: 验证点是否明确

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL分期测试用例生成器
专门针对三种BNPL支付方式的分期区间测试
"""

import json
from datetime import datetime

def generate_bnpl_installment_cases():
    """生成BNPL分期测试用例"""
    
    # 基础配置
    platforms = ["Android", "iOS", "Web", "H5"]
    user_types = {
        "Android": ["会员", "小B会员"],  # APP端不支持游客
        "iOS": ["会员", "小B会员"],     # APP端不支持游客
        "Web": ["游客", "会员", "小B会员"],
        "H5": ["游客", "会员", "小B会员"]
    }
    
    # BNPL分期区间定义
    bnpl_ranges = {
        "Affirm": {"min": 50.00, "max": 30000.00},
        "Afterpay": {"min": 1.00, "max": 4000.00},
        "<PERSON>larna": {"min": 0.50, "max": 999999.99}
    }
    
    # 测试金额等价类
    test_amounts = {
        "边界值下限": [0.49, 0.50, 0.51, 0.99, 1.00, 1.01, 49.99, 50.00, 50.01],
        "边界值上限": [3999.99, 4000.00, 4000.01, 29999.99, 30000.00, 30000.01, 999999.98, 999999.99, 1000000.00],
        "典型值": [10.00, 100.00, 500.00, 1000.00, 2000.00, 5000.00, 10000.00, 50000.00, 100000.00],
        "优惠后金额": [49.50, 99.50, 999.50, 3999.50, 29999.50]  # 模拟优惠后的金额
    }
    
    # 优惠类型（基于傲雷实际业务）
    discount_types = ["O币抵扣", "优惠券", "优惠码", "限时折扣", "自由捆绑", "会员折扣", "小B会员折扣"]
    
    # 商品类型（基于傲雷实际业务，扩展版）
    product_types = ["单品", "多品", "自由捆绑商品"]
    
    # 购买场景（基于傲雷实际业务，扩展版）
    purchase_scenarios = ["购物车结算", "直接购买", "待支付订单转BNPL"]
    
    # 退款时机
    refund_timings = ["订单确认后", "发货前", "发货后", "收货后"]
    
    # 退款类型
    refund_types = ["全额退款", "部分退款"]
    
    test_cases = []
    case_id = 1
    
    # 1. BNPL基础下单流程测试
    for platform in platforms:
        for user_type in user_types[platform]:
            for scenario in purchase_scenarios:
                for product_type in product_types:
                    case = {
                        "id": f"BNPL_{case_id:03d}",
                        "name": "BNPL分期下单流程",
                        "level2_platform": platform,
                        "level3_user": user_type,
                        "level4_scenario": scenario,
                        "level5_operation": f"添加{product_type}商品到{scenario}",
                        "level6_step": "选择BNPL先享后付分期支付",
                        "level7_substep": "确认分期方案并完成支付",
                        "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知",
                        "test_data": {
                            "currency": "USD",
                            "available_bnpl": ["Affirm", "Afterpay", "Klarna"],
                            "product_type": product_type,
                            "scenario_type": "normal_purchase"
                        }
                    }
                    test_cases.append(case)
                    case_id += 1
    
    return test_cases

def generate_boundary_value_cases():
    """生成边界值测试用例"""
    test_cases = []
    case_id = 200  # 从200开始编号
    
    # BNPL分期区间
    bnpl_ranges = {
        "Affirm": {"min": 50.00, "max": 30000.00},
        "Afterpay": {"min": 1.00, "max": 4000.00},
        "Klarna": {"min": 0.50, "max": 999999.99}
    }
    
    # 边界值测试金额
    boundary_amounts = [
        # Klarna下边界
        {"amount": 0.49, "expected_bnpl": [], "description": "低于所有BNPL最小值"},
        {"amount": 0.50, "expected_bnpl": ["Klarna"], "description": "Klarna最小值"},
        {"amount": 0.99, "expected_bnpl": ["Klarna"], "description": "Afterpay最小值下限"},
        {"amount": 1.00, "expected_bnpl": ["Klarna", "Afterpay"], "description": "Afterpay最小值"},
        
        # Affirm下边界
        {"amount": 49.99, "expected_bnpl": ["Klarna", "Afterpay"], "description": "Affirm最小值下限"},
        {"amount": 50.00, "expected_bnpl": ["Klarna", "Afterpay", "Affirm"], "description": "全支持区间开始"},
        
        # Afterpay上边界
        {"amount": 4000.00, "expected_bnpl": ["Klarna", "Afterpay", "Affirm"], "description": "Afterpay最大值"},
        {"amount": 4000.01, "expected_bnpl": ["Klarna", "Affirm"], "description": "Afterpay最大值上限"},
        
        # Affirm上边界
        {"amount": 30000.00, "expected_bnpl": ["Klarna", "Affirm"], "description": "Affirm最大值"},
        {"amount": 30000.01, "expected_bnpl": ["Klarna"], "description": "仅Klarna支持"},
        
        # Klarna上边界
        {"amount": 999999.99, "expected_bnpl": ["Klarna"], "description": "Klarna最大值"},
        {"amount": 1000000.00, "expected_bnpl": [], "description": "超出所有BNPL最大值"}
    ]
    
    platforms = ["Web", "H5"]  # 选择支持游客的平台进行边界值测试
    
    for platform in platforms:
        for boundary in boundary_amounts:
            case = {
                "id": f"BNPL_{case_id:03d}",
                "name": "BNPL分期金额边界值测试",
                "level2_platform": platform,
                "level3_user": "游客",
                "level4_scenario": "购物车结算",
                "level5_operation": f"添加商品到购物车(总价US${boundary['amount']})",
                "level6_step": "选择BNPL先享后付支付方式",
                "level7_substep": f"验证可用BNPL选项({boundary['description']})",
                "level8_expected": f"系统正确显示可用BNPL选项: {boundary['expected_bnpl'] if boundary['expected_bnpl'] else '无可用选项'}",
                "test_data": {
                    "amount": boundary['amount'],
                    "currency": "USD",
                    "expected_bnpl": boundary['expected_bnpl'],
                    "description": boundary['description']
                }
            }
            test_cases.append(case)
            case_id += 1
    
    return test_cases

def generate_discount_combination_cases():
    """生成优惠叠加测试用例"""
    test_cases = []
    case_id = 300  # 从300开始编号
    
    # 优惠场景定义（基于傲雷实际业务）
    discount_scenarios = [
        # 普通优惠测试（不指定具体金额）
        {"type": "O币抵扣"},
        {"type": "优惠券"},
        {"type": "优惠码"},
        {"type": "限时折扣"},
        {"type": "会员折扣"},
        {"type": "小B会员折扣"},
        {"type": "自由捆绑"},

        # 边界值优惠场景（需要指定金额验证边界）
        {"type": "优惠券", "original": 51.00, "discount": 1.00, "final": 50.00, "note": "优惠后刚好达到Affirm最小值", "is_boundary": True},
        {"type": "O币抵扣", "original": 4001.00, "discount": 1.00, "final": 4000.00, "note": "优惠后刚好达到Afterpay最大值", "is_boundary": True},
        {"type": "会员折扣", "original": 1.50, "discount": 1.00, "final": 0.50, "note": "优惠后刚好达到Klarna最小值", "is_boundary": True},

        # 多重优惠叠加（不指定具体金额）
        {"type": "O币+优惠券"},
        {"type": "优惠码+限时折扣"},
        {"type": "会员折扣+限时折扣"}
    ]
    
    platforms = ["Web", "H5", "Android", "iOS"]
    user_mapping = {
        "O币抵扣": ["会员", "小B会员"],
        "优惠券": ["游客", "会员", "小B会员"],
        "优惠码": ["游客", "会员", "小B会员"],
        "限时折扣": ["游客", "会员", "小B会员"],
        "会员折扣": ["会员"],
        "小B会员折扣": ["小B会员"],
        "自由捆绑": ["游客", "会员", "小B会员"],
        "O币+优惠券": ["会员", "小B会员"],
        "优惠码+限时折扣": ["游客", "会员", "小B会员"],
        "会员折扣+限时折扣": ["会员"]
    }
    
    for platform in platforms:
        for scenario in discount_scenarios:
            applicable_users = user_mapping.get(scenario['type'], ["游客", "会员", "小B会员"])
            
            # 过滤APP端不支持游客的情况
            if platform in ["Android", "iOS"] and "游客" in applicable_users:
                applicable_users = [u for u in applicable_users if u != "游客"]
            
            for user_type in applicable_users:
                # 判断是否为边界值测试
                is_boundary = scenario.get('is_boundary', False)

                if is_boundary:
                    # 边界值测试需要指定金额
                    case = {
                        "id": f"BNPL_{case_id:03d}",
                        "name": "BNPL分期优惠叠加边界值测试",
                        "level2_platform": platform,
                        "level3_user": user_type,
                        "level4_scenario": "购物车结算",
                        "level5_operation": f"添加商品并应用{scenario['type']}优惠",
                        "level6_step": "选择BNPL先享后付分期支付",
                        "level7_substep": f"验证边界值：最终价格US${scenario['final']}的分期方案",
                        "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过",
                        "test_data": {
                            "original_amount": scenario['original'],
                            "discount_amount": scenario['discount'],
                            "final_amount": scenario['final'],
                            "discount_type": scenario['type'],
                            "note": scenario.get('note', ''),
                            "scenario_type": "boundary_discount_test"
                        }
                    }
                else:
                    # 普通优惠测试不指定金额
                    case = {
                        "id": f"BNPL_{case_id:03d}",
                        "name": "BNPL分期优惠叠加测试",
                        "level2_platform": platform,
                        "level3_user": user_type,
                        "level4_scenario": "购物车结算",
                        "level5_operation": f"添加商品并应用{scenario['type']}优惠",
                        "level6_step": "选择BNPL先享后付分期支付",
                        "level7_substep": "确认基于折后价的分期方案",
                        "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误",
                        "test_data": {
                            "discount_type": scenario['type'],
                            "scenario_type": "normal_discount_test"
                        }
                    }
                test_cases.append(case)
                case_id += 1
    
    return test_cases

def generate_refund_cases():
    """生成退款测试用例"""
    test_cases = []
    case_id = 400  # 从400开始编号

    # 退款场景定义（基于傲雷实际业务，不指定具体金额）
    refund_scenarios = [
        {"timing": "订单确认后", "type": "全额退款"},
        {"timing": "订单确认后", "type": "部分退款"},
        {"timing": "发货前", "type": "全额退款"},
        {"timing": "发货前", "type": "部分退款"},
        {"timing": "发货后", "type": "全额退款"},
        {"timing": "发货后", "type": "部分退款"},
        {"timing": "收货后", "type": "全额退款"},
        {"timing": "收货后", "type": "部分退款"}
    ]

    platforms = ["Web", "H5"]  # 选择主要平台进行退款测试
    user_types = ["会员", "小B会员"]  # 选择主要用户类型

    for platform in platforms:
        for user_type in user_types:
            for scenario in refund_scenarios:
                case = {
                    "id": f"BNPL_{case_id:03d}",
                    "name": "BNPL分期退款处理",
                    "level2_platform": platform,
                    "level3_user": user_type,
                    "level4_scenario": "退款处理",
                    "level5_operation": f"BNPL订单{scenario['timing']}申请退款",
                    "level6_step": f"处理{scenario['type']}退款申请",
                    "level7_substep": "调整分期计划并完成退款",
                    "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确",
                    "test_data": {
                        "refund_timing": scenario['timing'],
                        "refund_type": scenario['type'],
                        "scenario_type": "refund_test"
                    }
                }
                test_cases.append(case)
                case_id += 1

    return test_cases

def save_test_cases_to_json(test_cases, filename):
    """保存测试用例到JSON文件"""
    # 按照V5.0格式组织数据
    organized_data = {
        "metadata": {
            "title": "BNPL先享后付分期测试用例",
            "version": "V10.0",
            "create_date": datetime.now().strftime("%Y-%m-%d"),
            "description": "BNPL三种支付方式分期区间测试，包含下单、退款、优惠叠加等场景",
            "total_cases": len(test_cases),
            "bnpl_providers": ["Affirm", "Afterpay", "Klarna"],
            "test_ranges": {
                "Affirm": "US$50.00 - US$30,000.00",
                "Afterpay": "US$1.00 - US$4,000.00",
                "Klarna": "US$0.50 - US$999,999.99"
            }
        },
        "test_modules": {
            "BNPL分期下单流程测试": [],
            "BNPL分期金额边界值测试": [],
            "BNPL分期优惠叠加测试": [],
            "BNPL分期退款处理测试": [],
            "BNPL额外业务场景测试": []
        },
        "all_cases": test_cases
    }

    # 按模块分类测试用例
    for case in test_cases:
        if "下单流程" in case['name']:
            organized_data["test_modules"]["BNPL分期下单流程测试"].append(case)
        elif "边界值" in case['name']:
            organized_data["test_modules"]["BNPL分期金额边界值测试"].append(case)
        elif "优惠叠加" in case['name']:
            organized_data["test_modules"]["BNPL分期优惠叠加测试"].append(case)
        elif "退款" in case['name']:
            organized_data["test_modules"]["BNPL分期退款处理测试"].append(case)
        else:
            # 其他业务场景
            organized_data["test_modules"]["BNPL额外业务场景测试"].append(case)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(organized_data, f, ensure_ascii=False, indent=2)

    return organized_data

def generate_additional_business_cases():
    """生成额外的业务场景测试用例"""
    test_cases = []
    case_id = 500  # 从500开始编号

    platforms = ["Web", "H5", "Android", "iOS"]
    user_types = {
        "Android": ["会员", "小B会员"],
        "iOS": ["会员", "小B会员"],
        "Web": ["游客", "会员", "小B会员"],
        "H5": ["游客", "会员", "小B会员"]
    }

    # 额外业务场景
    additional_scenarios = [
        {
            "name": "BNPL支付失败重试",
            "operation": "BNPL支付失败后重新选择分期方案",
            "step": "处理支付失败并重新选择BNPL分期",
            "substep": "重新确认分期方案并完成支付",
            "expected": "支付失败处理正确，重试流程正常，最终支付成功"
        },
        {
            "name": "BNPL支付中断恢复",
            "operation": "BNPL支付过程中断后恢复支付",
            "step": "从中断点恢复BNPL分期支付流程",
            "substep": "确认之前的分期选择并完成支付",
            "expected": "支付中断恢复正常，订单状态正确更新"
        },
        {
            "name": "BNPL额度不足处理",
            "operation": "BNPL分期额度不足时的处理",
            "step": "提示额度不足并引导选择其他支付方式",
            "substep": "显示可用额度并提供替代方案",
            "expected": "额度不足提示清晰，替代方案引导正确"
        },
        {
            "name": "BNPL分期方案变更",
            "operation": "支付前变更BNPL分期方案",
            "step": "修改已选择的分期方案",
            "substep": "确认新的分期方案并完成支付",
            "expected": "分期方案变更成功，费用计算正确"
        }
    ]

    for platform in platforms:
        for user_type in user_types[platform]:
            for scenario in additional_scenarios:
                case = {
                    "id": f"BNPL_{case_id:03d}",
                    "name": scenario["name"],
                    "level2_platform": platform,
                    "level3_user": user_type,
                    "level4_scenario": "异常场景处理",
                    "level5_operation": scenario["operation"],
                    "level6_step": scenario["step"],
                    "level7_substep": scenario["substep"],
                    "level8_expected": scenario["expected"],
                    "test_data": {
                        "scenario_type": "additional_business_case"
                    }
                }
                test_cases.append(case)
                case_id += 1

    return test_cases

if __name__ == "__main__":
    # 生成所有测试用例
    print("开始生成BNPL分期测试用例...")

    basic_cases = generate_bnpl_installment_cases()
    print(f"✓ 基础下单流程测试用例: {len(basic_cases)}个")

    boundary_cases = generate_boundary_value_cases()
    print(f"✓ 边界值测试用例: {len(boundary_cases)}个")

    discount_cases = generate_discount_combination_cases()
    print(f"✓ 优惠叠加测试用例: {len(discount_cases)}个")

    refund_cases = generate_refund_cases()
    print(f"✓ 退款处理测试用例: {len(refund_cases)}个")

    additional_cases = generate_additional_business_cases()
    print(f"✓ 额外业务场景测试用例: {len(additional_cases)}个")

    all_cases = basic_cases + boundary_cases + discount_cases + refund_cases + additional_cases

    print(f"\n📊 测试用例生成完成:")
    print(f"总计: {len(all_cases)}个测试用例")
    print(f"- 基础下单流程: {len(basic_cases)}个")
    print(f"- 边界值测试: {len(boundary_cases)}个")
    print(f"- 优惠叠加测试: {len(discount_cases)}个")
    print(f"- 退款处理测试: {len(refund_cases)}个")
    print(f"- 额外业务场景: {len(additional_cases)}个")

    # 保存到JSON文件
    filename = "bnpl_installment_test_cases.json"
    organized_data = save_test_cases_to_json(all_cases, filename)
    print(f"\n💾 测试用例已保存到: {filename}")

    # 显示统计信息
    print(f"\n📈 测试覆盖统计:")
    print(f"- 平台覆盖: Android, iOS, Web, H5")
    print(f"- 用户类型: 游客, 会员, 小B会员")
    print(f"- BNPL方式: Affirm, Afterpay, Klarna")
    print(f"- 金额范围: US$0.49 - US$1,000,000.00")
    print(f"- 优惠类型: 6种优惠组合")
    print(f"- 退款场景: 8种退款时机和类型")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终XMind文件生成器
生成完整的BNPL测试用例XMind文件
"""

import json
import os
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime
import uuid

class FinalXMindGenerator:
    def __init__(self, json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)
        self.topic_id_counter = 1
    
    def generate_topic_id(self):
        """生成唯一的主题ID"""
        topic_id = f"topic_{self.topic_id_counter}"
        self.topic_id_counter += 1
        return topic_id
    
    def create_xmind_content_xml(self):
        """创建XMind内容XML"""
        # 创建根元素
        xmap = ET.Element("xmap-content", xmlns="urn:xmind:xmap:xmlns:content:2.0")
        xmap.set("xmlns:fo", "http://www.w3.org/1999/XSL/Format")
        
        # 创建工作表
        sheet = ET.SubElement(xmap, "sheet", id="sheet1", theme="theme1")
        sheet_title = ET.SubElement(sheet, "title")
        sheet_title.text = "BNPL测试用例"
        
        # 创建根主题
        topic = ET.SubElement(sheet, "topic", id=self.generate_topic_id())
        title = ET.SubElement(topic, "title")
        title.text = self.test_data["title"]
        
        # 创建子主题容器
        children = ET.SubElement(topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        # 添加版本信息
        self.add_version_info(topics)
        
        # 添加测试模块
        for module in self.test_data["test_modules"]:
            self.add_module_topic(topics, module)
        
        # 添加测试总结
        self.add_summary_topic(topics)
        
        return xmap
    
    def add_version_info(self, parent):
        """添加版本信息主题"""
        version_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(version_topic, "title")
        title.text = f"版本信息 {self.test_data['version']}"
        
        children = ET.SubElement(version_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        version_details = [
            f"创建日期: {self.test_data['created_date']}",
            "测试范围: Affirm、Klarna、Afterpay",
            "测试类型: 功能测试、异常测试",
            "覆盖平台: Android、iOS、Web、H5"
        ]
        
        for detail in version_details:
            detail_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
            detail_title = ET.SubElement(detail_topic, "title")
            detail_title.text = detail
    
    def add_module_topic(self, parent, module):
        """添加测试模块主题"""
        module_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(module_topic, "title")
        title.text = module["module_name"]
        
        children = ET.SubElement(module_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        # 添加模块描述
        desc_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        desc_title = ET.SubElement(desc_topic, "title")
        desc_title.text = f"模块描述: {module['description']}"
        
        # 添加用例数量
        count_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        count_title = ET.SubElement(count_topic, "title")
        count_title.text = f"用例数量: {len(module['test_cases'])}个"
        
        # 按优先级分组
        high_priority_cases = [case for case in module["test_cases"] if case["priority"] == "高"]
        medium_priority_cases = [case for case in module["test_cases"] if case["priority"] == "中"]
        
        if high_priority_cases:
            self.add_priority_group(topics, "高优先级用例", high_priority_cases)
        
        if medium_priority_cases:
            self.add_priority_group(topics, "中优先级用例", medium_priority_cases)
    
    def add_priority_group(self, parent, group_name, cases):
        """添加优先级分组"""
        group_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(group_topic, "title")
        title.text = group_name
        
        children = ET.SubElement(group_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        for case in cases:
            self.add_test_case(topics, case)
    
    def add_test_case(self, parent, case):
        """添加测试用例"""
        case_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(case_topic, "title")
        title.text = f"{case['case_id']}: {case['case_name']}"
        
        children = ET.SubElement(case_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        # 基本信息
        basic_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        basic_title = ET.SubElement(basic_topic, "title")
        basic_title.text = "基本信息"
        
        basic_children = ET.SubElement(basic_topic, "children")
        basic_topics = ET.SubElement(basic_children, "topics", type="attached")
        
        # 优先级
        priority_topic = ET.SubElement(basic_topics, "topic", id=self.generate_topic_id())
        priority_title = ET.SubElement(priority_topic, "title")
        priority_title.text = f"优先级: {case['priority']}"
        
        # 测试类型
        type_topic = ET.SubElement(basic_topics, "topic", id=self.generate_topic_id())
        type_title = ET.SubElement(type_topic, "title")
        type_title.text = f"测试类型: {case['test_type']}"
        
        # 前置条件
        if case["preconditions"]:
            self.add_list_section(topics, "前置条件", case["preconditions"])
        
        # 测试步骤
        if case["test_steps"]:
            self.add_list_section(topics, "测试步骤", case["test_steps"])
        
        # 预期结果
        if case["expected_results"]:
            self.add_list_section(topics, "预期结果", case["expected_results"])
        
        # 测试数据
        if case.get("test_data"):
            test_data_list = [f"{k}: {v}" for k, v in case["test_data"].items()]
            self.add_list_section(topics, "测试数据", test_data_list)
    
    def add_list_section(self, parent, section_name, items):
        """添加列表类型的章节"""
        section_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(section_topic, "title")
        title.text = section_name
        
        children = ET.SubElement(section_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        for item in items:
            item_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
            item_title = ET.SubElement(item_topic, "title")
            item_title.text = item
    
    def add_summary_topic(self, parent):
        """添加测试总结主题"""
        summary_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(summary_topic, "title")
        title.text = "测试总结"
        
        children = ET.SubElement(summary_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        total_cases = sum(len(module["test_cases"]) for module in self.test_data["test_modules"])
        
        # 总用例数
        total_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        total_title = ET.SubElement(total_topic, "title")
        total_title.text = f"总用例数: {total_cases}个"
        
        # 测试模块数
        modules_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        modules_title = ET.SubElement(modules_topic, "title")
        modules_title.text = f"测试模块: {len(self.test_data['test_modules'])}个"
        
        # 覆盖范围
        coverage_items = [
            "BNPL服务商: Affirm、Klarna、Afterpay",
            "用户类型: 游客、普通会员、小B会员",
            "平台端口: Android、iOS、Web、H5",
            "测试场景: 购买流程、退款处理、优惠叠加、异常处理"
        ]
        self.add_list_section(topics, "覆盖范围", coverage_items)
        
        # 测试重点
        focus_items = [
            "支付流程完整性验证",
            "订单状态正确性验证",
            "退款处理准确性验证",
            "优惠计算正确性验证",
            "异常情况处理验证"
        ]
        self.add_list_section(topics, "测试重点", focus_items)
    
    def create_manifest_xml(self):
        """创建manifest.xml"""
        manifest = ET.Element("manifest", xmlns="urn:xmind:xmap:xmlns:manifest:1.0")
        
        file_entry = ET.SubElement(manifest, "file-entry", 
                                 **{"full-path": "content.xml", 
                                    "media-type": "text/xml"})
        
        file_entry2 = ET.SubElement(manifest, "file-entry",
                                  **{"full-path": "META-INF/",
                                     "media-type": ""})
        
        file_entry3 = ET.SubElement(manifest, "file-entry",
                                  **{"full-path": "meta.xml",
                                     "media-type": "text/xml"})
        
        return manifest
    
    def create_meta_xml(self):
        """创建meta.xml"""
        meta = ET.Element("meta", xmlns="urn:xmind:xmap:xmlns:meta:2.0")
        
        # 创建者
        creator = ET.SubElement(meta, "Creator")
        creator.text = "BNPL测试用例生成器"
        
        # 创建时间
        created = ET.SubElement(meta, "Created")
        created.text = datetime.now().isoformat()
        
        # 版本
        version = ET.SubElement(meta, "Version")
        version.text = "1.0"
        
        return meta
    
    def generate_xmind_file(self, output_file):
        """生成XMind文件"""
        # 创建临时目录
        temp_dir = "temp_xmind"
        os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(os.path.join(temp_dir, "META-INF"), exist_ok=True)
        
        try:
            # 生成content.xml
            content_xml = self.create_xmind_content_xml()
            content_tree = ET.ElementTree(content_xml)
            content_tree.write(os.path.join(temp_dir, "content.xml"), 
                             encoding="utf-8", xml_declaration=True)
            
            # 生成manifest.xml
            manifest_xml = self.create_manifest_xml()
            manifest_tree = ET.ElementTree(manifest_xml)
            manifest_tree.write(os.path.join(temp_dir, "META-INF", "manifest.xml"),
                              encoding="utf-8", xml_declaration=True)
            
            # 生成meta.xml
            meta_xml = self.create_meta_xml()
            meta_tree = ET.ElementTree(meta_xml)
            meta_tree.write(os.path.join(temp_dir, "meta.xml"),
                          encoding="utf-8", xml_declaration=True)
            
            # 创建XMind文件（实际上是ZIP文件）
            with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arc_name)
            
            return output_file
            
        finally:
            # 清理临时文件
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

def main():
    """主函数"""
    json_file = "bnpl_test_cases_v2.json"
    output_file = "../输出用例/BNPL先享后付_全面测试用例_v2.0.xmind"
    
    if not os.path.exists(json_file):
        print(f"找不到输入文件: {json_file}")
        return
    
    try:
        generator = FinalXMindGenerator(json_file)
        result_file = generator.generate_xmind_file(output_file)
        print(f"✅ XMind文件已成功生成: {result_file}")
        print("\n📊 文件统计:")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_cases = 0
        for module in data["test_modules"]:
            module_name = module["module_name"]
            case_count = len(module["test_cases"])
            total_cases += case_count
            print(f"  - {module_name}: {case_count}个用例")
        
        print(f"\n🎯 总计: {total_cases}个测试用例")
        print("📋 覆盖内容:")
        print("  - 3个BNPL服务商: Affirm、Klarna、Afterpay")
        print("  - 3种用户类型: 游客、普通会员、小B会员")
        print("  - 4个平台端口: Android、iOS、Web、H5")
        print("  - 4类测试场景: 购买流程、退款处理、优惠叠加、异常处理")
        
    except Exception as e:
        print(f"❌ 生成XMind文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

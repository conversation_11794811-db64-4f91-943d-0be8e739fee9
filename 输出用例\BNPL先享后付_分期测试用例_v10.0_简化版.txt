BNPL先享后付分期测试用例
==================================================

版本: V10.0
创建日期: 2025-07-16
测试用例总数: 216个
BNPL服务商: Affirm, Afterpay, Klarna

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试模块分布:
- BNPL分期下单流程测试: 60个用例
- BNPL分期金额边界值测试: 46个用例
- BNPL分期优惠叠加测试: 78个用例
- BNPL分期退款处理测试: 32个用例

详细测试用例:
--------------------------------------------------

BNPL分期下单流程测试
============

Android平台:
  会员:
    BNPL_001: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_002: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_003: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  小B会员:
    BNPL_007: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_008: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_009: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例


iOS平台:
  会员:
    BNPL_013: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_014: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_015: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  小B会员:
    BNPL_019: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_020: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_021: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例


Web平台:
  游客:
    BNPL_025: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_026: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_027: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  会员:
    BNPL_031: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_032: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_033: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  小B会员:
    BNPL_037: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_038: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_039: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例


H5平台:
  游客:
    BNPL_043: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_044: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_045: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  会员:
    BNPL_049: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_050: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_051: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例

  小B会员:
    BNPL_055: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_056: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_057: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付并确认支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    ... 还有3个类似用例


BNPL分期金额边界值测试
=============

Web平台:
  游客:
    BNPL_200: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49

    BNPL_201: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5

    BNPL_202: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99

    ... 还有10个类似用例

  会员:
    BNPL_317: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_319: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_321: 添加商品并应用会员折扣优惠
      步骤: 使用BNPL支付(原价US$1.5, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_318: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_320: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


H5平台:
  游客:
    BNPL_212: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49

    BNPL_213: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5

    BNPL_214: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99

    ... 还有10个类似用例

  会员:
    BNPL_345: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_347: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_349: 添加商品并应用会员折扣优惠
      步骤: 使用BNPL支付(原价US$1.5, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_346: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_348: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


Android平台:
  会员:
    BNPL_368: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_370: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_372: 添加商品并应用会员折扣优惠
      步骤: 使用BNPL支付(原价US$1.5, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_369: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_371: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


iOS平台:
  会员:
    BNPL_390: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_392: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_394: 添加商品并应用会员折扣优惠
      步骤: 使用BNPL支付(原价US$1.5, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_391: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付(原价US$51.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_393: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


BNPL分期优惠叠加测试
============

Web平台:
  会员:
    BNPL_300: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_303: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_306: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有6个类似用例

  小B会员:
    BNPL_301: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_304: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_307: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有5个类似用例

  游客:
    BNPL_302: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_305: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_308: 添加商品并应用限时折扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有2个类似用例


H5平台:
  会员:
    BNPL_328: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_331: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_334: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有6个类似用例

  小B会员:
    BNPL_329: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_332: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_335: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有5个类似用例

  游客:
    BNPL_330: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_333: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_336: 添加商品并应用限时折扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有2个类似用例


Android平台:
  会员:
    BNPL_356: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_358: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_360: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有6个类似用例

  小B会员:
    BNPL_357: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_359: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_361: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有5个类似用例


iOS平台:
  会员:
    BNPL_378: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_380: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_382: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有6个类似用例

  小B会员:
    BNPL_379: 添加商品并应用O币抵扣优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_381: 添加商品并应用优惠券优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_383: 添加商品并应用优惠码优惠
      步骤: 使用BNPL支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    ... 还有5个类似用例


BNPL分期退款处理测试
============

Web平台:
  会员:
    BNPL_400: BNPL订单订单确认后申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_401: BNPL订单订单确认后申请退款
      步骤: 处理部分退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_402: BNPL订单发货前申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    ... 还有5个类似用例

  小B会员:
    BNPL_408: BNPL订单订单确认后申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_409: BNPL订单订单确认后申请退款
      步骤: 处理部分退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_410: BNPL订单发货前申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    ... 还有5个类似用例


H5平台:
  会员:
    BNPL_416: BNPL订单订单确认后申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_417: BNPL订单订单确认后申请退款
      步骤: 处理部分退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_418: BNPL订单发货前申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    ... 还有5个类似用例

  小B会员:
    BNPL_424: BNPL订单订单确认后申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_425: BNPL订单订单确认后申请退款
      步骤: 处理部分退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_426: BNPL订单发货前申请退款
      步骤: 处理全额退款
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    ... 还有5个类似用例

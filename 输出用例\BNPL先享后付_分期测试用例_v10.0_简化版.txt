BNPL先享后付分期测试用例
==================================================

版本: V10.0
创建日期: 2025-07-16
测试用例总数: 286个
BNPL服务商: Affirm, Afterpay, Klarna

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试模块分布:
- BNPL分期下单流程测试: 90个用例
- BNPL分期金额边界值测试: 46个用例
- BNPL分期优惠叠加测试: 78个用例
- BNPL分期退款处理测试: 32个用例
- BNPL额外业务场景测试: 40个用例

详细测试用例:
--------------------------------------------------

BNPL分期下单流程测试
============

Android平台:
  会员:
    BNPL_001: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_002: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_003: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_004: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_005: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_006: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_007: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_008: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_009: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  小B会员:
    BNPL_010: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_011: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_012: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_013: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_014: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_015: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_016: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_017: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_018: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知


iOS平台:
  会员:
    BNPL_019: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_020: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_021: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_022: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_023: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_024: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_025: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_026: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_027: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  小B会员:
    BNPL_028: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_029: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_030: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_031: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_032: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_033: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_034: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_035: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_036: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知


Web平台:
  游客:
    BNPL_037: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_038: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_039: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_040: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_041: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_042: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_043: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_044: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_045: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  会员:
    BNPL_046: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_047: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_048: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_049: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_050: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_051: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_052: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_053: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_054: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  小B会员:
    BNPL_055: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_056: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_057: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_058: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_059: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_060: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_061: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_062: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_063: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知


H5平台:
  游客:
    BNPL_064: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_065: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_066: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_067: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_068: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_069: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_070: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_071: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_072: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  会员:
    BNPL_073: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_074: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_075: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_076: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_077: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_078: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_079: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_080: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_081: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

  小B会员:
    BNPL_082: 添加单品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_083: 添加多品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_084: 添加自由捆绑商品商品到购物车结算
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_085: 添加单品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_086: 添加多品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_087: 添加自由捆绑商品商品到直接购买
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_088: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_089: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知

    BNPL_090: 添加自由捆绑商品商品到待支付订单转BNPL
      步骤: 选择BNPL先享后付分期支付
      预期: BNPL支付流程正常，订单状态正确更新，用户收到确认通知


BNPL分期金额边界值测试
=============

Web平台:
  游客:
    BNPL_200: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49

    BNPL_201: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5

    BNPL_202: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99

    BNPL_203: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0

    BNPL_204: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99

    BNPL_205: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0

    BNPL_206: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0

    BNPL_207: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01

    BNPL_208: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0

    BNPL_209: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01

    BNPL_210: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99

    BNPL_211: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0

    BNPL_316: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  会员:
    BNPL_317: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_319: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_321: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_318: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_320: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


H5平台:
  游客:
    BNPL_212: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49

    BNPL_213: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5

    BNPL_214: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99

    BNPL_215: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0

    BNPL_216: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99

    BNPL_217: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0

    BNPL_218: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0

    BNPL_219: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01

    BNPL_220: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0

    BNPL_221: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01

    BNPL_222: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99

    BNPL_223: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL先享后付支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0

    BNPL_344: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  会员:
    BNPL_345: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_347: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_349: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_346: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_348: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


Android平台:
  会员:
    BNPL_368: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_370: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_372: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_369: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_371: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


iOS平台:
  会员:
    BNPL_390: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_392: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_394: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

  小B会员:
    BNPL_391: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过

    BNPL_393: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，边界值验证通过


BNPL分期优惠叠加测试
============

Web平台:
  会员:
    BNPL_300: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_303: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_306: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_309: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_311: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_314: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_322: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_325: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_327: 添加商品并应用会员折扣+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  小B会员:
    BNPL_301: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_304: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_307: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_310: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_312: 添加商品并应用小B会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_315: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_323: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_326: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  游客:
    BNPL_302: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_305: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_308: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_313: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_324: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误


H5平台:
  会员:
    BNPL_328: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_331: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_334: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_337: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_339: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_342: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_350: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_353: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_355: 添加商品并应用会员折扣+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  小B会员:
    BNPL_329: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_332: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_335: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_338: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_340: 添加商品并应用小B会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_343: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_351: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_354: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  游客:
    BNPL_330: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_333: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_336: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_341: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_352: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误


Android平台:
  会员:
    BNPL_356: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_358: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_360: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_362: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_364: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_366: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_373: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_375: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_377: 添加商品并应用会员折扣+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  小B会员:
    BNPL_357: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_359: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_361: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_363: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_365: 添加商品并应用小B会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_367: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_374: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_376: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误


iOS平台:
  会员:
    BNPL_378: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_380: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_382: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_384: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_386: 添加商品并应用会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_388: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_395: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_397: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_399: 添加商品并应用会员折扣+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

  小B会员:
    BNPL_379: 添加商品并应用O币抵扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_381: 添加商品并应用优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_383: 添加商品并应用优惠码优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_385: 添加商品并应用限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_387: 添加商品并应用小B会员折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_389: 添加商品并应用自由捆绑优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_396: 添加商品并应用O币+优惠券优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误

    BNPL_398: 添加商品并应用优惠码+限时折扣优惠
      步骤: 选择BNPL先享后付分期支付
      预期: 优惠正确应用，BNPL基于折后价计算，金额准确无误


BNPL分期退款处理测试
============

Web平台:
  会员:
    BNPL_400: BNPL订单订单确认后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_401: BNPL订单订单确认后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_402: BNPL订单发货前申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_403: BNPL订单发货前申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_404: BNPL订单发货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_405: BNPL订单发货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_406: BNPL订单收货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_407: BNPL订单收货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

  小B会员:
    BNPL_408: BNPL订单订单确认后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_409: BNPL订单订单确认后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_410: BNPL订单发货前申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_411: BNPL订单发货前申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_412: BNPL订单发货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_413: BNPL订单发货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_414: BNPL订单收货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_415: BNPL订单收货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确


H5平台:
  会员:
    BNPL_416: BNPL订单订单确认后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_417: BNPL订单订单确认后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_418: BNPL订单发货前申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_419: BNPL订单发货前申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_420: BNPL订单发货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_421: BNPL订单发货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_422: BNPL订单收货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_423: BNPL订单收货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

  小B会员:
    BNPL_424: BNPL订单订单确认后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_425: BNPL订单订单确认后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_426: BNPL订单发货前申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_427: BNPL订单发货前申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_428: BNPL订单发货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_429: BNPL订单发货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_430: BNPL订单收货后申请退款
      步骤: 处理全额退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确

    BNPL_431: BNPL订单收货后申请退款
      步骤: 处理部分退款退款申请
      预期: 退款处理成功，分期计划相应调整，状态同步正确


BNPL额外业务场景测试
============

Web平台:
  游客:
    BNPL_500: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_501: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_502: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_503: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  会员:
    BNPL_504: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_505: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_506: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_507: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  小B会员:
    BNPL_508: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_509: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_510: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_511: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确


H5平台:
  游客:
    BNPL_512: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_513: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_514: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_515: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  会员:
    BNPL_516: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_517: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_518: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_519: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  小B会员:
    BNPL_520: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_521: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_522: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_523: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确


Android平台:
  会员:
    BNPL_524: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_525: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_526: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_527: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  小B会员:
    BNPL_528: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_529: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_530: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_531: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确


iOS平台:
  会员:
    BNPL_532: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_533: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_534: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_535: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

  小B会员:
    BNPL_536: BNPL支付失败后重新选择分期方案
      步骤: 处理支付失败并重新选择BNPL分期
      预期: 支付失败处理正确，重试流程正常，最终支付成功

    BNPL_537: BNPL支付过程中断后恢复支付
      步骤: 从中断点恢复BNPL分期支付流程
      预期: 支付中断恢复正常，订单状态正确更新

    BNPL_538: BNPL分期额度不足时的处理
      步骤: 提示额度不足并引导选择其他支付方式
      预期: 额度不足提示清晰，替代方案引导正确

    BNPL_539: 支付前变更BNPL分期方案
      步骤: 修改已选择的分期方案
      预期: 分期方案变更成功，费用计算正确

{"metadata": {"title": "BNPL支付测试用例最终版", "version": "V11.0 Final", "create_date": "2025-07-16", "description": "基于完整业务知识库的BNPL支付测试用例最终版", "total_cases": 116, "bnpl_providers": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "test_ranges": {"Affirm": "US$50.00 - US$30,000.00", "Afterpay": "US$1.00 - US$4,000.00", "Klarna": "US$0.50 - US$999,999.99"}, "business_scope": "仅美国地区，基于pay接口200响应机制", "test_principles": ["测试平台核心逻辑，不测试第三方功能", "聚焦pay接口、订单生成、状态同步", "覆盖所有用户类型和平台差异", "验证优惠叠加和边界值处理"]}, "test_modules": {"核心支付流程测试": [{"id": "BNPL_001", "name": "BNPL正常支付流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_002", "name": "pay接口失败处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_003", "name": "第三方BNPL异常处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_004", "name": "BNPL正常支付流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_005", "name": "pay接口失败处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_006", "name": "第三方BNPL异常处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_007", "name": "BNPL正常支付流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_008", "name": "pay接口失败处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_009", "name": "第三方BNPL异常处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_010", "name": "BNPL正常支付流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_011", "name": "pay接口失败处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_012", "name": "第三方BNPL异常处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_013", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_014", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_015", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_016", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_017", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_018", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_019", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_020", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_021", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_022", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_023", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_024", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_025", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_026", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_027", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_028", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_029", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_030", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}], "BNPL分期区间边界值测试": [{"id": "BNPL_100", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "expected_bnpl": [], "description": "低于所有BNPL最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_101", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_102", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_103", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_104", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_105", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_106", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_107", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_108", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_109", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_110", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_111", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "expected_bnpl": [], "description": "超出所有BNPL最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_112", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "expected_bnpl": [], "description": "低于所有BNPL最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_113", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_114", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_115", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_116", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_117", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_118", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_119", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_120", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_121", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_122", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_123", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "expected_bnpl": [], "description": "超出所有BNPL最大值", "scenario_type": "boundary_value_test"}}], "优惠叠加BNPL支付测试": [{"id": "BNPL_200", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_201", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_202", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_203", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_204", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_205", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_206", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_207", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_208", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_209", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_210", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_211", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_212", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_213", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_214", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_215", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_216", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_217", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_218", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_219", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_220", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_221", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_222", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_223", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_224", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_225", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_226", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_227", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_228", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_229", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_230", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_231", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_232", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_233", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_234", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_235", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_236", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_237", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_238", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_239", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_240", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_241", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_242", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_243", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_244", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_245", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_246", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_247", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_248", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_249", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}], "订单状态和转换测试": [{"id": "BNPL_300", "name": "待支付订单转BNPL支付", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_301", "name": "BNPL支付状态同步", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_302", "name": "支付方式转换", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_303", "name": "待支付订单转BNPL支付", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_304", "name": "BNPL支付状态同步", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_305", "name": "支付方式转换", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_306", "name": "待支付订单转BNPL支付", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_307", "name": "BNPL支付状态同步", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_308", "name": "支付方式转换", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_309", "name": "待支付订单转BNPL支付", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_310", "name": "BNPL支付状态同步", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_311", "name": "支付方式转换", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}]}, "all_cases": [{"id": "BNPL_001", "name": "BNPL正常支付流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_002", "name": "pay接口失败处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_003", "name": "第三方BNPL异常处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_004", "name": "BNPL正常支付流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_005", "name": "pay接口失败处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_006", "name": "第三方BNPL异常处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_007", "name": "BNPL正常支付流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_008", "name": "pay接口失败处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_009", "name": "第三方BNPL异常处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_010", "name": "BNPL正常支付流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_011", "name": "pay接口失败处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_012", "name": "第三方BNPL异常处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_013", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_014", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_015", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_016", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_017", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_018", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_019", "name": "BNPL正常支付流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_020", "name": "pay接口失败处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_021", "name": "第三方BNPL异常处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_022", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_023", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_024", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_025", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_026", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_027", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_028", "name": "BNPL正常支付流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择商品并使用BNPL支付", "level6_step": "调用pay接口并处理200响应", "level7_substep": "生成订单并跳转第三方完成BNPL支付", "level8_expected": "pay接口响应200，订单正确生成，BNPL支付流程正常", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_029", "name": "pay接口失败处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "选择BNPL支付但pay接口调用失败", "level6_step": "处理pay接口失败响应", "level7_substep": "显示错误信息，不生成订单", "level8_expected": "pay接口失败时不生成订单，向用户显示明确错误信息", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_030", "name": "第三方BNPL异常处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "核心支付流程", "level5_operation": "pay接口成功但第三方BNPL平台异常", "level6_step": "处理第三方平台不可用情况", "level7_substep": "生成待支付订单，提供重新支付选项", "level8_expected": "订单正确生成为待支付状态，用户可重新选择支付方式", "test_data": {"scenario_type": "core_payment_flow"}}, {"id": "BNPL_100", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "expected_bnpl": [], "description": "低于所有BNPL最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_101", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_102", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_103", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_104", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_105", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_106", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_107", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_108", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_109", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_110", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_111", "name": "BNPL分期区间边界值验证", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "expected_bnpl": [], "description": "超出所有BNPL最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_112", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "expected_bnpl": [], "description": "低于所有BNPL最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_113", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_114", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_115", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_116", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_117", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_118", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_119", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_120", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_121", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_122", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_123", "name": "BNPL分期区间边界值验证", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "边界值测试", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "expected_bnpl": [], "description": "超出所有BNPL最大值", "scenario_type": "boundary_value_test"}}, {"id": "BNPL_200", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_201", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_202", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_203", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_204", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_205", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_206", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_207", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_208", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_209", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_210", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_211", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_212", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_213", "name": "优惠叠加BNPL支付测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_214", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_215", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_216", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_217", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_218", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_219", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_220", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_221", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_222", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_223", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_224", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_225", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_226", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_227", "name": "优惠叠加BNPL支付测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_228", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_229", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_230", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_231", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_232", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_233", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_234", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_235", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_236", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_237", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_238", "name": "优惠叠加BNPL支付测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_239", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_240", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用优惠券并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "优惠券", "description": "优惠券与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_241", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_242", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用O币抵扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "O币抵扣", "description": "O币抵扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_243", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用会员折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "会员折扣", "description": "会员折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_244", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_245", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用限时折扣并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "限时折扣", "description": "限时折扣与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_246", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_247", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用自由捆绑并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "自由捆绑", "description": "自由捆绑优惠与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_248", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_249", "name": "优惠叠加BNPL支付测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "优惠叠加测试", "level5_operation": "应用多重优惠并选择BNPL支付", "level6_step": "计算优惠后最终价格", "level7_substep": "基于最终价格选择BNPL分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价格计算，支付流程正常", "test_data": {"discount_type": "多重优惠", "description": "多种优惠同时与BNPL叠加使用", "scenario_type": "discount_integration_test"}}, {"id": "BNPL_300", "name": "待支付订单转BNPL支付", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_301", "name": "BNPL支付状态同步", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_302", "name": "支付方式转换", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_303", "name": "待支付订单转BNPL支付", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_304", "name": "BNPL支付状态同步", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_305", "name": "支付方式转换", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_306", "name": "待支付订单转BNPL支付", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_307", "name": "BNPL支付状态同步", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_308", "name": "支付方式转换", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_309", "name": "待支付订单转BNPL支付", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "从待支付订单重新选择BNPL支付", "level6_step": "选择BNPL分期方案", "level7_substep": "完成BNPL支付并同步订单状态", "level8_expected": "待支付订单成功转换为BNPL支付，订单状态正确更新", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_310", "name": "BNPL支付状态同步", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "BNPL第三方支付完成后状态同步", "level6_step": "接收第三方支付成功通知", "level7_substep": "同步更新订单状态为已支付", "level8_expected": "订单状态正确同步为已支付，用户可查看支付成功订单", "test_data": {"scenario_type": "order_status_test"}}, {"id": "BNPL_311", "name": "支付方式转换", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "订单状态管理", "level5_operation": "待支付订单在不同支付方式间转换", "level6_step": "从BNPL转换为其他支付方式或反之", "level7_substep": "保持订单信息完整性", "level8_expected": "支付方式转换成功，订单信息保持完整，价格计算正确", "test_data": {"scenario_type": "order_status_test"}}]}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL分期测试用例XMind生成器
将JSON格式的测试用例转换为XMind格式
使用更稳定的XMind生成方法
"""

import json
import os
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime
import uuid

def create_xmind_xml_structure(data):
    """创建XMind的XML结构"""

    # 创建根元素
    workbook = ET.Element('xmap-content', xmlns="urn:xmind:xmap:xmlns:content:2.0")
    workbook.set('xmlns:fo', "http://www.w3.org/1999/XSL/Format")

    # 创建工作表
    sheet = ET.SubElement(workbook, 'sheet', id="sheet1", theme="theme1")

    # 创建根主题
    topic = ET.SubElement(sheet, 'topic', id="root")
    title = ET.SubElement(topic, 'title')
    title.text = "BNPL先享后付分期测试用例"

    # 创建子主题容器
    children = ET.SubElement(topic, 'children')
    topics = ET.SubElement(children, 'topics', type="attached")

    return workbook, topics

def add_topic_to_parent(parent, title_text, topic_id=None):
    """向父节点添加子主题"""
    if topic_id is None:
        topic_id = str(uuid.uuid4())

    topic = ET.SubElement(parent, 'topic', id=topic_id)
    title = ET.SubElement(topic, 'title')
    title.text = title_text

    return topic

def create_xmind_from_json(json_file, output_file):
    """从JSON文件创建XMind文件"""

    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 创建XML结构
    workbook, main_topics = create_xmind_xml_structure(data)

    # 添加版本信息主题
    version_topic = add_topic_to_parent(main_topics, f"版本信息 {data['metadata']['version']}")
    version_children = ET.SubElement(version_topic, 'children')
    version_topics = ET.SubElement(version_children, 'topics', type="attached")

    # 添加版本详细信息
    version_details = [
        f"创建日期: {data['metadata']['create_date']}",
        f"测试用例总数: {data['metadata']['total_cases']}个",
        f"测试范围: {', '.join(data['metadata']['bnpl_providers'])}",
        "测试类型: 分期区间、下单流程、优惠叠加、退款处理",
        "覆盖平台: Android、iOS、Web、H5"
    ]

    for detail in version_details:
        add_topic_to_parent(version_topics, detail)

    # 添加BNPL分期区间信息
    range_topic = add_topic_to_parent(main_topics, "BNPL分期区间")
    range_children = ET.SubElement(range_topic, 'children')
    range_topics = ET.SubElement(range_children, 'topics', type="attached")

    for provider, range_info in data['metadata']['test_ranges'].items():
        add_topic_to_parent(range_topics, f"{provider}: {range_info}")
    
    # 添加测试模块
    for module_name, cases in data['test_modules'].items():
        if not cases:  # 跳过空模块
            continue
            
        module_topic = root_topic.addSubTopic()
        module_topic.setTitle(module_name)
        
        # 按用例ID分组，避免重复
        processed_cases = {}
        for case in cases:
            case_id = case['id']
            if case_id not in processed_cases:
                processed_cases[case_id] = case
        
        # 为每个测试用例创建完整的8层级结构
        case_groups = {}
        for case in processed_cases.values():
            # 创建分组键：平台-用户-场景
            group_key = f"{case['level2_platform']}-{case['level3_user']}-{case['level4_scenario']}"
            if group_key not in case_groups:
                case_groups[group_key] = []
            case_groups[group_key].append(case)
        
        # 为每个分组创建层级结构
        for group_key, group_cases in case_groups.items():
            # 第1层：用例ID和名称
            first_case = group_cases[0]
            case_topic = module_topic.addSubTopic()
            case_topic.setTitle(f"{first_case['id']}: {first_case['name']}")
            
            # 第2层：平台
            platform_topic = case_topic.addSubTopic()
            platform_topic.setTitle(first_case['level2_platform'])
            
            # 第3层：用户类型
            user_topic = platform_topic.addSubTopic()
            user_topic.setTitle(first_case['level3_user'])
            
            # 第4层：场景
            scenario_topic = user_topic.addSubTopic()
            scenario_topic.setTitle(first_case['level4_scenario'])
            
            # 为同一分组的不同操作创建子节点
            for case in group_cases:
                # 第5层：操作
                operation_topic = scenario_topic.addSubTopic()
                operation_topic.setTitle(case['level5_operation'])
                
                # 第6层：步骤
                step_topic = operation_topic.addSubTopic()
                step_topic.setTitle(case['level6_step'])
                
                # 第7层：子步骤
                substep_topic = step_topic.addSubTopic()
                substep_topic.setTitle(case['level7_substep'])
                
                # 第8层：预期结果
                expected_topic = substep_topic.addSubTopic()
                expected_topic.setTitle(case['level8_expected'])
                
                # 添加测试数据备注（可选）
                if 'test_data' in case and case['test_data']:
                    data_info = []
                    test_data = case['test_data']
                    
                    if 'amount' in test_data:
                        data_info.append(f"金额: US${test_data['amount']}")
                    if 'expected_bnpl' in test_data:
                        data_info.append(f"可用BNPL: {test_data['expected_bnpl']}")
                    if 'discount_type' in test_data:
                        data_info.append(f"优惠类型: {test_data['discount_type']}")
                    if 'refund_type' in test_data:
                        data_info.append(f"退款类型: {test_data['refund_type']}")
                    
                    if data_info:
                        data_topic = expected_topic.addSubTopic()
                        data_topic.setTitle(f"测试数据: {'; '.join(data_info)}")
    
    # 保存XMind文件
    xmind.save(workbook, output_file)
    return workbook

def create_summary_statistics(data):
    """创建测试用例统计摘要"""
    summary = {
        "总体统计": {
            "测试用例总数": data['metadata']['total_cases'],
            "测试模块数": len([m for m in data['test_modules'].values() if m]),
            "BNPL服务商": len(data['metadata']['bnpl_providers']),
            "创建日期": data['metadata']['create_date']
        },
        "模块分布": {},
        "平台覆盖": {},
        "用户类型": {},
        "金额范围": data['metadata']['test_ranges']
    }
    
    # 统计各模块用例数
    for module_name, cases in data['test_modules'].items():
        if cases:
            summary["模块分布"][module_name] = len(cases)
    
    # 统计平台和用户类型分布
    all_cases = data['all_cases']
    platforms = {}
    users = {}
    
    for case in all_cases:
        platform = case.get('level2_platform', 'Unknown')
        user = case.get('level3_user', 'Unknown')
        
        platforms[platform] = platforms.get(platform, 0) + 1
        users[user] = users.get(user, 0) + 1
    
    summary["平台覆盖"] = platforms
    summary["用户类型"] = users
    
    return summary

if __name__ == "__main__":
    # 输入和输出文件
    json_file = "bnpl_installment_test_cases.json"
    output_file = "../输出用例/BNPL先享后付_分期测试用例_v10.0.xmind"
    
    try:
        print("开始转换BNPL分期测试用例到XMind格式...")
        
        # 读取JSON数据
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建统计摘要
        summary = create_summary_statistics(data)
        print("\n📊 测试用例统计摘要:")
        for category, stats in summary.items():
            print(f"\n{category}:")
            if isinstance(stats, dict):
                for key, value in stats.items():
                    print(f"  - {key}: {value}")
            else:
                print(f"  {stats}")
        
        # 创建XMind文件
        workbook = create_xmind_from_json(json_file, output_file)
        
        print(f"\n✅ XMind文件创建成功!")
        print(f"📁 文件位置: {output_file}")
        print(f"📋 包含 {data['metadata']['total_cases']} 个测试用例")
        print(f"🎯 覆盖 {len(data['metadata']['bnpl_providers'])} 种BNPL支付方式")
        print(f"🔧 采用V5.0标准8层级结构")
        
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

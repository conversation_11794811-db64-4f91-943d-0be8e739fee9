# 边界值与等价类分析指南

## 用户类型等价类划分

### 有效等价类
1. **游客用户**
   - 未登录状态
   - 可浏览商品
   - 部分活动可参与（需配置允许）

2. **普通会员**
   - 已注册并登录
   - 享受会员价格
   - 可参与会员活动
   - 可使用优惠券、优惠码、O币

3. **小B会员**
   - 特殊会员身份
   - 享受专享折扣
   - 不能使用优惠码
   - 可使用优惠券、O币

### 无效等价类
1. **异常账号状态**
   - 被冻结的账号
   - 被限制的账号
   - 已注销的账号

## 平台端口等价类

### 有效等价类
- **Android端**: 安卓移动应用
- **iOS端**: 苹果移动应用
- **Web端**: 桌面网页版
- **H5端**: 移动网页版

### 边界条件
- **版本兼容性**: 最低支持版本
- **设备兼容性**: 不同设备型号
- **网络环境**: 不同网络条件下的表现

## 优惠金额边界值分析

### 固定金额优惠
- **最小值**: 0.01元（最小货币单位）
- **常用值**: 5元、10元、20元、50元
- **大额值**: 100元、500元、1000元
- **边界外**: 0元（无效）、负数（无效）

### 折扣比例
- **最小折扣**: 0.1折（1%）
- **常用折扣**: 5折、7折、8折、9折
- **最大折扣**: 9.9折（99%）
- **边界值**: 0折（免费）、10折（原价）
- **无效值**: 负折扣、超过10折

### 最低消费金额
- **无门槛**: 0元
- **低门槛**: 50元、100元
- **中等门槛**: 200元、500元
- **高门槛**: 1000元、2000元
- **边界测试**: 刚好达到门槛、差1分钱

## O币抵扣边界值

### 抵扣比例
- **最小比例**: 1%
- **常用比例**: 10%、20%、50%
- **最大比例**: 100%（全额抵扣）
- **边界测试**: 0%（不抵扣）、超过100%

### 最低支付金额
- **最小值**: 0.01元
- **常用值**: 1元、5元、10元
- **边界测试**: 
  - 订单金额等于最低支付金额
  - 订单金额小于最低支付金额
  - O币抵扣后剩余金额等于最低支付金额

### O币余额
- **零余额**: 0个O币
- **少量余额**: 1-10个O币
- **充足余额**: 足够抵扣订单金额
- **超额余额**: 远超订单金额的O币

## 时间边界值分析

### 活动时间
- **活动开始前**: 提前1分钟、1秒
- **活动开始时**: 准确的开始时间
- **活动进行中**: 活动期间的任意时间
- **活动结束时**: 准确的结束时间
- **活动结束后**: 延后1秒、1分钟

### 优惠券有效期
- **未生效**: 生效时间前
- **刚生效**: 生效时间点
- **有效期内**: 有效期间的任意时间
- **即将过期**: 过期前1小时、1分钟
- **刚过期**: 过期时间点
- **已过期**: 过期后的任意时间

## 商品数量边界值

### 购买数量
- **最小值**: 1件
- **常用值**: 2件、5件、10件
- **限购边界**: 达到限购数量
- **库存边界**: 等于库存数量
- **超出库存**: 超过库存数量
- **异常值**: 0件、负数

### 库存数量
- **缺货**: 0件库存
- **少量库存**: 1-5件
- **充足库存**: 大于限购数量
- **无限库存**: 不限制库存

## 价格边界值分析

### 商品价格
- **最低价**: 0.01元
- **促销价**: 低于原价的价格
- **会员价**: 会员专享价格
- **小B价**: 小B会员专享价格
- **异常价**: 0元、负价格

### 订单金额
- **最小订单**: 0.01元
- **免邮门槛**: 达到免邮金额
- **优惠门槛**: 达到优惠使用门槛
- **大额订单**: 超过常规金额的订单

## 地区边界值

### 支持地区
- **主要市场**: US、UK、DE、FR、JP等
- **新兴市场**: TH、KR等
- **边缘地区**: 部分支持的地区
- **不支持地区**: 明确不支持的地区

### 配送范围
- **标准配送**: 常规配送地区
- **偏远地区**: 需要额外运费的地区
- **不配送**: 无法配送的地区

## 并发边界值

### 用户并发
- **低并发**: 1-10个用户
- **中等并发**: 100-1000个用户
- **高并发**: 1000-10000个用户
- **极限并发**: 超过系统设计容量

### 秒杀场景
- **商品数量**: 限量商品的抢购
- **时间精度**: 秒级的时间控制
- **库存扣减**: 并发下的库存准确性

## 数据长度边界值

### 用户输入
- **用户名**: 最小长度、最大长度、超长输入
- **密码**: 最小长度、最大长度、特殊字符
- **地址**: 最大字符数限制
- **备注**: 最大字符数限制

### 商品信息
- **商品名称**: 最大长度限制
- **商品描述**: 最大长度限制
- **SKU编码**: 固定长度或最大长度

## 测试数据设计原则

### 边界值选择
1. **刚好在边界上**: 等于边界值
2. **刚好在边界内**: 边界值±1
3. **刚好在边界外**: 超出边界的最小值

### 等价类代表值
1. **有效等价类**: 选择典型的有效值
2. **无效等价类**: 选择典型的无效值
3. **边界等价类**: 选择边界附近的值

### 组合测试策略
1. **全组合**: 所有可能的组合（适用于关键功能）
2. **正交组合**: 用最少组合覆盖最多场景
3. **成对组合**: 任意两个因子的所有组合
4. **风险驱动**: 优先测试高风险的组合

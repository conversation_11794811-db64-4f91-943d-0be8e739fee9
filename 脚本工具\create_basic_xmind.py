#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础BNPL分期测试用例XMind生成器
使用最简单的方法创建XMind文件
"""

import json
import os
import zipfile
import tempfile
from datetime import datetime

def create_basic_xmind_content():
    """创建基础的XMind内容XML"""
    
    content_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:xlink="http://www.w3.org/1999/xlink" version="2.0">
  <sheet id="sheet1" theme="theme1">
    <topic id="root" structure-class="org.xmind.ui.logic.right">
      <title>BNPL先享后付分期测试用例</title>
      <children>
        <topics type="attached">
          <topic id="version" structure-class="org.xmind.ui.logic.right">
            <title>版本信息 V10.0</title>
            <children>
              <topics type="attached">
                <topic id="date">
                  <title>创建日期: 2025-07-16</title>
                </topic>
                <topic id="total">
                  <title>测试用例总数: 256个</title>
                </topic>
                <topic id="scope">
                  <title>测试范围: Affirm, Afterpay, Klarna</title>
                </topic>
                <topic id="type">
                  <title>测试类型: 分期区间、下单流程、优惠叠加、退款处理</title>
                </topic>
                <topic id="platform">
                  <title>覆盖平台: Android、iOS、Web、H5</title>
                </topic>
              </topics>
            </children>
          </topic>
          <topic id="ranges" structure-class="org.xmind.ui.logic.right">
            <title>BNPL分期区间</title>
            <children>
              <topics type="attached">
                <topic id="affirm">
                  <title>Affirm: US$50.00 - US$30,000.00</title>
                </topic>
                <topic id="afterpay">
                  <title>Afterpay: US$1.00 - US$4,000.00</title>
                </topic>
                <topic id="klarna">
                  <title>Klarna: US$0.50 - US$999,999.99</title>
                </topic>
              </topics>
            </children>
          </topic>
          <topic id="module1" structure-class="org.xmind.ui.logic.right">
            <title>BNPL分期下单流程测试 (120个用例)</title>
            <children>
              <topics type="attached">
                <topic id="android_platform">
                  <title>Android平台 (24个用例)</title>
                  <children>
                    <topics type="attached">
                      <topic id="android_member">
                        <title>会员用户 (12个用例)</title>
                        <children>
                          <topics type="attached">
                            <topic id="android_member_cart">
                              <title>购物车结算 (4个用例)</title>
                              <children>
                                <topics type="attached">
                                  <topic id="cart_single">
                                    <title>单品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="cart_multi">
                                    <title>多品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="cart_bundle">
                                    <title>捆绑商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="cart_presale">
                                    <title>预售商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                </topics>
                              </children>
                            </topic>
                            <topic id="android_member_direct">
                              <title>直接购买 (4个用例)</title>
                              <children>
                                <topics type="attached">
                                  <topic id="direct_single">
                                    <title>单品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="direct_multi">
                                    <title>多品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="direct_bundle">
                                    <title>捆绑商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="direct_presale">
                                    <title>预售商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                </topics>
                              </children>
                            </topic>
                            <topic id="android_member_instant">
                              <title>立即购买 (4个用例)</title>
                              <children>
                                <topics type="attached">
                                  <topic id="instant_single">
                                    <title>单品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="instant_multi">
                                    <title>多品商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="instant_bundle">
                                    <title>捆绑商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                  <topic id="instant_presale">
                                    <title>预售商品 - BNPL支付 (US$100)</title>
                                  </topic>
                                </topics>
                              </children>
                            </topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="android_smallb">
                        <title>小B会员用户 (12个用例)</title>
                        <children>
                          <topics type="attached">
                            <topic id="smallb_scenarios">
                              <title>购物车结算 + 直接购买 + 立即购买</title>
                              <children>
                                <topics type="attached">
                                  <topic id="smallb_note">
                                    <title>各4个用例，涵盖4种商品类型</title>
                                  </topic>
                                </topics>
                              </children>
                            </topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="ios_platform">
                  <title>iOS平台 (24个用例)</title>
                  <children>
                    <topics type="attached">
                      <topic id="ios_structure">
                        <title>结构同Android平台</title>
                        <children>
                          <topics type="attached">
                            <topic id="ios_member">
                              <title>会员用户: 12个用例</title>
                            </topic>
                            <topic id="ios_smallb">
                              <title>小B会员用户: 12个用例</title>
                            </topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="web_platform">
                  <title>Web平台 (36个用例)</title>
                  <children>
                    <topics type="attached">
                      <topic id="web_guest">
                        <title>游客用户 (12个用例)</title>
                        <children>
                          <topics type="attached">
                            <topic id="web_guest_scenarios">
                              <title>购物车结算 + 直接购买 + 立即购买</title>
                            </topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="web_member">
                        <title>会员用户 (12个用例)</title>
                      </topic>
                      <topic id="web_smallb">
                        <title>小B会员用户 (12个用例)</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="h5_platform">
                  <title>H5平台 (36个用例)</title>
                  <children>
                    <topics type="attached">
                      <topic id="h5_structure">
                        <title>结构同Web平台</title>
                        <children>
                          <topics type="attached">
                            <topic id="h5_all_users">
                              <title>游客 + 会员 + 小B会员: 各12个用例</title>
                            </topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>
          <topic id="module2" structure-class="org.xmind.ui.logic.right">
            <title>BNPL分期金额边界值测试 (24个用例)</title>
            <children>
              <topics type="attached">
                <topic id="boundary_lower">
                  <title>下边界测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="b1">
                        <title>US$0.49 - 无BNPL可用</title>
                      </topic>
                      <topic id="b2">
                        <title>US$0.50 - 仅Klarna可用</title>
                      </topic>
                      <topic id="b3">
                        <title>US$1.00 - Klarna+Afterpay可用</title>
                      </topic>
                      <topic id="b4">
                        <title>US$50.00 - 全部BNPL可用</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="boundary_upper">
                  <title>上边界测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="u1">
                        <title>US$4,000.00 - 全部BNPL可用</title>
                      </topic>
                      <topic id="u2">
                        <title>US$4,000.01 - Klarna+Affirm可用</title>
                      </topic>
                      <topic id="u3">
                        <title>US$30,000.00 - Klarna+Affirm可用</title>
                      </topic>
                      <topic id="u4">
                        <title>US$999,999.99 - 仅Klarna可用</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>
          <topic id="module3" structure-class="org.xmind.ui.logic.right">
            <title>BNPL分期优惠叠加测试 (80个用例)</title>
            <children>
              <topics type="attached">
                <topic id="discount_types">
                  <title>优惠类型测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="ocoin">
                        <title>O币抵扣 + BNPL分期</title>
                      </topic>
                      <topic id="coupon">
                        <title>优惠券 + BNPL分期</title>
                      </topic>
                      <topic id="flash_sale">
                        <title>限时折扣 + BNPL分期</title>
                      </topic>
                      <topic id="member_discount">
                        <title>会员折扣 + BNPL分期</title>
                      </topic>
                      <topic id="bundle">
                        <title>自由捆绑 + BNPL分期</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="boundary_discount">
                  <title>边界值优惠测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="bd1">
                        <title>优惠后刚好US$50.00 (Affirm最小值)</title>
                      </topic>
                      <topic id="bd2">
                        <title>优惠后刚好US$4,000.00 (Afterpay最大值)</title>
                      </topic>
                      <topic id="bd3">
                        <title>优惠后刚好US$0.50 (Klarna最小值)</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>
          <topic id="module4" structure-class="org.xmind.ui.logic.right">
            <title>BNPL分期退款处理测试 (32个用例)</title>
            <children>
              <topics type="attached">
                <topic id="refund_timing">
                  <title>退款时机测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="rt1">
                        <title>订单确认后退款</title>
                      </topic>
                      <topic id="rt2">
                        <title>发货前退款</title>
                      </topic>
                      <topic id="rt3">
                        <title>发货后退款</title>
                      </topic>
                      <topic id="rt4">
                        <title>收货后退款</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
                <topic id="refund_type">
                  <title>退款类型测试</title>
                  <children>
                    <topics type="attached">
                      <topic id="full_refund">
                        <title>全额退款 - 分期计划完全取消</title>
                      </topic>
                      <topic id="partial_refund">
                        <title>部分退款 - 分期计划按比例调整</title>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>
        </topics>
      </children>
    </topic>
  </sheet>
</xmap-content>'''
    
    return content_xml

def create_manifest_xml():
    """创建manifest.xml文件"""
    
    manifest_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
</manifest>'''
    
    return manifest_xml

def create_xmind_file(output_path):
    """创建XMind文件"""
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # 创建META-INF目录
        meta_inf_dir = os.path.join(temp_dir, 'META-INF')
        os.makedirs(meta_inf_dir, exist_ok=True)
        
        # 写入content.xml
        content_path = os.path.join(temp_dir, 'content.xml')
        with open(content_path, 'w', encoding='utf-8') as f:
            f.write(create_basic_xmind_content())
        
        # 写入manifest.xml
        manifest_path = os.path.join(meta_inf_dir, 'manifest.xml')
        with open(manifest_path, 'w', encoding='utf-8') as f:
            f.write(create_manifest_xml())
        
        # 创建XMind文件（实际上是ZIP文件）
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as xmind_file:
            # 添加content.xml
            xmind_file.write(content_path, 'content.xml')
            # 添加manifest.xml
            xmind_file.write(manifest_path, 'META-INF/manifest.xml')
    
    return True

if __name__ == "__main__":
    output_file = "../输出用例/BNPL先享后付_分期测试用例_v10.0_基础版.xmind"
    
    try:
        print("开始创建基础版BNPL分期测试用例XMind文件...")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建XMind文件
        success = create_xmind_file(output_file)
        
        if success:
            print(f"✅ XMind文件创建成功!")
            print(f"📁 文件位置: {output_file}")
            print(f"📋 包含BNPL分期测试的主要模块和结构")
            print(f"🎯 覆盖3种BNPL支付方式的分期区间测试")
            print(f"🔧 采用基础XMind格式，兼容性更好")
            
            # 检查文件大小
            file_size = os.path.getsize(output_file)
            print(f"📊 文件大小: {file_size} 字节")
            
        else:
            print("❌ XMind文件创建失败")
            
    except Exception as e:
        print(f"❌ 创建过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

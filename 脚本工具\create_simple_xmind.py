#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的BNPL分期测试用例XMind生成器
使用文本格式先生成，然后手动转换为XMind
"""

import json
from datetime import datetime

def create_text_format_for_xmind(json_file, output_file):
    """创建适合XMind导入的文本格式"""
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    lines = []
    
    # 添加标题
    lines.append("BNPL先享后付分期测试用例")
    lines.append("")
    
    # 添加版本信息
    lines.append(f"├── 版本信息 {data['metadata']['version']}")
    lines.append(f"    ├── 创建日期: {data['metadata']['create_date']}")
    lines.append(f"    ├── 测试用例总数: {data['metadata']['total_cases']}个")
    lines.append(f"    ├── 测试范围: {', '.join(data['metadata']['bnpl_providers'])}")
    lines.append("    ├── 测试类型: 分期区间、下单流程、优惠叠加、退款处理")
    lines.append("    └── 覆盖平台: Android、iOS、Web、H5")
    lines.append("")
    
    # 添加BNPL分期区间信息
    lines.append("├── BNPL分期区间")
    for i, (provider, range_info) in enumerate(data['metadata']['test_ranges'].items()):
        prefix = "└──" if i == len(data['metadata']['test_ranges']) - 1 else "├──"
        lines.append(f"    {prefix} {provider}: {range_info}")
    lines.append("")
    
    # 添加测试模块
    module_count = 0
    total_modules = len([m for m in data['test_modules'].values() if m])
    
    for module_name, cases in data['test_modules'].items():
        if not cases:  # 跳过空模块
            continue
            
        module_count += 1
        module_prefix = "└──" if module_count == total_modules else "├──"
        lines.append(f"{module_prefix} {module_name}")
        
        # 按平台-用户-场景分组
        case_groups = {}
        for case in cases:
            group_key = f"{case['level2_platform']}-{case['level3_user']}-{case['level4_scenario']}"
            if group_key not in case_groups:
                case_groups[group_key] = []
            case_groups[group_key].append(case)
        
        # 为每个分组创建层级结构
        group_count = 0
        total_groups = len(case_groups)
        
        for group_key, group_cases in case_groups.items():
            group_count += 1
            group_prefix = "    └──" if group_count == total_groups else "    ├──"
            
            # 第1层：用例ID和名称
            first_case = group_cases[0]
            lines.append(f"{group_prefix} {first_case['id']}: {first_case['name']}")
            
            # 第2层：平台
            platform_prefix = "        └──" if group_count == total_groups else "        ├──"
            lines.append(f"{platform_prefix} {first_case['level2_platform']}")
            
            # 第3层：用户类型
            user_prefix = "            └──" if group_count == total_groups else "            ├──"
            lines.append(f"{user_prefix} {first_case['level3_user']}")
            
            # 第4层：场景
            scenario_prefix = "                └──" if group_count == total_groups else "                ├──"
            lines.append(f"{scenario_prefix} {first_case['level4_scenario']}")
            
            # 为同一分组的不同操作创建子节点
            operation_count = 0
            total_operations = len(group_cases)
            
            for case in group_cases:
                operation_count += 1
                op_prefix = "                    └──" if operation_count == total_operations else "                    ├──"
                
                # 第5层：操作
                lines.append(f"{op_prefix} {case['level5_operation']}")
                
                # 第6层：步骤
                step_prefix = "                        └──" if operation_count == total_operations else "                        ├──"
                lines.append(f"{step_prefix} {case['level6_step']}")
                
                # 第7层：子步骤
                substep_prefix = "                            └──" if operation_count == total_operations else "                            ├──"
                lines.append(f"{substep_prefix} {case['level7_substep']}")
                
                # 第8层：预期结果
                expected_prefix = "                                └──"
                lines.append(f"{expected_prefix} {case['level8_expected']}")
                
                # 添加测试数据（如果有）
                if 'test_data' in case and case['test_data']:
                    test_data = case['test_data']
                    data_info = []
                    
                    if 'amount' in test_data:
                        data_info.append(f"金额: US${test_data['amount']}")
                    if 'expected_bnpl' in test_data:
                        data_info.append(f"可用BNPL: {test_data['expected_bnpl']}")
                    if 'discount_type' in test_data:
                        data_info.append(f"优惠类型: {test_data['discount_type']}")
                    if 'refund_type' in test_data:
                        data_info.append(f"退款类型: {test_data['refund_type']}")
                    
                    if data_info:
                        lines.append(f"                                    └── 测试数据: {'; '.join(data_info)}")
                
                if operation_count < total_operations:
                    lines.append("")  # 添加空行分隔
        
        if module_count < total_modules:
            lines.append("")  # 模块间添加空行
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    return lines

def create_simplified_structure(json_file, output_file):
    """创建简化的结构，便于手动导入XMind"""
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    lines = []
    
    # 标题
    lines.append("BNPL先享后付分期测试用例")
    lines.append("=" * 50)
    lines.append("")
    
    # 版本信息
    lines.append(f"版本: {data['metadata']['version']}")
    lines.append(f"创建日期: {data['metadata']['create_date']}")
    lines.append(f"测试用例总数: {data['metadata']['total_cases']}个")
    lines.append(f"BNPL服务商: {', '.join(data['metadata']['bnpl_providers'])}")
    lines.append("")
    
    # BNPL分期区间
    lines.append("BNPL分期区间:")
    for provider, range_info in data['metadata']['test_ranges'].items():
        lines.append(f"- {provider}: {range_info}")
    lines.append("")
    
    # 测试模块统计
    lines.append("测试模块分布:")
    for module_name, cases in data['test_modules'].items():
        if cases:
            lines.append(f"- {module_name}: {len(cases)}个用例")
    lines.append("")
    
    # 详细测试用例（简化版）
    lines.append("详细测试用例:")
    lines.append("-" * 50)
    
    case_id = 1
    for module_name, cases in data['test_modules'].items():
        if not cases:
            continue
            
        lines.append(f"\n{module_name}")
        lines.append("=" * len(module_name))
        
        # 按平台分组显示
        platform_groups = {}
        for case in cases:
            platform = case['level2_platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(case)
        
        for platform, platform_cases in platform_groups.items():
            lines.append(f"\n{platform}平台:")
            
            # 按用户类型分组
            user_groups = {}
            for case in platform_cases:
                user = case['level3_user']
                if user not in user_groups:
                    user_groups[user] = []
                user_groups[user].append(case)
            
            for user, user_cases in user_groups.items():
                lines.append(f"  {user}:")
                
                for case in user_cases[:3]:  # 每个用户类型只显示前3个用例作为示例
                    lines.append(f"    {case['id']}: {case['level5_operation']}")
                    lines.append(f"      步骤: {case['level6_step']}")
                    lines.append(f"      预期: {case['level8_expected']}")
                    
                    if 'test_data' in case and 'amount' in case['test_data']:
                        lines.append(f"      金额: US${case['test_data']['amount']}")
                    lines.append("")
                
                if len(user_cases) > 3:
                    lines.append(f"    ... 还有{len(user_cases) - 3}个类似用例")
                    lines.append("")
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    return lines

if __name__ == "__main__":
    # 输入和输出文件
    json_file = "bnpl_installment_test_cases.json"
    text_output = "../输出用例/BNPL先享后付_分期测试用例_v10.0.txt"
    simple_output = "../输出用例/BNPL先享后付_分期测试用例_v10.0_简化版.txt"
    
    try:
        print("开始生成BNPL分期测试用例文本格式...")
        
        # 生成完整的文本格式
        lines = create_text_format_for_xmind(json_file, text_output)
        print(f"✅ 完整文本格式已生成: {text_output}")
        print(f"📋 包含 {len(lines)} 行内容")
        
        # 生成简化版本
        simple_lines = create_simplified_structure(json_file, simple_output)
        print(f"✅ 简化版本已生成: {simple_output}")
        print(f"📋 包含 {len(simple_lines)} 行内容")
        
        print("\n📝 使用说明:")
        print("1. 可以直接查看文本文件了解测试用例结构")
        print("2. 可以将文本内容复制到XMind中手动创建思维导图")
        print("3. 简化版本便于快速浏览和理解测试覆盖范围")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

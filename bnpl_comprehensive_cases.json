{"title": "BNPL先享后付功能全面测试用例", "version": "v7.0", "created_date": "2025-07-01", "total_cases": 584, "design_principles": ["重点关注异常流问题，反向用例占60-70%", "按照BNPL先享后付_全面测试用例_v2.0.txt格式风格", "7大类异常全面覆盖：网络、系统、支付、业务、用户操作、数据、安全", "每种异常都包含基础场景和恢复场景", "增加组合异常场景测试", "目标用例数量：500-600条"], "test_modules": [{"module_name": "BNPL正向流程测试", "description": "验证BNPL支付的正常业务流程", "test_cases": [{"case_id": "BNPL_001", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_002", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_003", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_004", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_005", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_006", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_007", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_008", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_009", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_010", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_011", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_012", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_013", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_014", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_015", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_016", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_017", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_018", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_019", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_020", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_021", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_022", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_023", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_024", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_025", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_026", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_027", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_028", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_029", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_030", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_031", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_032", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_033", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_034", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_035", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_036", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_037", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_038", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_039", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_040", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_041", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_042", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_043", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_044", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_045", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_046", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_047", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_048", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_049", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_050", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_051", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_052", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_053", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_054", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_055", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择正常商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_056", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择限时折扣商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_057", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "选择自由捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_058", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择正常商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_059", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择限时折扣商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_060", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接购买", "operation": "选择自由捆绑商品进入直接购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"}, "priority": "高"}, {"case_id": "BNPL_061", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_062", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠码优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_063", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_064", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_065", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_066", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_067", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_068", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_069", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠码优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_070", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_071", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_072", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_073", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_074", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_075", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_076", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠码优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_077", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_078", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_079", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_080", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_081", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_082", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_083", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠码优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_084", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_085", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_086", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠券优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_087", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "O币抵扣优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_088", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "会员价优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"}, "priority": "中"}, {"case_id": "BNPL_089", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_090", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_091", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_092", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_093", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_094", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_095", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_096", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_097", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_098", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_099", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_100", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_101", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_102", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_103", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}, {"case_id": "BNPL_104", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"}, "priority": "高"}]}, {"module_name": "BNPL异常流程测试", "description": "重点验证各种异常情况下的系统处理能力", "test_cases": [{"case_id": "BNPL_105", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_106", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常恢复场景", "operation": "在网络超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络超时_恢复", "priority": "中"}, {"case_id": "BNPL_107", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_108", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络超时异常恢复场景", "operation": "在网络超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络超时_恢复", "priority": "中"}, {"case_id": "BNPL_109", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_110", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络超时异常恢复场景", "operation": "在网络超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络超时_恢复", "priority": "中"}, {"case_id": "BNPL_111", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_112", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络超时异常恢复场景", "operation": "在网络超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络超时_恢复", "priority": "中"}, {"case_id": "BNPL_113", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络中断", "priority": "中"}, {"case_id": "BNPL_114", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络中断异常恢复场景", "operation": "在网络中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络中断_恢复", "priority": "中"}, {"case_id": "BNPL_115", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络中断", "priority": "中"}, {"case_id": "BNPL_116", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络中断异常恢复场景", "operation": "在网络中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络中断_恢复", "priority": "中"}, {"case_id": "BNPL_117", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络中断", "priority": "中"}, {"case_id": "BNPL_118", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络中断异常恢复场景", "operation": "在网络中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络中断_恢复", "priority": "中"}, {"case_id": "BNPL_119", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络中断", "priority": "中"}, {"case_id": "BNPL_120", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络中断异常恢复场景", "operation": "在网络中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络中断_恢复", "priority": "中"}, {"case_id": "BNPL_121", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络波动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络波动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络波动", "priority": "中"}, {"case_id": "BNPL_122", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络波动异常恢复场景", "operation": "在网络波动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络波动_恢复", "priority": "中"}, {"case_id": "BNPL_123", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络波动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络波动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络波动", "priority": "中"}, {"case_id": "BNPL_124", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络波动异常恢复场景", "operation": "在网络波动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络波动_恢复", "priority": "中"}, {"case_id": "BNPL_125", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络波动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络波动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络波动", "priority": "中"}, {"case_id": "BNPL_126", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络波动异常恢复场景", "operation": "在网络波动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络波动_恢复", "priority": "中"}, {"case_id": "BNPL_127", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络波动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发网络波动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "网络波动", "priority": "中"}, {"case_id": "BNPL_128", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络波动异常恢复场景", "operation": "在网络波动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "网络波动_恢复", "priority": "中"}, {"case_id": "BNPL_129", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "连接失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发连接失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "连接失败", "priority": "中"}, {"case_id": "BNPL_130", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "连接失败异常恢复场景", "operation": "在连接失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "连接失败_恢复", "priority": "中"}, {"case_id": "BNPL_131", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "连接失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发连接失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "连接失败", "priority": "中"}, {"case_id": "BNPL_132", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "连接失败异常恢复场景", "operation": "在连接失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "连接失败_恢复", "priority": "中"}, {"case_id": "BNPL_133", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "连接失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发连接失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "连接失败", "priority": "中"}, {"case_id": "BNPL_134", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "连接失败异常恢复场景", "operation": "在连接失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "连接失败_恢复", "priority": "中"}, {"case_id": "BNPL_135", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "连接失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发连接失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "连接失败", "priority": "中"}, {"case_id": "BNPL_136", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "连接失败异常恢复场景", "operation": "在连接失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "连接失败_恢复", "priority": "中"}, {"case_id": "BNPL_137", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "DNS解析失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发DNS解析失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别DNS解析失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "DNS解析失败", "priority": "中"}, {"case_id": "BNPL_138", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "DNS解析失败异常恢复场景", "operation": "在DNS解析失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "DNS解析失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "DNS解析失败_恢复", "priority": "中"}, {"case_id": "BNPL_139", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "DNS解析失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发DNS解析失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别DNS解析失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "DNS解析失败", "priority": "中"}, {"case_id": "BNPL_140", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "DNS解析失败异常恢复场景", "operation": "在DNS解析失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "DNS解析失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "DNS解析失败_恢复", "priority": "中"}, {"case_id": "BNPL_141", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "DNS解析失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发DNS解析失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别DNS解析失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "DNS解析失败", "priority": "中"}, {"case_id": "BNPL_142", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "DNS解析失败异常恢复场景", "operation": "在DNS解析失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "DNS解析失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "DNS解析失败_恢复", "priority": "中"}, {"case_id": "BNPL_143", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "DNS解析失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发DNS解析失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别DNS解析失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "DNS解析失败", "priority": "中"}, {"case_id": "BNPL_144", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "DNS解析失败异常恢复场景", "operation": "在DNS解析失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "DNS解析失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "DNS解析失败_恢复", "priority": "中"}, {"case_id": "BNPL_145", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "代理服务器异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发代理服务器异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别代理服务器异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "代理服务器异常", "priority": "中"}, {"case_id": "BNPL_146", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "代理服务器异常异常恢复场景", "operation": "在代理服务器异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "代理服务器异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "代理服务器异常_恢复", "priority": "中"}, {"case_id": "BNPL_147", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "代理服务器异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发代理服务器异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别代理服务器异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "代理服务器异常", "priority": "中"}, {"case_id": "BNPL_148", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "代理服务器异常异常恢复场景", "operation": "在代理服务器异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "代理服务器异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "代理服务器异常_恢复", "priority": "中"}, {"case_id": "BNPL_149", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "代理服务器异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发代理服务器异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别代理服务器异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "代理服务器异常", "priority": "中"}, {"case_id": "BNPL_150", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "代理服务器异常异常恢复场景", "operation": "在代理服务器异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "代理服务器异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "代理服务器异常_恢复", "priority": "中"}, {"case_id": "BNPL_151", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "代理服务器异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发代理服务器异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别代理服务器异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "代理服务器异常", "priority": "中"}, {"case_id": "BNPL_152", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "代理服务器异常异常恢复场景", "operation": "在代理服务器异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "代理服务器异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "代理服务器异常_恢复", "priority": "中"}, {"case_id": "BNPL_153", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "CDN异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CDN异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CDN异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "CDN异常", "priority": "中"}, {"case_id": "BNPL_154", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "CDN异常异常恢复场景", "operation": "在CDN异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CDN异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "CDN异常_恢复", "priority": "中"}, {"case_id": "BNPL_155", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "CDN异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CDN异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CDN异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "CDN异常", "priority": "中"}, {"case_id": "BNPL_156", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "CDN异常异常恢复场景", "operation": "在CDN异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CDN异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "CDN异常_恢复", "priority": "中"}, {"case_id": "BNPL_157", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "CDN异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CDN异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CDN异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "CDN异常", "priority": "中"}, {"case_id": "BNPL_158", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "CDN异常异常恢复场景", "operation": "在CDN异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CDN异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "CDN异常_恢复", "priority": "中"}, {"case_id": "BNPL_159", "case_name": "网络异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "CDN异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CDN异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CDN异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "网络异常", "exception_type": "CDN异常", "priority": "中"}, {"case_id": "BNPL_160", "case_name": "网络异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "CDN异常异常恢复场景", "operation": "在CDN异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CDN异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "网络异常", "exception_type": "CDN异常_恢复", "priority": "中"}, {"case_id": "BNPL_161", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务商维护", "priority": "高"}, {"case_id": "BNPL_162", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常恢复场景", "operation": "在服务商维护异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务商维护_恢复", "priority": "中"}, {"case_id": "BNPL_163", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务商维护", "priority": "高"}, {"case_id": "BNPL_164", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务商维护异常恢复场景", "operation": "在服务商维护异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务商维护_恢复", "priority": "中"}, {"case_id": "BNPL_165", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务商维护", "priority": "高"}, {"case_id": "BNPL_166", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "服务商维护异常恢复场景", "operation": "在服务商维护异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务商维护_恢复", "priority": "中"}, {"case_id": "BNPL_167", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务商维护", "priority": "高"}, {"case_id": "BNPL_168", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "服务商维护异常恢复场景", "operation": "在服务商维护异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务商维护_恢复", "priority": "中"}, {"case_id": "BNPL_169", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "API调用失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发API调用失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "API调用失败", "priority": "高"}, {"case_id": "BNPL_170", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "API调用失败异常恢复场景", "operation": "在API调用失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "API调用失败_恢复", "priority": "中"}, {"case_id": "BNPL_171", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "API调用失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发API调用失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "API调用失败", "priority": "高"}, {"case_id": "BNPL_172", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "API调用失败异常恢复场景", "operation": "在API调用失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "API调用失败_恢复", "priority": "中"}, {"case_id": "BNPL_173", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "API调用失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发API调用失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "API调用失败", "priority": "高"}, {"case_id": "BNPL_174", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "API调用失败异常恢复场景", "operation": "在API调用失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "API调用失败_恢复", "priority": "中"}, {"case_id": "BNPL_175", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "API调用失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发API调用失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "API调用失败", "priority": "高"}, {"case_id": "BNPL_176", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "API调用失败异常恢复场景", "operation": "在API调用失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "API调用失败_恢复", "priority": "中"}, {"case_id": "BNPL_177", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据同步异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据同步异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据同步异常", "priority": "高"}, {"case_id": "BNPL_178", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据同步异常异常恢复场景", "operation": "在数据同步异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据同步异常_恢复", "priority": "中"}, {"case_id": "BNPL_179", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据同步异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据同步异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据同步异常", "priority": "高"}, {"case_id": "BNPL_180", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据同步异常异常恢复场景", "operation": "在数据同步异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据同步异常_恢复", "priority": "中"}, {"case_id": "BNPL_181", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据同步异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据同步异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据同步异常", "priority": "高"}, {"case_id": "BNPL_182", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据同步异常异常恢复场景", "operation": "在数据同步异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据同步异常_恢复", "priority": "中"}, {"case_id": "BNPL_183", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据同步异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据同步异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据同步异常", "priority": "高"}, {"case_id": "BNPL_184", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据同步异常异常恢复场景", "operation": "在数据同步异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据同步异常_恢复", "priority": "中"}, {"case_id": "BNPL_185", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "系统崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发系统崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "系统崩溃", "priority": "高"}, {"case_id": "BNPL_186", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "系统崩溃异常恢复场景", "operation": "在系统崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "系统崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_187", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "系统崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发系统崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "系统崩溃", "priority": "高"}, {"case_id": "BNPL_188", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "系统崩溃异常恢复场景", "operation": "在系统崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "系统崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_189", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "系统崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发系统崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "系统崩溃", "priority": "高"}, {"case_id": "BNPL_190", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "系统崩溃异常恢复场景", "operation": "在系统崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "系统崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_191", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "系统崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发系统崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "系统崩溃", "priority": "高"}, {"case_id": "BNPL_192", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "系统崩溃异常恢复场景", "operation": "在系统崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "系统崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_193", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务器过载异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务器过载异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务器过载异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务器过载", "priority": "高"}, {"case_id": "BNPL_194", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务器过载异常恢复场景", "operation": "在服务器过载异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务器过载异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务器过载_恢复", "priority": "中"}, {"case_id": "BNPL_195", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务器过载异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务器过载异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务器过载异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务器过载", "priority": "高"}, {"case_id": "BNPL_196", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务器过载异常恢复场景", "operation": "在服务器过载异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务器过载异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务器过载_恢复", "priority": "中"}, {"case_id": "BNPL_197", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "服务器过载异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务器过载异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务器过载异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务器过载", "priority": "高"}, {"case_id": "BNPL_198", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "服务器过载异常恢复场景", "operation": "在服务器过载异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务器过载异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务器过载_恢复", "priority": "中"}, {"case_id": "BNPL_199", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "服务器过载异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发服务器过载异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别服务器过载异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "服务器过载", "priority": "高"}, {"case_id": "BNPL_200", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "服务器过载异常恢复场景", "operation": "在服务器过载异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "服务器过载异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "服务器过载_恢复", "priority": "中"}, {"case_id": "BNPL_201", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据库异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据库异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据库异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据库异常", "priority": "高"}, {"case_id": "BNPL_202", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据库异常异常恢复场景", "operation": "在数据库异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据库异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据库异常_恢复", "priority": "中"}, {"case_id": "BNPL_203", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据库异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据库异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据库异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据库异常", "priority": "高"}, {"case_id": "BNPL_204", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据库异常异常恢复场景", "operation": "在数据库异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据库异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据库异常_恢复", "priority": "中"}, {"case_id": "BNPL_205", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据库异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据库异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据库异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据库异常", "priority": "高"}, {"case_id": "BNPL_206", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据库异常异常恢复场景", "operation": "在数据库异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据库异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据库异常_恢复", "priority": "中"}, {"case_id": "BNPL_207", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据库异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据库异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据库异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "数据库异常", "priority": "高"}, {"case_id": "BNPL_208", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据库异常异常恢复场景", "operation": "在数据库异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据库异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "数据库异常_恢复", "priority": "中"}, {"case_id": "BNPL_209", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "缓存失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发缓存失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别缓存失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "缓存失效", "priority": "高"}, {"case_id": "BNPL_210", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "缓存失效异常恢复场景", "operation": "在缓存失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "缓存失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "缓存失效_恢复", "priority": "中"}, {"case_id": "BNPL_211", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "缓存失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发缓存失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别缓存失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "缓存失效", "priority": "高"}, {"case_id": "BNPL_212", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "缓存失效异常恢复场景", "operation": "在缓存失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "缓存失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "缓存失效_恢复", "priority": "中"}, {"case_id": "BNPL_213", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "缓存失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发缓存失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别缓存失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "缓存失效", "priority": "高"}, {"case_id": "BNPL_214", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "缓存失效异常恢复场景", "operation": "在缓存失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "缓存失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "缓存失效_恢复", "priority": "中"}, {"case_id": "BNPL_215", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "缓存失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发缓存失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别缓存失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "缓存失效", "priority": "高"}, {"case_id": "BNPL_216", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "缓存失效异常恢复场景", "operation": "在缓存失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "缓存失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "缓存失效_恢复", "priority": "中"}, {"case_id": "BNPL_217", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "负载均衡异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发负载均衡异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别负载均衡异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "负载均衡异常", "priority": "高"}, {"case_id": "BNPL_218", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "负载均衡异常异常恢复场景", "operation": "在负载均衡异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "负载均衡异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "负载均衡异常_恢复", "priority": "中"}, {"case_id": "BNPL_219", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "负载均衡异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发负载均衡异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别负载均衡异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "负载均衡异常", "priority": "高"}, {"case_id": "BNPL_220", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "负载均衡异常异常恢复场景", "operation": "在负载均衡异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "负载均衡异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "负载均衡异常_恢复", "priority": "中"}, {"case_id": "BNPL_221", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "负载均衡异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发负载均衡异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别负载均衡异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "负载均衡异常", "priority": "高"}, {"case_id": "BNPL_222", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "负载均衡异常异常恢复场景", "operation": "在负载均衡异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "负载均衡异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "负载均衡异常_恢复", "priority": "中"}, {"case_id": "BNPL_223", "case_name": "系统异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "负载均衡异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发负载均衡异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别负载均衡异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "系统异常", "exception_type": "负载均衡异常", "priority": "高"}, {"case_id": "BNPL_224", "case_name": "系统异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "负载均衡异常异常恢复场景", "operation": "在负载均衡异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "负载均衡异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "系统异常", "exception_type": "负载均衡异常_恢复", "priority": "中"}, {"case_id": "BNPL_225", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付中断", "priority": "高"}, {"case_id": "BNPL_226", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常恢复场景", "operation": "在支付中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付中断_恢复", "priority": "中"}, {"case_id": "BNPL_227", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付中断", "priority": "高"}, {"case_id": "BNPL_228", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付中断异常恢复场景", "operation": "在支付中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付中断_恢复", "priority": "中"}, {"case_id": "BNPL_229", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付中断", "priority": "高"}, {"case_id": "BNPL_230", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付中断异常恢复场景", "operation": "在支付中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付中断_恢复", "priority": "中"}, {"case_id": "BNPL_231", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付中断", "priority": "高"}, {"case_id": "BNPL_232", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付中断异常恢复场景", "operation": "在支付中断异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付中断_恢复", "priority": "中"}, {"case_id": "BNPL_233", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付超时", "priority": "高"}, {"case_id": "BNPL_234", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付超时异常恢复场景", "operation": "在支付超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付超时_恢复", "priority": "中"}, {"case_id": "BNPL_235", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付超时", "priority": "高"}, {"case_id": "BNPL_236", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付超时异常恢复场景", "operation": "在支付超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付超时_恢复", "priority": "中"}, {"case_id": "BNPL_237", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付超时", "priority": "高"}, {"case_id": "BNPL_238", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付超时异常恢复场景", "operation": "在支付超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付超时_恢复", "priority": "中"}, {"case_id": "BNPL_239", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付超时", "priority": "高"}, {"case_id": "BNPL_240", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付超时异常恢复场景", "operation": "在支付超时异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付超时_恢复", "priority": "中"}, {"case_id": "BNPL_241", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付取消异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付取消异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付取消", "priority": "高"}, {"case_id": "BNPL_242", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付取消异常恢复场景", "operation": "在支付取消异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付取消_恢复", "priority": "中"}, {"case_id": "BNPL_243", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付取消异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付取消异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付取消", "priority": "高"}, {"case_id": "BNPL_244", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付取消异常恢复场景", "operation": "在支付取消异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付取消_恢复", "priority": "中"}, {"case_id": "BNPL_245", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付取消异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付取消异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付取消", "priority": "高"}, {"case_id": "BNPL_246", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付取消异常恢复场景", "operation": "在支付取消异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付取消_恢复", "priority": "中"}, {"case_id": "BNPL_247", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付取消异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付取消异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付取消", "priority": "高"}, {"case_id": "BNPL_248", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付取消异常恢复场景", "operation": "在支付取消异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付取消_恢复", "priority": "中"}, {"case_id": "BNPL_249", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "余额不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发余额不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "余额不足", "priority": "高"}, {"case_id": "BNPL_250", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "余额不足异常恢复场景", "operation": "在余额不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "余额不足_恢复", "priority": "中"}, {"case_id": "BNPL_251", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "余额不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发余额不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "余额不足", "priority": "高"}, {"case_id": "BNPL_252", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "余额不足异常恢复场景", "operation": "在余额不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "余额不足_恢复", "priority": "中"}, {"case_id": "BNPL_253", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "余额不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发余额不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "余额不足", "priority": "高"}, {"case_id": "BNPL_254", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "余额不足异常恢复场景", "operation": "在余额不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "余额不足_恢复", "priority": "中"}, {"case_id": "BNPL_255", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "余额不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发余额不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "余额不足", "priority": "高"}, {"case_id": "BNPL_256", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "余额不足异常恢复场景", "operation": "在余额不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "余额不足_恢复", "priority": "中"}, {"case_id": "BNPL_257", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付限额异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付限额异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付限额异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付限额", "priority": "高"}, {"case_id": "BNPL_258", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付限额异常恢复场景", "operation": "在支付限额异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付限额异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付限额_恢复", "priority": "中"}, {"case_id": "BNPL_259", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付限额异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付限额异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付限额异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付限额", "priority": "高"}, {"case_id": "BNPL_260", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付限额异常恢复场景", "operation": "在支付限额异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付限额异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付限额_恢复", "priority": "中"}, {"case_id": "BNPL_261", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付限额异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付限额异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付限额异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付限额", "priority": "高"}, {"case_id": "BNPL_262", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付限额异常恢复场景", "operation": "在支付限额异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付限额异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付限额_恢复", "priority": "中"}, {"case_id": "BNPL_263", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付限额异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付限额异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付限额异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付限额", "priority": "高"}, {"case_id": "BNPL_264", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付限额异常恢复场景", "operation": "在支付限额异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付限额异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付限额_恢复", "priority": "中"}, {"case_id": "BNPL_265", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付频率限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付频率限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付频率限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付频率限制", "priority": "高"}, {"case_id": "BNPL_266", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付频率限制异常恢复场景", "operation": "在支付频率限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付频率限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付频率限制_恢复", "priority": "中"}, {"case_id": "BNPL_267", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付频率限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付频率限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付频率限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付频率限制", "priority": "高"}, {"case_id": "BNPL_268", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付频率限制异常恢复场景", "operation": "在支付频率限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付频率限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付频率限制_恢复", "priority": "中"}, {"case_id": "BNPL_269", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付频率限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付频率限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付频率限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付频率限制", "priority": "高"}, {"case_id": "BNPL_270", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付频率限制异常恢复场景", "operation": "在支付频率限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付频率限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付频率限制_恢复", "priority": "中"}, {"case_id": "BNPL_271", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付频率限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付频率限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付频率限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付频率限制", "priority": "高"}, {"case_id": "BNPL_272", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付频率限制异常恢复场景", "operation": "在支付频率限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付频率限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付频率限制_恢复", "priority": "中"}, {"case_id": "BNPL_273", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付渠道异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付渠道异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付渠道异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付渠道异常", "priority": "高"}, {"case_id": "BNPL_274", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付渠道异常异常恢复场景", "operation": "在支付渠道异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付渠道异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付渠道异常_恢复", "priority": "中"}, {"case_id": "BNPL_275", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付渠道异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付渠道异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付渠道异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付渠道异常", "priority": "高"}, {"case_id": "BNPL_276", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付渠道异常异常恢复场景", "operation": "在支付渠道异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付渠道异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付渠道异常_恢复", "priority": "中"}, {"case_id": "BNPL_277", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付渠道异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付渠道异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付渠道异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付渠道异常", "priority": "高"}, {"case_id": "BNPL_278", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付渠道异常异常恢复场景", "operation": "在支付渠道异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付渠道异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付渠道异常_恢复", "priority": "中"}, {"case_id": "BNPL_279", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付渠道异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发支付渠道异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别支付渠道异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "支付渠道异常", "priority": "高"}, {"case_id": "BNPL_280", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付渠道异常异常恢复场景", "operation": "在支付渠道异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "支付渠道异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "支付渠道异常_恢复", "priority": "中"}, {"case_id": "BNPL_281", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "分期方案异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发分期方案异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别分期方案异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "分期方案异常", "priority": "高"}, {"case_id": "BNPL_282", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "分期方案异常异常恢复场景", "operation": "在分期方案异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "分期方案异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "分期方案异常_恢复", "priority": "中"}, {"case_id": "BNPL_283", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "分期方案异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发分期方案异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别分期方案异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "分期方案异常", "priority": "高"}, {"case_id": "BNPL_284", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "分期方案异常异常恢复场景", "operation": "在分期方案异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "分期方案异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "分期方案异常_恢复", "priority": "中"}, {"case_id": "BNPL_285", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "分期方案异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发分期方案异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别分期方案异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "分期方案异常", "priority": "高"}, {"case_id": "BNPL_286", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "分期方案异常异常恢复场景", "operation": "在分期方案异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "分期方案异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "分期方案异常_恢复", "priority": "中"}, {"case_id": "BNPL_287", "case_name": "支付异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "分期方案异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发分期方案异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别分期方案异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "支付异常", "exception_type": "分期方案异常", "priority": "高"}, {"case_id": "BNPL_288", "case_name": "支付异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "分期方案异常异常恢复场景", "operation": "在分期方案异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "分期方案异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "支付异常", "exception_type": "分期方案异常_恢复", "priority": "中"}, {"case_id": "BNPL_289", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品缺货异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品缺货异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品缺货", "priority": "中"}, {"case_id": "BNPL_290", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品缺货异常恢复场景", "operation": "在商品缺货异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品缺货_恢复", "priority": "中"}, {"case_id": "BNPL_291", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品缺货异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品缺货异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品缺货", "priority": "中"}, {"case_id": "BNPL_292", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品缺货异常恢复场景", "operation": "在商品缺货异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品缺货_恢复", "priority": "中"}, {"case_id": "BNPL_293", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品缺货异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品缺货异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品缺货", "priority": "中"}, {"case_id": "BNPL_294", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品缺货异常恢复场景", "operation": "在商品缺货异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品缺货_恢复", "priority": "中"}, {"case_id": "BNPL_295", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品缺货异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品缺货异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品缺货", "priority": "中"}, {"case_id": "BNPL_296", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品缺货异常恢复场景", "operation": "在商品缺货异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品缺货_恢复", "priority": "中"}, {"case_id": "BNPL_297", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "价格变动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发价格变动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "价格变动", "priority": "中"}, {"case_id": "BNPL_298", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "价格变动异常恢复场景", "operation": "在价格变动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "价格变动_恢复", "priority": "中"}, {"case_id": "BNPL_299", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "价格变动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发价格变动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "价格变动", "priority": "中"}, {"case_id": "BNPL_300", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "价格变动异常恢复场景", "operation": "在价格变动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "价格变动_恢复", "priority": "中"}, {"case_id": "BNPL_301", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "价格变动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发价格变动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "价格变动", "priority": "中"}, {"case_id": "BNPL_302", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "价格变动异常恢复场景", "operation": "在价格变动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "价格变动_恢复", "priority": "中"}, {"case_id": "BNPL_303", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "价格变动异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发价格变动异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "价格变动", "priority": "中"}, {"case_id": "BNPL_304", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "价格变动异常恢复场景", "operation": "在价格变动异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "价格变动_恢复", "priority": "中"}, {"case_id": "BNPL_305", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "库存不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发库存不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "库存不足", "priority": "中"}, {"case_id": "BNPL_306", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "库存不足异常恢复场景", "operation": "在库存不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "库存不足_恢复", "priority": "中"}, {"case_id": "BNPL_307", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "库存不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发库存不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "库存不足", "priority": "中"}, {"case_id": "BNPL_308", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "库存不足异常恢复场景", "operation": "在库存不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "库存不足_恢复", "priority": "中"}, {"case_id": "BNPL_309", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "库存不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发库存不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "库存不足", "priority": "中"}, {"case_id": "BNPL_310", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "库存不足异常恢复场景", "operation": "在库存不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "库存不足_恢复", "priority": "中"}, {"case_id": "BNPL_311", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "库存不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发库存不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "库存不足", "priority": "中"}, {"case_id": "BNPL_312", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "库存不足异常恢复场景", "operation": "在库存不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "库存不足_恢复", "priority": "中"}, {"case_id": "BNPL_313", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "活动过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发活动过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "活动过期", "priority": "中"}, {"case_id": "BNPL_314", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "活动过期异常恢复场景", "operation": "在活动过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "活动过期_恢复", "priority": "中"}, {"case_id": "BNPL_315", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "活动过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发活动过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "活动过期", "priority": "中"}, {"case_id": "BNPL_316", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "活动过期异常恢复场景", "operation": "在活动过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "活动过期_恢复", "priority": "中"}, {"case_id": "BNPL_317", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "活动过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发活动过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "活动过期", "priority": "中"}, {"case_id": "BNPL_318", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "活动过期异常恢复场景", "operation": "在活动过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "活动过期_恢复", "priority": "中"}, {"case_id": "BNPL_319", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "活动过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发活动过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "活动过期", "priority": "中"}, {"case_id": "BNPL_320", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "活动过期异常恢复场景", "operation": "在活动过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "活动过期_恢复", "priority": "中"}, {"case_id": "BNPL_321", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "权限不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "权限不足", "priority": "中"}, {"case_id": "BNPL_322", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "权限不足异常恢复场景", "operation": "在权限不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "权限不足_恢复", "priority": "中"}, {"case_id": "BNPL_323", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "权限不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "权限不足", "priority": "中"}, {"case_id": "BNPL_324", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "权限不足异常恢复场景", "operation": "在权限不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "权限不足_恢复", "priority": "中"}, {"case_id": "BNPL_325", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "权限不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "权限不足", "priority": "中"}, {"case_id": "BNPL_326", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "权限不足异常恢复场景", "operation": "在权限不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "权限不足_恢复", "priority": "中"}, {"case_id": "BNPL_327", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "权限不足异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限不足异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限不足异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "权限不足", "priority": "中"}, {"case_id": "BNPL_328", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "权限不足异常恢复场景", "operation": "在权限不足异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限不足异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "权限不足_恢复", "priority": "中"}, {"case_id": "BNPL_329", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品下架异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品下架异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品下架异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品下架", "priority": "中"}, {"case_id": "BNPL_330", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品下架异常恢复场景", "operation": "在商品下架异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品下架异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品下架_恢复", "priority": "中"}, {"case_id": "BNPL_331", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品下架异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品下架异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品下架异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品下架", "priority": "中"}, {"case_id": "BNPL_332", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品下架异常恢复场景", "operation": "在商品下架异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品下架异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品下架_恢复", "priority": "中"}, {"case_id": "BNPL_333", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品下架异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品下架异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品下架异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品下架", "priority": "中"}, {"case_id": "BNPL_334", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品下架异常恢复场景", "operation": "在商品下架异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品下架异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品下架_恢复", "priority": "中"}, {"case_id": "BNPL_335", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品下架异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发商品下架异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别商品下架异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "商品下架", "priority": "中"}, {"case_id": "BNPL_336", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品下架异常恢复场景", "operation": "在商品下架异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "商品下架异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "商品下架_恢复", "priority": "中"}, {"case_id": "BNPL_337", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "地区限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发地区限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别地区限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "地区限制", "priority": "中"}, {"case_id": "BNPL_338", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "地区限制异常恢复场景", "operation": "在地区限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "地区限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "地区限制_恢复", "priority": "中"}, {"case_id": "BNPL_339", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "地区限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发地区限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别地区限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "地区限制", "priority": "中"}, {"case_id": "BNPL_340", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "地区限制异常恢复场景", "operation": "在地区限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "地区限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "地区限制_恢复", "priority": "中"}, {"case_id": "BNPL_341", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "地区限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发地区限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别地区限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "地区限制", "priority": "中"}, {"case_id": "BNPL_342", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "地区限制异常恢复场景", "operation": "在地区限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "地区限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "地区限制_恢复", "priority": "中"}, {"case_id": "BNPL_343", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "地区限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发地区限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别地区限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "地区限制", "priority": "中"}, {"case_id": "BNPL_344", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "地区限制异常恢复场景", "operation": "在地区限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "地区限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "地区限制_恢复", "priority": "中"}, {"case_id": "BNPL_345", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "年龄限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发年龄限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别年龄限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "年龄限制", "priority": "中"}, {"case_id": "BNPL_346", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "年龄限制异常恢复场景", "operation": "在年龄限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "年龄限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "年龄限制_恢复", "priority": "中"}, {"case_id": "BNPL_347", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "年龄限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发年龄限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别年龄限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "年龄限制", "priority": "中"}, {"case_id": "BNPL_348", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "年龄限制异常恢复场景", "operation": "在年龄限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "年龄限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "年龄限制_恢复", "priority": "中"}, {"case_id": "BNPL_349", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "年龄限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发年龄限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别年龄限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "年龄限制", "priority": "中"}, {"case_id": "BNPL_350", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "年龄限制异常恢复场景", "operation": "在年龄限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "年龄限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "年龄限制_恢复", "priority": "中"}, {"case_id": "BNPL_351", "case_name": "业务异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "年龄限制异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发年龄限制异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别年龄限制异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "业务异常", "exception_type": "年龄限制", "priority": "中"}, {"case_id": "BNPL_352", "case_name": "业务异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "年龄限制异常恢复场景", "operation": "在年龄限制异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "年龄限制异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "业务异常", "exception_type": "年龄限制_恢复", "priority": "中"}, {"case_id": "BNPL_353", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_354", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "浏览器关闭异常恢复场景", "operation": "在浏览器关闭异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭_恢复", "priority": "中"}, {"case_id": "BNPL_355", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_356", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "浏览器关闭异常恢复场景", "operation": "在浏览器关闭异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭_恢复", "priority": "中"}, {"case_id": "BNPL_357", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_358", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "浏览器关闭异常恢复场景", "operation": "在浏览器关闭异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭_恢复", "priority": "中"}, {"case_id": "BNPL_359", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_360", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "浏览器关闭异常恢复场景", "operation": "在浏览器关闭异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "浏览器关闭_恢复", "priority": "中"}, {"case_id": "BNPL_361", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_362", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "应用崩溃异常恢复场景", "operation": "在应用崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_363", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_364", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "应用崩溃异常恢复场景", "operation": "在应用崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_365", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_366", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "应用崩溃异常恢复场景", "operation": "在应用崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_367", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_368", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "应用崩溃异常恢复场景", "operation": "在应用崩溃异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "应用崩溃_恢复", "priority": "中"}, {"case_id": "BNPL_369", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重复操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重复操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "重复操作", "priority": "中"}, {"case_id": "BNPL_370", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重复操作异常恢复场景", "operation": "在重复操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "重复操作_恢复", "priority": "中"}, {"case_id": "BNPL_371", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "重复操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重复操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "重复操作", "priority": "中"}, {"case_id": "BNPL_372", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "重复操作异常恢复场景", "operation": "在重复操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "重复操作_恢复", "priority": "中"}, {"case_id": "BNPL_373", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "重复操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重复操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "重复操作", "priority": "中"}, {"case_id": "BNPL_374", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "重复操作异常恢复场景", "operation": "在重复操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "重复操作_恢复", "priority": "中"}, {"case_id": "BNPL_375", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "重复操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重复操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "重复操作", "priority": "中"}, {"case_id": "BNPL_376", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "重复操作异常恢复场景", "operation": "在重复操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "重复操作_恢复", "priority": "中"}, {"case_id": "BNPL_377", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "非法操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发非法操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "非法操作", "priority": "中"}, {"case_id": "BNPL_378", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "非法操作异常恢复场景", "operation": "在非法操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "非法操作_恢复", "priority": "中"}, {"case_id": "BNPL_379", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "非法操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发非法操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "非法操作", "priority": "中"}, {"case_id": "BNPL_380", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "非法操作异常恢复场景", "operation": "在非法操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "非法操作_恢复", "priority": "中"}, {"case_id": "BNPL_381", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "非法操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发非法操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "非法操作", "priority": "中"}, {"case_id": "BNPL_382", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "非法操作异常恢复场景", "operation": "在非法操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "非法操作_恢复", "priority": "中"}, {"case_id": "BNPL_383", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "非法操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发非法操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "非法操作", "priority": "中"}, {"case_id": "BNPL_384", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "非法操作异常恢复场景", "operation": "在非法操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "非法操作_恢复", "priority": "中"}, {"case_id": "BNPL_385", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "快速点击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发快速点击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别快速点击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "快速点击", "priority": "中"}, {"case_id": "BNPL_386", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "快速点击异常恢复场景", "operation": "在快速点击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "快速点击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "快速点击_恢复", "priority": "中"}, {"case_id": "BNPL_387", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "快速点击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发快速点击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别快速点击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "快速点击", "priority": "中"}, {"case_id": "BNPL_388", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "快速点击异常恢复场景", "operation": "在快速点击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "快速点击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "快速点击_恢复", "priority": "中"}, {"case_id": "BNPL_389", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "快速点击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发快速点击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别快速点击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "快速点击", "priority": "中"}, {"case_id": "BNPL_390", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "快速点击异常恢复场景", "operation": "在快速点击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "快速点击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "快速点击_恢复", "priority": "中"}, {"case_id": "BNPL_391", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "快速点击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发快速点击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别快速点击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "快速点击", "priority": "中"}, {"case_id": "BNPL_392", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "快速点击异常恢复场景", "operation": "在快速点击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "快速点击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "快速点击_恢复", "priority": "中"}, {"case_id": "BNPL_393", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "页面刷新异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发页面刷新异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别页面刷新异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "页面刷新", "priority": "中"}, {"case_id": "BNPL_394", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "页面刷新异常恢复场景", "operation": "在页面刷新异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "页面刷新异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "页面刷新_恢复", "priority": "中"}, {"case_id": "BNPL_395", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "页面刷新异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发页面刷新异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别页面刷新异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "页面刷新", "priority": "中"}, {"case_id": "BNPL_396", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "页面刷新异常恢复场景", "operation": "在页面刷新异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "页面刷新异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "页面刷新_恢复", "priority": "中"}, {"case_id": "BNPL_397", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "页面刷新异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发页面刷新异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别页面刷新异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "页面刷新", "priority": "中"}, {"case_id": "BNPL_398", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "页面刷新异常恢复场景", "operation": "在页面刷新异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "页面刷新异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "页面刷新_恢复", "priority": "中"}, {"case_id": "BNPL_399", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "页面刷新异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发页面刷新异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别页面刷新异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "页面刷新", "priority": "中"}, {"case_id": "BNPL_400", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "页面刷新异常恢复场景", "operation": "在页面刷新异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "页面刷新异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "页面刷新_恢复", "priority": "中"}, {"case_id": "BNPL_401", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "返回操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发返回操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别返回操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "返回操作", "priority": "中"}, {"case_id": "BNPL_402", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "返回操作异常恢复场景", "operation": "在返回操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "返回操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "返回操作_恢复", "priority": "中"}, {"case_id": "BNPL_403", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "返回操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发返回操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别返回操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "返回操作", "priority": "中"}, {"case_id": "BNPL_404", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "返回操作异常恢复场景", "operation": "在返回操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "返回操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "返回操作_恢复", "priority": "中"}, {"case_id": "BNPL_405", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "返回操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发返回操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别返回操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "返回操作", "priority": "中"}, {"case_id": "BNPL_406", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "返回操作异常恢复场景", "operation": "在返回操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "返回操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "返回操作_恢复", "priority": "中"}, {"case_id": "BNPL_407", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "返回操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发返回操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别返回操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "返回操作", "priority": "中"}, {"case_id": "BNPL_408", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "返回操作异常恢复场景", "operation": "在返回操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "返回操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "返回操作_恢复", "priority": "中"}, {"case_id": "BNPL_409", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "多窗口操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发多窗口操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别多窗口操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作", "priority": "中"}, {"case_id": "BNPL_410", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "多窗口操作异常恢复场景", "operation": "在多窗口操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "多窗口操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作_恢复", "priority": "中"}, {"case_id": "BNPL_411", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "多窗口操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发多窗口操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别多窗口操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作", "priority": "中"}, {"case_id": "BNPL_412", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "多窗口操作异常恢复场景", "operation": "在多窗口操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "多窗口操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作_恢复", "priority": "中"}, {"case_id": "BNPL_413", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "多窗口操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发多窗口操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别多窗口操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作", "priority": "中"}, {"case_id": "BNPL_414", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "多窗口操作异常恢复场景", "operation": "在多窗口操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "多窗口操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作_恢复", "priority": "中"}, {"case_id": "BNPL_415", "case_name": "用户操作异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "多窗口操作异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发多窗口操作异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别多窗口操作异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作", "priority": "中"}, {"case_id": "BNPL_416", "case_name": "用户操作异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "多窗口操作异常恢复场景", "operation": "在多窗口操作异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "多窗口操作异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "用户操作异常", "exception_type": "多窗口操作_恢复", "priority": "中"}, {"case_id": "BNPL_417", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据篡改异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据篡改异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据篡改异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据篡改", "priority": "中"}, {"case_id": "BNPL_418", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据篡改异常恢复场景", "operation": "在数据篡改异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据篡改异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据篡改_恢复", "priority": "中"}, {"case_id": "BNPL_419", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据篡改异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据篡改异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据篡改异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据篡改", "priority": "中"}, {"case_id": "BNPL_420", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据篡改异常恢复场景", "operation": "在数据篡改异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据篡改异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据篡改_恢复", "priority": "中"}, {"case_id": "BNPL_421", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据篡改异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据篡改异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据篡改异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据篡改", "priority": "中"}, {"case_id": "BNPL_422", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据篡改异常恢复场景", "operation": "在数据篡改异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据篡改异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据篡改_恢复", "priority": "中"}, {"case_id": "BNPL_423", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据篡改异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据篡改异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据篡改异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据篡改", "priority": "中"}, {"case_id": "BNPL_424", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据篡改异常恢复场景", "operation": "在数据篡改异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据篡改异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据篡改_恢复", "priority": "中"}, {"case_id": "BNPL_425", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据丢失异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据丢失异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据丢失异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据丢失", "priority": "中"}, {"case_id": "BNPL_426", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据丢失异常恢复场景", "operation": "在数据丢失异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据丢失异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据丢失_恢复", "priority": "中"}, {"case_id": "BNPL_427", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据丢失异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据丢失异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据丢失异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据丢失", "priority": "中"}, {"case_id": "BNPL_428", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据丢失异常恢复场景", "operation": "在数据丢失异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据丢失异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据丢失_恢复", "priority": "中"}, {"case_id": "BNPL_429", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据丢失异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据丢失异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据丢失异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据丢失", "priority": "中"}, {"case_id": "BNPL_430", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据丢失异常恢复场景", "operation": "在数据丢失异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据丢失异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据丢失_恢复", "priority": "中"}, {"case_id": "BNPL_431", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据丢失异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据丢失异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据丢失异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据丢失", "priority": "中"}, {"case_id": "BNPL_432", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据丢失异常恢复场景", "operation": "在数据丢失异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据丢失异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据丢失_恢复", "priority": "中"}, {"case_id": "BNPL_433", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据不一致异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据不一致异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据不一致异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据不一致", "priority": "中"}, {"case_id": "BNPL_434", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据不一致异常恢复场景", "operation": "在数据不一致异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据不一致异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据不一致_恢复", "priority": "中"}, {"case_id": "BNPL_435", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据不一致异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据不一致异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据不一致异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据不一致", "priority": "中"}, {"case_id": "BNPL_436", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据不一致异常恢复场景", "operation": "在数据不一致异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据不一致异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据不一致_恢复", "priority": "中"}, {"case_id": "BNPL_437", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据不一致异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据不一致异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据不一致异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据不一致", "priority": "中"}, {"case_id": "BNPL_438", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据不一致异常恢复场景", "operation": "在数据不一致异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据不一致异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据不一致_恢复", "priority": "中"}, {"case_id": "BNPL_439", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据不一致异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据不一致异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据不一致异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据不一致", "priority": "中"}, {"case_id": "BNPL_440", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据不一致异常恢复场景", "operation": "在数据不一致异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据不一致异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据不一致_恢复", "priority": "中"}, {"case_id": "BNPL_441", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "格式错误异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发格式错误异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别格式错误异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "格式错误", "priority": "中"}, {"case_id": "BNPL_442", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "格式错误异常恢复场景", "operation": "在格式错误异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "格式错误异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "格式错误_恢复", "priority": "中"}, {"case_id": "BNPL_443", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "格式错误异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发格式错误异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别格式错误异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "格式错误", "priority": "中"}, {"case_id": "BNPL_444", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "格式错误异常恢复场景", "operation": "在格式错误异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "格式错误异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "格式错误_恢复", "priority": "中"}, {"case_id": "BNPL_445", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "格式错误异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发格式错误异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别格式错误异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "格式错误", "priority": "中"}, {"case_id": "BNPL_446", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "格式错误异常恢复场景", "operation": "在格式错误异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "格式错误异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "格式错误_恢复", "priority": "中"}, {"case_id": "BNPL_447", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "格式错误异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发格式错误异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别格式错误异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "格式错误", "priority": "中"}, {"case_id": "BNPL_448", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "格式错误异常恢复场景", "operation": "在格式错误异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "格式错误异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "格式错误_恢复", "priority": "中"}, {"case_id": "BNPL_449", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "编码异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发编码异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别编码异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "编码异常", "priority": "中"}, {"case_id": "BNPL_450", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "编码异常异常恢复场景", "operation": "在编码异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "编码异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "编码异常_恢复", "priority": "中"}, {"case_id": "BNPL_451", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "编码异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发编码异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别编码异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "编码异常", "priority": "中"}, {"case_id": "BNPL_452", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "编码异常异常恢复场景", "operation": "在编码异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "编码异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "编码异常_恢复", "priority": "中"}, {"case_id": "BNPL_453", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "编码异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发编码异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别编码异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "编码异常", "priority": "中"}, {"case_id": "BNPL_454", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "编码异常异常恢复场景", "operation": "在编码异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "编码异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "编码异常_恢复", "priority": "中"}, {"case_id": "BNPL_455", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "编码异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发编码异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别编码异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "编码异常", "priority": "中"}, {"case_id": "BNPL_456", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "编码异常异常恢复场景", "operation": "在编码异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "编码异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "编码异常_恢复", "priority": "中"}, {"case_id": "BNPL_457", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "字符集异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发字符集异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别字符集异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "字符集异常", "priority": "中"}, {"case_id": "BNPL_458", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "字符集异常异常恢复场景", "operation": "在字符集异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "字符集异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "字符集异常_恢复", "priority": "中"}, {"case_id": "BNPL_459", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "字符集异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发字符集异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别字符集异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "字符集异常", "priority": "中"}, {"case_id": "BNPL_460", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "字符集异常异常恢复场景", "operation": "在字符集异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "字符集异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "字符集异常_恢复", "priority": "中"}, {"case_id": "BNPL_461", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "字符集异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发字符集异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别字符集异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "字符集异常", "priority": "中"}, {"case_id": "BNPL_462", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "字符集异常异常恢复场景", "operation": "在字符集异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "字符集异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "字符集异常_恢复", "priority": "中"}, {"case_id": "BNPL_463", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "字符集异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发字符集异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别字符集异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "字符集异常", "priority": "中"}, {"case_id": "BNPL_464", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "字符集异常异常恢复场景", "operation": "在字符集异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "字符集异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "字符集异常_恢复", "priority": "中"}, {"case_id": "BNPL_465", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据溢出异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据溢出异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据溢出异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据溢出", "priority": "中"}, {"case_id": "BNPL_466", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据溢出异常恢复场景", "operation": "在数据溢出异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据溢出异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据溢出_恢复", "priority": "中"}, {"case_id": "BNPL_467", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据溢出异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据溢出异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据溢出异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据溢出", "priority": "中"}, {"case_id": "BNPL_468", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据溢出异常恢复场景", "operation": "在数据溢出异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据溢出异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据溢出_恢复", "priority": "中"}, {"case_id": "BNPL_469", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据溢出异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据溢出异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据溢出异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据溢出", "priority": "中"}, {"case_id": "BNPL_470", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "数据溢出异常恢复场景", "operation": "在数据溢出异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据溢出异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据溢出_恢复", "priority": "中"}, {"case_id": "BNPL_471", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据溢出异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发数据溢出异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别数据溢出异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "数据溢出", "priority": "中"}, {"case_id": "BNPL_472", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "数据溢出异常恢复场景", "operation": "在数据溢出异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "数据溢出异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "数据溢出_恢复", "priority": "中"}, {"case_id": "BNPL_473", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "空值异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发空值异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别空值异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "空值异常", "priority": "中"}, {"case_id": "BNPL_474", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "空值异常异常恢复场景", "operation": "在空值异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "空值异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "空值异常_恢复", "priority": "中"}, {"case_id": "BNPL_475", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "空值异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发空值异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别空值异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "空值异常", "priority": "中"}, {"case_id": "BNPL_476", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "空值异常异常恢复场景", "operation": "在空值异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "空值异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "空值异常_恢复", "priority": "中"}, {"case_id": "BNPL_477", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "空值异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发空值异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别空值异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "空值异常", "priority": "中"}, {"case_id": "BNPL_478", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "空值异常异常恢复场景", "operation": "在空值异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "空值异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "空值异常_恢复", "priority": "中"}, {"case_id": "BNPL_479", "case_name": "数据异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "空值异常异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发空值异常异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别空值异常异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "数据异常", "exception_type": "空值异常", "priority": "中"}, {"case_id": "BNPL_480", "case_name": "数据异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "空值异常异常恢复场景", "operation": "在空值异常异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "空值异常异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "数据异常", "exception_type": "空值异常_恢复", "priority": "中"}, {"case_id": "BNPL_481", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会话过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发会话过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别会话过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "会话过期", "priority": "中"}, {"case_id": "BNPL_482", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会话过期异常恢复场景", "operation": "在会话过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "会话过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "会话过期_恢复", "priority": "中"}, {"case_id": "BNPL_483", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会话过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发会话过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别会话过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "会话过期", "priority": "中"}, {"case_id": "BNPL_484", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会话过期异常恢复场景", "operation": "在会话过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "会话过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "会话过期_恢复", "priority": "中"}, {"case_id": "BNPL_485", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "会话过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发会话过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别会话过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "会话过期", "priority": "中"}, {"case_id": "BNPL_486", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "会话过期异常恢复场景", "operation": "在会话过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "会话过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "会话过期_恢复", "priority": "中"}, {"case_id": "BNPL_487", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "会话过期异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发会话过期异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别会话过期异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "会话过期", "priority": "中"}, {"case_id": "BNPL_488", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "会话过期异常恢复场景", "operation": "在会话过期异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "会话过期异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "会话过期_恢复", "priority": "中"}, {"case_id": "BNPL_489", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "权限验证失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限验证失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限验证失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "权限验证失败", "priority": "中"}, {"case_id": "BNPL_490", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "权限验证失败异常恢复场景", "operation": "在权限验证失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限验证失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "权限验证失败_恢复", "priority": "中"}, {"case_id": "BNPL_491", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "权限验证失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限验证失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限验证失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "权限验证失败", "priority": "中"}, {"case_id": "BNPL_492", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "权限验证失败异常恢复场景", "operation": "在权限验证失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限验证失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "权限验证失败_恢复", "priority": "中"}, {"case_id": "BNPL_493", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "权限验证失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限验证失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限验证失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "权限验证失败", "priority": "中"}, {"case_id": "BNPL_494", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "权限验证失败异常恢复场景", "operation": "在权限验证失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限验证失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "权限验证失败_恢复", "priority": "中"}, {"case_id": "BNPL_495", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "权限验证失败异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发权限验证失败异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别权限验证失败异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "权限验证失败", "priority": "中"}, {"case_id": "BNPL_496", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "权限验证失败异常恢复场景", "operation": "在权限验证失败异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "权限验证失败异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "权限验证失败_恢复", "priority": "中"}, {"case_id": "BNPL_497", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "跨站请求异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发跨站请求异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别跨站请求异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "跨站请求", "priority": "中"}, {"case_id": "BNPL_498", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "跨站请求异常恢复场景", "operation": "在跨站请求异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "跨站请求异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "跨站请求_恢复", "priority": "中"}, {"case_id": "BNPL_499", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "跨站请求异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发跨站请求异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别跨站请求异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "跨站请求", "priority": "中"}, {"case_id": "BNPL_500", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "跨站请求异常恢复场景", "operation": "在跨站请求异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "跨站请求异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "跨站请求_恢复", "priority": "中"}, {"case_id": "BNPL_501", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "跨站请求异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发跨站请求异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别跨站请求异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "跨站请求", "priority": "中"}, {"case_id": "BNPL_502", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "跨站请求异常恢复场景", "operation": "在跨站请求异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "跨站请求异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "跨站请求_恢复", "priority": "中"}, {"case_id": "BNPL_503", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "跨站请求异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发跨站请求异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别跨站请求异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "跨站请求", "priority": "中"}, {"case_id": "BNPL_504", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "跨站请求异常恢复场景", "operation": "在跨站请求异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "跨站请求异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "跨站请求_恢复", "priority": "中"}, {"case_id": "BNPL_505", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重放攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重放攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重放攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "重放攻击", "priority": "中"}, {"case_id": "BNPL_506", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "重放攻击异常恢复场景", "operation": "在重放攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重放攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "重放攻击_恢复", "priority": "中"}, {"case_id": "BNPL_507", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "重放攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重放攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重放攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "重放攻击", "priority": "中"}, {"case_id": "BNPL_508", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "重放攻击异常恢复场景", "operation": "在重放攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重放攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "重放攻击_恢复", "priority": "中"}, {"case_id": "BNPL_509", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "重放攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重放攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重放攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "重放攻击", "priority": "中"}, {"case_id": "BNPL_510", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "重放攻击异常恢复场景", "operation": "在重放攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重放攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "重放攻击_恢复", "priority": "中"}, {"case_id": "BNPL_511", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "重放攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发重放攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别重放攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "重放攻击", "priority": "中"}, {"case_id": "BNPL_512", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "重放攻击异常恢复场景", "operation": "在重放攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "重放攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "重放攻击_恢复", "priority": "中"}, {"case_id": "BNPL_513", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "SQL注入异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发SQL注入异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别SQL注入异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "SQL注入", "priority": "中"}, {"case_id": "BNPL_514", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "SQL注入异常恢复场景", "operation": "在SQL注入异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "SQL注入异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "SQL注入_恢复", "priority": "中"}, {"case_id": "BNPL_515", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "SQL注入异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发SQL注入异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别SQL注入异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "SQL注入", "priority": "中"}, {"case_id": "BNPL_516", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "SQL注入异常恢复场景", "operation": "在SQL注入异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "SQL注入异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "SQL注入_恢复", "priority": "中"}, {"case_id": "BNPL_517", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "SQL注入异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发SQL注入异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别SQL注入异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "SQL注入", "priority": "中"}, {"case_id": "BNPL_518", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "SQL注入异常恢复场景", "operation": "在SQL注入异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "SQL注入异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "SQL注入_恢复", "priority": "中"}, {"case_id": "BNPL_519", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "SQL注入异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发SQL注入异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别SQL注入异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "SQL注入", "priority": "中"}, {"case_id": "BNPL_520", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "SQL注入异常恢复场景", "operation": "在SQL注入异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "SQL注入异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "SQL注入_恢复", "priority": "中"}, {"case_id": "BNPL_521", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "XSS攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发XSS攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别XSS攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "XSS攻击", "priority": "中"}, {"case_id": "BNPL_522", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "XSS攻击异常恢复场景", "operation": "在XSS攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "XSS攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "XSS攻击_恢复", "priority": "中"}, {"case_id": "BNPL_523", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "XSS攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发XSS攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别XSS攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "XSS攻击", "priority": "中"}, {"case_id": "BNPL_524", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "XSS攻击异常恢复场景", "operation": "在XSS攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "XSS攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "XSS攻击_恢复", "priority": "中"}, {"case_id": "BNPL_525", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "XSS攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发XSS攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别XSS攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "XSS攻击", "priority": "中"}, {"case_id": "BNPL_526", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "XSS攻击异常恢复场景", "operation": "在XSS攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "XSS攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "XSS攻击_恢复", "priority": "中"}, {"case_id": "BNPL_527", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "XSS攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发XSS攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别XSS攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "XSS攻击", "priority": "中"}, {"case_id": "BNPL_528", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "XSS攻击异常恢复场景", "operation": "在XSS攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "XSS攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "XSS攻击_恢复", "priority": "中"}, {"case_id": "BNPL_529", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "CSRF攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CSRF攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CSRF攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "CSRF攻击", "priority": "中"}, {"case_id": "BNPL_530", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "CSRF攻击异常恢复场景", "operation": "在CSRF攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CSRF攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "CSRF攻击_恢复", "priority": "中"}, {"case_id": "BNPL_531", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "CSRF攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CSRF攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CSRF攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "CSRF攻击", "priority": "中"}, {"case_id": "BNPL_532", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "CSRF攻击异常恢复场景", "operation": "在CSRF攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CSRF攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "CSRF攻击_恢复", "priority": "中"}, {"case_id": "BNPL_533", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "CSRF攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CSRF攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CSRF攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "CSRF攻击", "priority": "中"}, {"case_id": "BNPL_534", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "CSRF攻击异常恢复场景", "operation": "在CSRF攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CSRF攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "CSRF攻击_恢复", "priority": "中"}, {"case_id": "BNPL_535", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "CSRF攻击异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发CSRF攻击异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别CSRF攻击异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "CSRF攻击", "priority": "中"}, {"case_id": "BNPL_536", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "CSRF攻击异常恢复场景", "operation": "在CSRF攻击异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "CSRF攻击异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "CSRF攻击_恢复", "priority": "中"}, {"case_id": "BNPL_537", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "令牌失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发令牌失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别令牌失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "令牌失效", "priority": "中"}, {"case_id": "BNPL_538", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "令牌失效异常恢复场景", "operation": "在令牌失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "令牌失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "令牌失效_恢复", "priority": "中"}, {"case_id": "BNPL_539", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "令牌失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发令牌失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别令牌失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "令牌失效", "priority": "中"}, {"case_id": "BNPL_540", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "令牌失效异常恢复场景", "operation": "在令牌失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "令牌失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "令牌失效_恢复", "priority": "中"}, {"case_id": "BNPL_541", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "令牌失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发令牌失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别令牌失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "令牌失效", "priority": "中"}, {"case_id": "BNPL_542", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "令牌失效异常恢复场景", "operation": "在令牌失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "令牌失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "令牌失效_恢复", "priority": "中"}, {"case_id": "BNPL_543", "case_name": "安全异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "令牌失效异常场景", "operation": "正常进入BNPL支付流程", "action": "在支付过程中触发令牌失效异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确识别令牌失效异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"}, "exception_category": "安全异常", "exception_type": "令牌失效", "priority": "中"}, {"case_id": "BNPL_544", "case_name": "安全异常恢复", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "令牌失效异常恢复场景", "operation": "在令牌失效异常发生后", "action": "尝试恢复并重新进行BNPL支付", "sub_action": "验证异常恢复后的系统状态", "expected_result": "令牌失效异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"}, "exception_category": "安全异常", "exception_type": "令牌失效_恢复", "priority": "中"}, {"case_id": "BNPL_545", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "网络超时+支付中断组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发网络超时和支付中断异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理网络超时和支付中断组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "网络异常+支付异常", "priority": "高"}, {"case_id": "BNPL_546", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "网络超时+支付中断组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发网络超时和支付中断异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理网络超时和支付中断组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "网络异常+支付异常", "priority": "高"}, {"case_id": "BNPL_547", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时+支付中断组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发网络超时和支付中断异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理网络超时和支付中断组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "网络异常+支付异常", "priority": "高"}, {"case_id": "BNPL_548", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络超时+支付中断组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发网络超时和支付中断异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理网络超时和支付中断组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "网络异常+支付异常", "priority": "高"}, {"case_id": "BNPL_549", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "服务商维护+商品缺货组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发服务商维护和商品缺货异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理服务商维护和商品缺货组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "系统异常+业务异常", "priority": "高"}, {"case_id": "BNPL_550", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "服务商维护+商品缺货组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发服务商维护和商品缺货异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理服务商维护和商品缺货组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "系统异常+业务异常", "priority": "高"}, {"case_id": "BNPL_551", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护+商品缺货组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发服务商维护和商品缺货异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理服务商维护和商品缺货组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "系统异常+业务异常", "priority": "高"}, {"case_id": "BNPL_552", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务商维护+商品缺货组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发服务商维护和商品缺货异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理服务商维护和商品缺货组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "系统异常+业务异常", "priority": "高"}, {"case_id": "BNPL_553", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "浏览器关闭+数据篡改组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发浏览器关闭和数据篡改异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理浏览器关闭和数据篡改组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "用户操作异常+数据异常", "priority": "高"}, {"case_id": "BNPL_554", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "浏览器关闭+数据篡改组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发浏览器关闭和数据篡改异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理浏览器关闭和数据篡改组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "用户操作异常+数据异常", "priority": "高"}, {"case_id": "BNPL_555", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "浏览器关闭+数据篡改组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发浏览器关闭和数据篡改异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理浏览器关闭和数据篡改组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "用户操作异常+数据异常", "priority": "高"}, {"case_id": "BNPL_556", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "浏览器关闭+数据篡改组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发浏览器关闭和数据篡改异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理浏览器关闭和数据篡改组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "用户操作异常+数据异常", "priority": "高"}, {"case_id": "BNPL_557", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "会话过期+网络超时组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发会话过期和网络超时异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理会话过期和网络超时组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "安全异常+网络异常", "priority": "高"}, {"case_id": "BNPL_558", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "会话过期+网络超时组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发会话过期和网络超时异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理会话过期和网络超时组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "安全异常+网络异常", "priority": "高"}, {"case_id": "BNPL_559", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会话过期+网络超时组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发会话过期和网络超时异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理会话过期和网络超时组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "安全异常+网络异常", "priority": "高"}, {"case_id": "BNPL_560", "case_name": "组合异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会话过期+网络超时组合异常场景", "operation": "正常进入BNPL支付流程", "action": "同时触发会话过期和网络超时异常", "sub_action": "观察系统对组合异常的处理能力", "expected_result": "系统正确处理会话过期和网络超时组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"}, "exception_category": "安全异常+网络异常", "priority": "高"}]}, {"module_name": "BNPL边界值测试", "description": "验证各种边界条件下的系统处理", "test_cases": [{"case_id": "BNPL_561", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "时间边界测试", "operation": "在活动开始/结束时间点进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理时间边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "时间边界", "priority": "中"}, {"case_id": "BNPL_562", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "时间边界测试", "operation": "在活动开始/结束时间点进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理时间边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "时间边界", "priority": "中"}, {"case_id": "BNPL_563", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "时间边界测试", "operation": "在活动开始/结束时间点进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理时间边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "时间边界", "priority": "中"}, {"case_id": "BNPL_564", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "时间边界测试", "operation": "在活动开始/结束时间点进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理时间边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "时间边界", "priority": "中"}, {"case_id": "BNPL_565", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "并发边界测试", "operation": "多用户同时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理并发边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "并发边界", "priority": "中"}, {"case_id": "BNPL_566", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "并发边界测试", "operation": "多用户同时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理并发边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "并发边界", "priority": "中"}, {"case_id": "BNPL_567", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "并发边界测试", "operation": "多用户同时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理并发边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "并发边界", "priority": "中"}, {"case_id": "BNPL_568", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "并发边界测试", "operation": "多用户同时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理并发边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "并发边界", "priority": "中"}, {"case_id": "BNPL_569", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "数据边界测试", "operation": "使用特殊字符商品名进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理数据边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "数据边界", "priority": "中"}, {"case_id": "BNPL_570", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "数据边界测试", "operation": "使用特殊字符商品名进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理数据边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "数据边界", "priority": "中"}, {"case_id": "BNPL_571", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "数据边界测试", "operation": "使用特殊字符商品名进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理数据边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "数据边界", "priority": "中"}, {"case_id": "BNPL_572", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "数据边界测试", "operation": "使用特殊字符商品名进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理数据边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "数据边界", "priority": "中"}, {"case_id": "BNPL_573", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会话边界测试", "operation": "支付页面长时间停留后进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理会话边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "会话边界", "priority": "中"}, {"case_id": "BNPL_574", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会话边界测试", "operation": "支付页面长时间停留后进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理会话边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "会话边界", "priority": "中"}, {"case_id": "BNPL_575", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "会话边界测试", "operation": "支付页面长时间停留后进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理会话边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "会话边界", "priority": "中"}, {"case_id": "BNPL_576", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "会话边界测试", "operation": "支付页面长时间停留后进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理会话边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "会话边界", "priority": "中"}, {"case_id": "BNPL_577", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "频率边界测试", "operation": "短时间内频繁进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理频率边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "频率边界", "priority": "中"}, {"case_id": "BNPL_578", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "频率边界测试", "operation": "短时间内频繁进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理频率边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "频率边界", "priority": "中"}, {"case_id": "BNPL_579", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "频率边界测试", "operation": "短时间内频繁进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理频率边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "频率边界", "priority": "中"}, {"case_id": "BNPL_580", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "频率边界测试", "operation": "短时间内频繁进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理频率边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "频率边界", "priority": "中"}, {"case_id": "BNPL_581", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "容量边界测试", "operation": "购物车包含大量商品时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理容量边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "容量边界", "priority": "中"}, {"case_id": "BNPL_582", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "容量边界测试", "operation": "购物车包含大量商品时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理容量边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "容量边界", "priority": "中"}, {"case_id": "BNPL_583", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "容量边界测试", "operation": "购物车包含大量商品时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理容量边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "容量边界", "priority": "中"}, {"case_id": "BNPL_584", "case_name": "边界值测试", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "容量边界测试", "operation": "购物车包含大量商品时进行BNPL支付", "action": "验证边界条件下的BNPL支付", "sub_action": "确认系统边界值处理和响应", "expected_result": "系统正确处理容量边界边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"}, "boundary_type": "容量边界", "priority": "中"}]}]}
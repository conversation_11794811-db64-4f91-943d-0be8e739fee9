# BNPL先享后付功能测试用例

- BNPL先享后付功能测试用例
  - 版本信息 v1.0
    - 创建日期: 2025-07-01
    - 测试范围: Affirm、Klarna、Afterpay
    - 测试类型: 功能测试、异常测试
    - 覆盖平台: Android、iOS、Web、H5
  - BNPL购买流程测试
    - 模块描述: 验证不同用户在不同平台使用BNPL支付的完整流程
    - 用例数量: 36个
    - 高优先级用例
      - BNPL_PURCHASE_001: 游客在Android端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 无需登录
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Android
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_002: 游客在iOS端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 无需登录
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: iOS
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_003: 游客在Web端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 无需登录
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Web
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_004: 游客在H5端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 无需登录
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: H5
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_005: 普通会员在Android端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 普通会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Android
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_006: 普通会员在iOS端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 普通会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: iOS
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_007: 普通会员在Web端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 普通会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Web
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_008: 普通会员在H5端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 普通会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: H5
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_009: 小B会员在Android端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 小B会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Android
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_010: 小B会员在iOS端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 小B会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: iOS
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_011: 小B会员在Web端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 小B会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Web
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_012: 小B会员在H5端使用Affirm支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Affirm服务
          - 小B会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Affirm支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Affirm支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: H5
          - provider: Affirm
          - product_price: 100-500美元
      - BNPL_PURCHASE_013: 游客在Android端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 无需登录
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Android
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_014: 游客在iOS端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 无需登录
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: iOS
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_015: 游客在Web端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 无需登录
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Web
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_016: 游客在H5端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 无需登录
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: H5
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_017: 普通会员在Android端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 普通会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Android
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_018: 普通会员在iOS端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 普通会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: iOS
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_019: 普通会员在Web端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 普通会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Web
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_020: 普通会员在H5端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 普通会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: H5
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_021: 小B会员在Android端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 小B会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Android
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_022: 小B会员在iOS端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 小B会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: iOS
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_023: 小B会员在Web端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 小B会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Web
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_024: 小B会员在H5端使用Klarna支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Klarna服务
          - 小B会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Klarna支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Klarna支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: H5
          - provider: Klarna
          - product_price: 100-500美元
      - BNPL_PURCHASE_025: 游客在Android端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 无需登录
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Android
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_026: 游客在iOS端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 无需登录
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: iOS
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_027: 游客在Web端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 无需登录
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: Web
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_028: 游客在H5端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 无需登录
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 浏览商品并加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 游客
          - platform: H5
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_029: 普通会员在Android端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 普通会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Android
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_030: 普通会员在iOS端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 普通会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: iOS
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_031: 普通会员在Web端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 普通会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: Web
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_032: 普通会员在H5端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 普通会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 普通会员
          - platform: H5
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_033: 小B会员在Android端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 小B会员账号准备就绪
          - Android端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Android端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Android
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_034: 小B会员在iOS端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 小B会员账号准备就绪
          - iOS端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在iOS端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: iOS
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_035: 小B会员在Web端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 小B会员账号准备就绪
          - Web端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在Web端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: Web
          - provider: Afterpay
          - product_price: 100-500美元
      - BNPL_PURCHASE_036: 小B会员在H5端使用Afterpay支付
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 系统已集成Afterpay服务
          - 小B会员账号准备就绪
          - H5端环境正常
          - 测试商品库存充足
        - 测试步骤
          - 1. 在H5端访问商城
          - 2. 选择测试商品加入购物车
          - 3. 进入结账页面
          - 4. 选择Afterpay支付方式
          - 5. 选择分期方案
          - 6. 确认订单信息
          - 7. 提交BNPL支付申请
          - 8. 等待审批结果
          - 9. 确认支付完成
        - 预期结果
          - Afterpay支付选项正常显示
          - 分期方案清晰展示费用信息
          - 审批流程顺畅完成
          - 订单状态正确更新
          - 支付成功后跳转确认页面
          - 用户收到订单确认通知
        - 测试数据
          - user_type: 小B会员
          - platform: H5
          - provider: Afterpay
          - product_price: 100-500美元
  - BNPL退款场景测试
    - 模块描述: 验证不同时机和类型的BNPL订单退款处理
    - 用例数量: 24个
    - 高优先级用例
      - BNPL_REFUND_001: Affirm订单订单确认后立即退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 订单确认
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_002: Affirm订单订单确认后立即退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 订单确认
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_003: Affirm订单发货前退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 待发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_004: Affirm订单发货前退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 待发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_005: Affirm订单发货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 已发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_006: Affirm订单发货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 已发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_007: Affirm订单收货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 已收货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_008: Affirm订单收货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Affirm支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Affirm退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Affirm退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Affirm接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Affirm
          - order_status: 已收货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_009: Klarna订单订单确认后立即退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 订单确认
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_010: Klarna订单订单确认后立即退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 订单确认
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_011: Klarna订单发货前退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 待发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_012: Klarna订单发货前退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 待发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_013: Klarna订单发货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 已发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_014: Klarna订单发货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 已发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_015: Klarna订单收货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 已收货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_016: Klarna订单收货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Klarna支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Klarna退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Klarna退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Klarna接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Klarna
          - order_status: 已收货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_017: Afterpay订单订单确认后立即退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 订单确认
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_018: Afterpay订单订单确认后立即退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为订单确认
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 订单确认
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_019: Afterpay订单发货前退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 待发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_020: Afterpay订单发货前退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为待发货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 待发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_021: Afterpay订单发货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 已发货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_022: Afterpay订单发货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为已发货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 已发货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_023: Afterpay订单收货后退款_全额退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择全额退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 已收货
          - refund_type: 全额退款
          - refund_amount: 50-100%订单金额
      - BNPL_REFUND_024: Afterpay订单收货后退款_部分退款
        - 基本信息
          - 优先级: 高
          - 测试类型: 功能测试
        - 前置条件
          - 存在Afterpay支付的订单
          - 订单状态为已收货
          - 退款权限已配置
          - Afterpay退款接口正常
        - 测试步骤
          - 1. 登录管理后台或用户账号
          - 2. 找到目标BNPL订单
          - 3. 发起退款申请
          - 4. 选择部分退款
          - 5. 填写退款原因
          - 6. 提交退款申请
          - 7. 系统处理退款请求
          - 8. 调用Afterpay退款接口
          - 9. 更新订单状态
          - 10. 通知用户退款结果
        - 预期结果
          - 退款申请成功提交
          - Afterpay接口调用成功
          - 订单状态正确更新为退款中/已退款
          - 分期计划相应调整
          - 用户收到退款通知
          - 退款金额正确计算
        - 测试数据
          - provider: Afterpay
          - order_status: 已收货
          - refund_type: 部分退款
          - refund_amount: 50-100%订单金额
  - BNPL优惠叠加测试
    - 模块描述: 验证BNPL与各种优惠活动的叠加使用
    - 用例数量: 27个
    - 中优先级用例
      - BNPL_DISCOUNT_001: 普通会员使用优惠券+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠券活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠券
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_002: 小B会员使用优惠券+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 优惠券活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 优惠券
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_003: 普通会员使用优惠码+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠码活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠码优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠码优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠码
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_004: 普通会员使用O币抵扣+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - O币抵扣活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: O币抵扣
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_005: 小B会员使用O币抵扣+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - O币抵扣活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: O币抵扣
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_006: 普通会员使用会员价+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 会员价活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 会员价
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_007: 小B会员使用会员价+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 会员价活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 会员价
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_008: 普通会员使用限时折扣+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 限时折扣活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 限时折扣
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_009: 小B会员使用限时折扣+Affirm支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 限时折扣活动正在进行
          - Affirm服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Affirm支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 限时折扣
          - provider: Affirm
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_010: 普通会员使用优惠券+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠券活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠券
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_011: 小B会员使用优惠券+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 优惠券活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 优惠券
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_012: 普通会员使用优惠码+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠码活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠码优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠码优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠码
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_013: 普通会员使用O币抵扣+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - O币抵扣活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: O币抵扣
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_014: 小B会员使用O币抵扣+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - O币抵扣活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: O币抵扣
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_015: 普通会员使用会员价+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 会员价活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 会员价
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_016: 小B会员使用会员价+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 会员价活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 会员价
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_017: 普通会员使用限时折扣+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 限时折扣活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 限时折扣
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_018: 小B会员使用限时折扣+Klarna支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 限时折扣活动正在进行
          - Klarna服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Klarna支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 限时折扣
          - provider: Klarna
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_019: 普通会员使用优惠券+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠券活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠券
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_020: 小B会员使用优惠券+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 优惠券活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠券优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠券优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 优惠券
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_021: 普通会员使用优惠码+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 优惠码活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用优惠码优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 优惠码优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 优惠码
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_022: 普通会员使用O币抵扣+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - O币抵扣活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: O币抵扣
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_023: 小B会员使用O币抵扣+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - O币抵扣活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用O币抵扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - O币抵扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: O币抵扣
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_024: 普通会员使用会员价+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 会员价活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 会员价
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_025: 小B会员使用会员价+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 会员价活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用会员价优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 会员价优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 会员价
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_026: 普通会员使用限时折扣+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 普通会员账号已登录
          - 限时折扣活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 普通会员
          - discount_type: 限时折扣
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
      - BNPL_DISCOUNT_027: 小B会员使用限时折扣+Afterpay支付
        - 基本信息
          - 优先级: 中
          - 测试类型: 功能测试
        - 前置条件
          - 小B会员账号已登录
          - 限时折扣活动正在进行
          - Afterpay服务正常
          - 测试商品参与优惠活动
        - 测试步骤
          - 1. 选择参与优惠的商品
          - 2. 应用限时折扣优惠
          - 3. 确认优惠后价格
          - 4. 进入支付页面
          - 5. 选择Afterpay支付
          - 6. 确认分期金额基于优惠后价格
          - 7. 完成BNPL支付流程
          - 8. 验证订单金额计算正确
        - 预期结果
          - 限时折扣优惠正确应用
          - BNPL分期基于优惠后价格计算
          - 订单总金额计算正确
          - 优惠明细清晰显示
          - 支付流程正常完成
        - 测试数据
          - user_type: 小B会员
          - discount_type: 限时折扣
          - provider: Afterpay
          - original_price: 200美元
          - discount_amount: 20-50美元
  - BNPL异常场景测试
    - 模块描述: 验证各种异常情况下的系统处理能力
    - 用例数量: 15个
    - 中优先级用例
      - BNPL_EXCEPTION_001: Affirm支付_网络超时异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟网络超时异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发网络超时
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Affirm
          - exception_type: 网络超时
          - scenario_description: 支付过程中网络连接超时
      - BNPL_EXCEPTION_002: Affirm支付_服务商维护异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟服务商维护异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发服务商维护
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Affirm
          - exception_type: 服务商维护
          - scenario_description: BNPL服务商系统维护
      - BNPL_EXCEPTION_003: Affirm支付_审批拒绝异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟审批拒绝异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发审批拒绝
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Affirm
          - exception_type: 审批拒绝
          - scenario_description: 用户信用审批被拒绝
      - BNPL_EXCEPTION_004: Affirm支付_重复提交异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟重复提交异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发重复提交
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Affirm
          - exception_type: 重复提交
          - scenario_description: 用户重复提交支付请求
      - BNPL_EXCEPTION_005: Affirm支付_浏览器关闭异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟浏览器关闭异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发浏览器关闭
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Affirm
          - exception_type: 浏览器关闭
          - scenario_description: 支付过程中意外关闭浏览器
      - BNPL_EXCEPTION_006: Klarna支付_网络超时异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟网络超时异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发网络超时
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Klarna
          - exception_type: 网络超时
          - scenario_description: 支付过程中网络连接超时
      - BNPL_EXCEPTION_007: Klarna支付_服务商维护异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟服务商维护异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发服务商维护
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Klarna
          - exception_type: 服务商维护
          - scenario_description: BNPL服务商系统维护
      - BNPL_EXCEPTION_008: Klarna支付_审批拒绝异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟审批拒绝异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发审批拒绝
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Klarna
          - exception_type: 审批拒绝
          - scenario_description: 用户信用审批被拒绝
      - BNPL_EXCEPTION_009: Klarna支付_重复提交异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟重复提交异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发重复提交
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Klarna
          - exception_type: 重复提交
          - scenario_description: 用户重复提交支付请求
      - BNPL_EXCEPTION_010: Klarna支付_浏览器关闭异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟浏览器关闭异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发浏览器关闭
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Klarna
          - exception_type: 浏览器关闭
          - scenario_description: 支付过程中意外关闭浏览器
      - BNPL_EXCEPTION_011: Afterpay支付_网络超时异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟网络超时异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发网络超时
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Afterpay
          - exception_type: 网络超时
          - scenario_description: 支付过程中网络连接超时
      - BNPL_EXCEPTION_012: Afterpay支付_服务商维护异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟服务商维护异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发服务商维护
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Afterpay
          - exception_type: 服务商维护
          - scenario_description: BNPL服务商系统维护
      - BNPL_EXCEPTION_013: Afterpay支付_审批拒绝异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟审批拒绝异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发审批拒绝
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Afterpay
          - exception_type: 审批拒绝
          - scenario_description: 用户信用审批被拒绝
      - BNPL_EXCEPTION_014: Afterpay支付_重复提交异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟重复提交异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发重复提交
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Afterpay
          - exception_type: 重复提交
          - scenario_description: 用户重复提交支付请求
      - BNPL_EXCEPTION_015: Afterpay支付_浏览器关闭异常处理
        - 基本信息
          - 优先级: 中
          - 测试类型: 异常测试
        - 前置条件
          - 正常的BNPL支付环境
          - 模拟浏览器关闭异常条件
          - 测试订单准备就绪
        - 测试步骤
          - 1. 正常进入BNPL支付流程
          - 2. 在关键步骤触发浏览器关闭
          - 3. 观察系统响应
          - 4. 检查订单状态
          - 5. 验证错误处理机制
          - 6. 确认用户体验
        - 预期结果
          - 系统能正确识别异常情况
          - 显示友好的错误提示信息
          - 订单状态不会出现异常
          - 用户可以重新尝试或选择其他支付方式
          - 不会产生重复扣款或订单
        - 测试数据
          - provider: Afterpay
          - exception_type: 浏览器关闭
          - scenario_description: 支付过程中意外关闭浏览器
  - 测试总结
    - 总用例数: 102个
    - 测试模块: 4个
    - 覆盖范围
      - BNPL服务商: Affirm、Klarna、Afterpay
      - 用户类型: 游客、普通会员、小B会员
      - 平台端口: Android、iOS、Web、H5
      - 测试场景: 购买流程、退款处理、优惠叠加、异常处理
    - 测试重点
      - 支付流程完整性验证
      - 订单状态正确性验证
      - 退款处理准确性验证
      - 优惠计算正确性验证
      - 异常情况处理验证

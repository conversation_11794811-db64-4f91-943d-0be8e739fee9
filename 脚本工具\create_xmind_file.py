#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建真正的XMind文件
使用xmind库生成标准的.xmind格式文件
"""

import json
import os
import sys

# 尝试导入xmind库，如果没有则提供安装指导
try:
    import xmind
except ImportError:
    print("需要安装xmind库，请运行: pip install xmind")
    print("如果安装失败，可以尝试: pip install xmindparser")
    sys.exit(1)

class XMindFileGenerator:
    def __init__(self, json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)
    
    def create_xmind_file(self, output_file):
        """创建XMind文件"""
        # 创建工作簿
        workbook = xmind.load(None)  # 创建新的工作簿
        
        # 获取第一个工作表
        sheet = workbook.getPrimarySheet()
        sheet.setTitle("BNPL测试用例")
        
        # 获取根主题
        root_topic = sheet.getRootTopic()
        root_topic.setTitle(self.test_data["title"])
        
        # 添加版本信息主题
        version_topic = root_topic.addSubTopic()
        version_topic.setTitle(f"版本信息 {self.test_data['version']}")
        
        # 添加版本详细信息
        version_details = [
            f"创建日期: {self.test_data['created_date']}",
            "测试范围: Affirm、Klarna、Afterpay",
            "测试类型: 功能测试、异常测试",
            "覆盖平台: Android、iOS、Web、H5"
        ]
        
        for detail in version_details:
            detail_topic = version_topic.addSubTopic()
            detail_topic.setTitle(detail)
        
        # 添加测试模块
        for module in self.test_data["test_modules"]:
            self.add_module_to_xmind(root_topic, module)
        
        # 添加测试总结
        self.add_summary_to_xmind(root_topic)
        
        # 保存文件
        xmind.save(workbook, output_file)
        return output_file
    
    def add_module_to_xmind(self, parent_topic, module):
        """添加测试模块到XMind"""
        module_topic = parent_topic.addSubTopic()
        module_topic.setTitle(module["module_name"])
        
        # 添加模块描述
        desc_topic = module_topic.addSubTopic()
        desc_topic.setTitle(f"模块描述: {module['description']}")
        
        # 添加用例数量
        count_topic = module_topic.addSubTopic()
        count_topic.setTitle(f"用例数量: {len(module['test_cases'])}个")
        
        # 按优先级分组
        high_priority_cases = [case for case in module["test_cases"] if case["priority"] == "高"]
        medium_priority_cases = [case for case in module["test_cases"] if case["priority"] == "中"]
        
        if high_priority_cases:
            high_topic = module_topic.addSubTopic()
            high_topic.setTitle("高优先级用例")
            for case in high_priority_cases:
                self.add_case_to_xmind(high_topic, case)
        
        if medium_priority_cases:
            medium_topic = module_topic.addSubTopic()
            medium_topic.setTitle("中优先级用例")
            for case in medium_priority_cases:
                self.add_case_to_xmind(medium_topic, case)
    
    def add_case_to_xmind(self, parent_topic, case):
        """添加测试用例到XMind"""
        case_topic = parent_topic.addSubTopic()
        case_topic.setTitle(f"{case['case_id']}: {case['case_name']}")
        
        # 基本信息
        basic_topic = case_topic.addSubTopic()
        basic_topic.setTitle("基本信息")
        
        priority_topic = basic_topic.addSubTopic()
        priority_topic.setTitle(f"优先级: {case['priority']}")
        
        type_topic = basic_topic.addSubTopic()
        type_topic.setTitle(f"测试类型: {case['test_type']}")
        
        # 前置条件
        if case["preconditions"]:
            precond_topic = case_topic.addSubTopic()
            precond_topic.setTitle("前置条件")
            for condition in case["preconditions"]:
                cond_topic = precond_topic.addSubTopic()
                cond_topic.setTitle(condition)
        
        # 测试步骤
        if case["test_steps"]:
            steps_topic = case_topic.addSubTopic()
            steps_topic.setTitle("测试步骤")
            for step in case["test_steps"]:
                step_topic = steps_topic.addSubTopic()
                step_topic.setTitle(step)
        
        # 预期结果
        if case["expected_results"]:
            results_topic = case_topic.addSubTopic()
            results_topic.setTitle("预期结果")
            for result in case["expected_results"]:
                result_topic = results_topic.addSubTopic()
                result_topic.setTitle(result)
        
        # 测试数据
        if case.get("test_data"):
            data_topic = case_topic.addSubTopic()
            data_topic.setTitle("测试数据")
            for key, value in case["test_data"].items():
                data_item_topic = data_topic.addSubTopic()
                data_item_topic.setTitle(f"{key}: {value}")
    
    def add_summary_to_xmind(self, root_topic):
        """添加测试总结到XMind"""
        summary_topic = root_topic.addSubTopic()
        summary_topic.setTitle("测试总结")
        
        total_cases = sum(len(module["test_cases"]) for module in self.test_data["test_modules"])
        
        # 总用例数
        total_topic = summary_topic.addSubTopic()
        total_topic.setTitle(f"总用例数: {total_cases}个")
        
        # 测试模块数
        modules_topic = summary_topic.addSubTopic()
        modules_topic.setTitle(f"测试模块: {len(self.test_data['test_modules'])}个")
        
        # 覆盖范围
        coverage_topic = summary_topic.addSubTopic()
        coverage_topic.setTitle("覆盖范围")
        
        coverage_items = [
            "BNPL服务商: Affirm、Klarna、Afterpay",
            "用户类型: 游客、普通会员、小B会员",
            "平台端口: Android、iOS、Web、H5",
            "测试场景: 购买流程、退款处理、优惠叠加、异常处理"
        ]
        
        for item in coverage_items:
            item_topic = coverage_topic.addSubTopic()
            item_topic.setTitle(item)
        
        # 测试重点
        focus_topic = summary_topic.addSubTopic()
        focus_topic.setTitle("测试重点")
        
        focus_items = [
            "支付流程完整性验证",
            "订单状态正确性验证",
            "退款处理准确性验证",
            "优惠计算正确性验证",
            "异常情况处理验证"
        ]
        
        for item in focus_items:
            item_topic = focus_topic.addSubTopic()
            item_topic.setTitle(item)

def main():
    """主函数"""
    json_file = "bnpl_test_cases.json"
    output_file = "../输出用例/BNPL先享后付_全面测试用例_v1.0.xmind"
    
    if not os.path.exists(json_file):
        print(f"找不到输入文件: {json_file}")
        return
    
    try:
        generator = XMindFileGenerator(json_file)
        result_file = generator.create_xmind_file(output_file)
        print(f"XMind文件已成功生成: {result_file}")
        print("文件包含以下内容:")
        print("- BNPL购买流程测试用例 (36个)")
        print("- BNPL退款场景测试用例 (24个)")
        print("- BNPL优惠叠加测试用例 (45个)")
        print("- BNPL异常场景测试用例 (15个)")
        print("总计: 120个测试用例")
    except Exception as e:
        print(f"生成XMind文件时出错: {e}")
        print("请确保已安装xmind库: pip install xmind")

if __name__ == "__main__":
    main()

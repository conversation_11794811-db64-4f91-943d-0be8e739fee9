XMind转换结果 (BNPL先享后付_全面测试用例_v2.0.xmind)
==================================================

# Sheet_1
--------------------------------------------------
BNPL先享后付功能测试用例
├── 版本信息 v2.0
    ├── 创建日期: 2025-07-01
    ├── 测试范围: Affirm、Klarna、Afterpay
    ├── 测试类型: 功能测试、异常测试
    └── 覆盖平台: Android、iOS、Web、H5（APP端不支持游客）
├── BNPL购买流程测试
   ├── BNPL_PURCHASE_001: 支付
			├── H5
				├── 游客
					├──购物车结算
						├── 添加任意商品多件进入购物车结算
							├──点击结算并使用BNPL先享后付功能
								└──扣款金额与分期计划一致Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
						├── 添加任意商品一件进入购物车结算
							├──点击结算并使用BNPL先享后付功能
								└──扣款金额与分期计划一致Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
					└── 直接结算
				├── 小B会员
				└── 会员
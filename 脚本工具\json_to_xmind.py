#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON测试用例转XMind格式生成器
将JSON格式的测试用例转换为XMind思维导图
"""

import json
import os
from datetime import datetime

class XMindGenerator:
    def __init__(self, json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)
    
    def generate_xmind_content(self):
        """生成XMind内容结构"""
        content = {
            "title": self.test_data["title"],
            "structure": self.build_mind_map_structure()
        }
        return content
    
    def build_mind_map_structure(self):
        """构建思维导图结构"""
        root = {
            "title": self.test_data["title"],
            "children": []
        }
        
        # 添加版本信息
        version_info = {
            "title": f"版本信息 {self.test_data['version']}",
            "children": [
                {"title": f"创建日期: {self.test_data['created_date']}"},
                {"title": "测试范围: Affirm、Klarna、Afterpay"},
                {"title": "测试类型: 功能测试、异常测试"},
                {"title": "覆盖平台: Android、iOS、Web、H5"}
            ]
        }
        root["children"].append(version_info)
        
        # 添加测试模块
        for module in self.test_data["test_modules"]:
            module_node = self.build_module_node(module)
            root["children"].append(module_node)
        
        # 添加测试总结
        summary_node = self.build_summary_node()
        root["children"].append(summary_node)
        
        return root
    
    def build_module_node(self, module):
        """构建测试模块节点"""
        module_node = {
            "title": module["module_name"],
            "children": [
                {"title": f"模块描述: {module['description']}"},
                {"title": f"用例数量: {len(module['test_cases'])}个"}
            ]
        }
        
        # 按优先级分组用例
        high_priority_cases = []
        medium_priority_cases = []
        
        for case in module["test_cases"]:
            case_node = self.build_case_node(case)
            if case["priority"] == "高":
                high_priority_cases.append(case_node)
            else:
                medium_priority_cases.append(case_node)
        
        if high_priority_cases:
            high_priority_group = {
                "title": "高优先级用例",
                "children": high_priority_cases
            }
            module_node["children"].append(high_priority_group)
        
        if medium_priority_cases:
            medium_priority_group = {
                "title": "中优先级用例", 
                "children": medium_priority_cases
            }
            module_node["children"].append(medium_priority_group)
        
        return module_node
    
    def build_case_node(self, case):
        """构建测试用例节点"""
        case_node = {
            "title": f"{case['case_id']}: {case['case_name']}",
            "children": []
        }
        
        # 基本信息
        basic_info = {
            "title": "基本信息",
            "children": [
                {"title": f"优先级: {case['priority']}"},
                {"title": f"测试类型: {case['test_type']}"}
            ]
        }
        case_node["children"].append(basic_info)
        
        # 前置条件
        if case["preconditions"]:
            preconditions = {
                "title": "前置条件",
                "children": [{"title": condition} for condition in case["preconditions"]]
            }
            case_node["children"].append(preconditions)
        
        # 测试步骤
        if case["test_steps"]:
            test_steps = {
                "title": "测试步骤",
                "children": [{"title": step} for step in case["test_steps"]]
            }
            case_node["children"].append(test_steps)
        
        # 预期结果
        if case["expected_results"]:
            expected_results = {
                "title": "预期结果",
                "children": [{"title": result} for result in case["expected_results"]]
            }
            case_node["children"].append(expected_results)
        
        # 测试数据
        if case.get("test_data"):
            test_data = {
                "title": "测试数据",
                "children": [{"title": f"{k}: {v}"} for k, v in case["test_data"].items()]
            }
            case_node["children"].append(test_data)
        
        return case_node
    
    def build_summary_node(self):
        """构建测试总结节点"""
        total_cases = sum(len(module["test_cases"]) for module in self.test_data["test_modules"])
        
        summary = {
            "title": "测试总结",
            "children": [
                {"title": f"总用例数: {total_cases}个"},
                {"title": f"测试模块: {len(self.test_data['test_modules'])}个"},
                {
                    "title": "覆盖范围",
                    "children": [
                        {"title": "BNPL服务商: Affirm、Klarna、Afterpay"},
                        {"title": "用户类型: 游客、普通会员、小B会员"},
                        {"title": "平台端口: Android、iOS、Web、H5"},
                        {"title": "测试场景: 购买流程、退款处理、优惠叠加、异常处理"}
                    ]
                },
                {
                    "title": "测试重点",
                    "children": [
                        {"title": "支付流程完整性验证"},
                        {"title": "订单状态正确性验证"},
                        {"title": "退款处理准确性验证"},
                        {"title": "优惠计算正确性验证"},
                        {"title": "异常情况处理验证"}
                    ]
                }
            ]
        }
        
        return summary
    
    def export_to_text(self, output_file):
        """导出为文本格式（模拟XMind结构）"""
        content = self.generate_xmind_content()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# {content['title']}\n\n")
            self.write_node_to_text(f, content['structure'], 0)
        
        return output_file
    
    def write_node_to_text(self, file, node, level):
        """递归写入节点到文本文件"""
        indent = "  " * level
        file.write(f"{indent}- {node['title']}\n")
        
        if 'children' in node:
            for child in node['children']:
                self.write_node_to_text(file, child, level + 1)

def main():
    # 生成XMind格式的测试用例
    json_file = "bnpl_test_cases.json"
    if os.path.exists(json_file):
        generator = XMindGenerator(json_file)
        output_file = "../输出用例/BNPL先享后付_测试用例_v1.0.txt"
        generator.export_to_text(output_file)
        print(f"XMind格式测试用例已生成到: {output_file}")
    else:
        print(f"找不到输入文件: {json_file}")

if __name__ == "__main__":
    main()

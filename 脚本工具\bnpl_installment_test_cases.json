{"metadata": {"title": "BNPL先享后付分期测试用例", "version": "V10.0", "create_date": "2025-07-16", "description": "BNPL三种支付方式分期区间测试，包含下单、退款、优惠叠加等场景", "total_cases": 286, "bnpl_providers": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "test_ranges": {"Affirm": "US$50.00 - US$30,000.00", "Afterpay": "US$1.00 - US$4,000.00", "Klarna": "US$0.50 - US$999,999.99"}}, "test_modules": {"BNPL分期下单流程测试": [{"id": "BNPL_001", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_002", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_003", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_004", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_005", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_006", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_007", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_008", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_009", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_010", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_011", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_012", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_013", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_014", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_015", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_016", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_017", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_018", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_019", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_020", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_021", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_022", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_023", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_024", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_025", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_026", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_027", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_028", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_029", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_030", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_031", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_032", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_033", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_034", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_035", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_036", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_037", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_038", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_039", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_040", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_041", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_042", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_043", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_044", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_045", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_046", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_047", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_048", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_049", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_050", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_051", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_052", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_053", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_054", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_055", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_056", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_057", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_058", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_059", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_060", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_061", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_062", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_063", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_064", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_065", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_066", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_067", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_068", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_069", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_070", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_071", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_072", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_073", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_074", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_075", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_076", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_077", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_078", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_079", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_080", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_081", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_082", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_083", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_084", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_085", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_086", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_087", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_088", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_089", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_090", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}], "BNPL分期金额边界值测试": [{"id": "BNPL_200", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "currency": "USD", "expected_bnpl": [], "description": "低于所有BNPL最小值"}}, {"id": "BNPL_201", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值"}}, {"id": "BNPL_202", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限"}}, {"id": "BNPL_203", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值"}}, {"id": "BNPL_204", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限"}}, {"id": "BNPL_205", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始"}}, {"id": "BNPL_206", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值"}}, {"id": "BNPL_207", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限"}}, {"id": "BNPL_208", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值"}}, {"id": "BNPL_209", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持"}}, {"id": "BNPL_210", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值"}}, {"id": "BNPL_211", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "currency": "USD", "expected_bnpl": [], "description": "超出所有BNPL最大值"}}, {"id": "BNPL_212", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "currency": "USD", "expected_bnpl": [], "description": "低于所有BNPL最小值"}}, {"id": "BNPL_213", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值"}}, {"id": "BNPL_214", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限"}}, {"id": "BNPL_215", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值"}}, {"id": "BNPL_216", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限"}}, {"id": "BNPL_217", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始"}}, {"id": "BNPL_218", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值"}}, {"id": "BNPL_219", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限"}}, {"id": "BNPL_220", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值"}}, {"id": "BNPL_221", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持"}}, {"id": "BNPL_222", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值"}}, {"id": "BNPL_223", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "currency": "USD", "expected_bnpl": [], "description": "超出所有BNPL最大值"}}, {"id": "BNPL_316", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_317", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_318", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_319", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_320", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_321", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_344", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_345", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_346", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_347", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_348", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_349", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_368", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_369", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_370", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_371", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_372", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_390", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_391", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_392", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_393", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_394", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}], "BNPL分期优惠叠加测试": [{"id": "BNPL_300", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_301", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_302", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_303", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_304", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_305", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_306", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_307", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_308", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_309", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_310", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_311", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_312", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_313", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_314", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_315", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_322", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_323", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_324", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_325", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_326", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_327", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_328", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_329", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_330", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_331", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_332", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_333", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_334", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_335", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_336", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_337", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_338", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_339", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_340", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_341", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_342", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_343", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_350", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_351", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_352", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_353", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_354", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_355", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_356", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_357", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_358", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_359", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_360", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_361", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_362", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_363", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_364", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_365", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_366", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_367", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_373", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_374", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_375", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_376", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_377", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_378", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_379", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_380", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_381", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_382", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_383", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_384", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_385", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_386", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_387", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_388", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_389", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_395", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_396", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_397", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_398", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_399", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}], "BNPL分期退款处理测试": [{"id": "BNPL_400", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_401", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_402", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_403", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_404", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_405", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_406", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_407", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_408", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_409", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_410", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_411", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_412", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_413", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_414", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_415", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_416", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_417", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_418", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_419", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_420", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_421", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_422", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_423", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_424", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_425", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_426", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_427", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_428", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_429", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_430", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_431", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}], "BNPL额外业务场景测试": [{"id": "BNPL_500", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_501", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_502", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_503", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_504", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_505", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_506", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_507", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_508", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_509", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_510", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_511", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_512", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_513", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_514", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_515", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_516", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_517", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_518", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_519", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_520", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_521", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_522", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_523", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_524", "name": "BNPL支付失败重试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_525", "name": "BNPL支付中断恢复", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_526", "name": "BNPL额度不足处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_527", "name": "BNPL分期方案变更", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_528", "name": "BNPL支付失败重试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_529", "name": "BNPL支付中断恢复", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_530", "name": "BNPL额度不足处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_531", "name": "BNPL分期方案变更", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_532", "name": "BNPL支付失败重试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_533", "name": "BNPL支付中断恢复", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_534", "name": "BNPL额度不足处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_535", "name": "BNPL分期方案变更", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_536", "name": "BNPL支付失败重试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_537", "name": "BNPL支付中断恢复", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_538", "name": "BNPL额度不足处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_539", "name": "BNPL分期方案变更", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}]}, "all_cases": [{"id": "BNPL_001", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_002", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_003", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_004", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_005", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_006", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_007", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_008", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_009", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_010", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_011", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_012", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_013", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_014", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_015", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_016", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_017", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_018", "name": "BNPL分期下单流程", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_019", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_020", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_021", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_022", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_023", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_024", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_025", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_026", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_027", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_028", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_029", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_030", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_031", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_032", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_033", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_034", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_035", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_036", "name": "BNPL分期下单流程", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_037", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_038", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_039", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_040", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_041", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_042", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_043", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_044", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_045", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_046", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_047", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_048", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_049", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_050", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_051", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_052", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_053", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_054", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_055", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_056", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_057", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_058", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_059", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_060", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_061", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_062", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_063", "name": "BNPL分期下单流程", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_064", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_065", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_066", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_067", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_068", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_069", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_070", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_071", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_072", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_073", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_074", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_075", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_076", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_077", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_078", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_079", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_080", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_081", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_082", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加单品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_083", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加多品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_084", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加自由捆绑商品商品到购物车结算", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_085", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加单品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_086", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加多品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_087", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "直接购买", "level5_operation": "添加自由捆绑商品商品到直接购买", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_088", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加单品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "单品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_089", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加多品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "多品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_090", "name": "BNPL分期下单流程", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "待支付订单转BNPL", "level5_operation": "添加自由捆绑商品商品到待支付订单转BNPL", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认分期方案并完成支付", "level8_expected": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知", "test_data": {"currency": "USD", "available_bnpl": ["Affirm", "Afterpay", "<PERSON><PERSON><PERSON>"], "product_type": "自由捆绑商品", "scenario_type": "normal_purchase"}}, {"id": "BNPL_200", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "currency": "USD", "expected_bnpl": [], "description": "低于所有BNPL最小值"}}, {"id": "BNPL_201", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值"}}, {"id": "BNPL_202", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限"}}, {"id": "BNPL_203", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值"}}, {"id": "BNPL_204", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限"}}, {"id": "BNPL_205", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始"}}, {"id": "BNPL_206", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值"}}, {"id": "BNPL_207", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限"}}, {"id": "BNPL_208", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值"}}, {"id": "BNPL_209", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持"}}, {"id": "BNPL_210", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值"}}, {"id": "BNPL_211", "name": "BNPL分期金额边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "currency": "USD", "expected_bnpl": [], "description": "超出所有BNPL最大值"}}, {"id": "BNPL_212", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.49)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(低于所有BNPL最小值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 0.49, "currency": "USD", "expected_bnpl": [], "description": "低于所有BNPL最小值"}}, {"id": "BNPL_213", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.5)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.5, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON><PERSON>最小值"}}, {"id": "BNPL_214", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$0.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 0.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "Afterpay最小值下限"}}, {"id": "BNPL_215", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最小值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 1.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Afterpay最小值"}}, {"id": "BNPL_216", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$49.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最小值下限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']", "test_data": {"amount": 49.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay"], "description": "Affirm最小值下限"}}, {"id": "BNPL_217", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$50.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(全支持区间开始)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 50.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "全支持区间开始"}}, {"id": "BNPL_218", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']", "test_data": {"amount": 4000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Afterpay", "Affirm"], "description": "Afterpay最大值"}}, {"id": "BNPL_219", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$4000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Afterpay最大值上限)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 4000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Afterpay最大值上限"}}, {"id": "BNPL_220", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Affirm最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['Klarna', 'Affirm']", "test_data": {"amount": 30000.0, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>", "Affirm"], "description": "Affirm最大值"}}, {"id": "BNPL_221", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$30000.01)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(仅Klarna支持)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 30000.01, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "仅Klarna支持"}}, {"id": "BNPL_222", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$999999.99)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(Klarna最大值)", "level8_expected": "系统正确显示可用BNPL选项: ['<PERSON><PERSON><PERSON>']", "test_data": {"amount": 999999.99, "currency": "USD", "expected_bnpl": ["<PERSON><PERSON><PERSON>"], "description": "<PERSON><PERSON>na最大值"}}, {"id": "BNPL_223", "name": "BNPL分期金额边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品到购物车(总价US$1000000.0)", "level6_step": "选择BNPL先享后付支付方式", "level7_substep": "验证可用BNPL选项(超出所有BNPL最大值)", "level8_expected": "系统正确显示可用BNPL选项: 无可用选项", "test_data": {"amount": 1000000.0, "currency": "USD", "expected_bnpl": [], "description": "超出所有BNPL最大值"}}, {"id": "BNPL_300", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_301", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_302", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_303", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_304", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_305", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_306", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_307", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_308", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_309", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_310", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_311", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_312", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_313", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_314", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_315", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_316", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_317", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_318", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_319", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_320", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_321", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_322", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_323", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_324", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_325", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_326", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_327", "name": "BNPL分期优惠叠加测试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_328", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_329", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_330", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_331", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_332", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_333", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_334", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_335", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_336", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_337", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_338", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_339", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_340", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_341", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_342", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_343", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_344", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_345", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_346", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_347", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_348", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_349", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_350", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_351", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_352", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_353", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_354", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_355", "name": "BNPL分期优惠叠加测试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_356", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_357", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_358", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_359", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_360", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_361", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_362", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_363", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_364", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_365", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_366", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_367", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_368", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_369", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_370", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_371", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_372", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_373", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_374", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_375", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_376", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_377", "name": "BNPL分期优惠叠加测试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_378", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_379", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币抵扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_380", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_381", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_382", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_383", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_384", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_385", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_386", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_387", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用小B会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "小B会员折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_388", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_389", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用自由捆绑优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "自由捆绑", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_390", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_391", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$50.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 51.0, "discount_amount": 1.0, "final_amount": 50.0, "discount_type": "优惠券", "note": "优惠后刚好达到Affirm最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_392", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_393", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币抵扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$4000.0的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 4001.0, "discount_amount": 1.0, "final_amount": 4000.0, "discount_type": "O币抵扣", "note": "优惠后刚好达到Afterpay最大值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_394", "name": "BNPL分期优惠叠加边界值测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "验证边界值：最终价格US$0.5的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，边界值验证通过", "test_data": {"original_amount": 1.5, "discount_amount": 1.0, "final_amount": 0.5, "discount_type": "会员折扣", "note": "优惠后刚好达到Klarna最小值", "scenario_type": "boundary_discount_test"}}, {"id": "BNPL_395", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_396", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用O币+优惠券优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "O币+优惠券", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_397", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_398", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用优惠码+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "优惠码+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_399", "name": "BNPL分期优惠叠加测试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "购物车结算", "level5_operation": "添加商品并应用会员折扣+限时折扣优惠", "level6_step": "选择BNPL先享后付分期支付", "level7_substep": "确认基于折后价的分期方案", "level8_expected": "优惠正确应用，BNPL基于折后价计算，金额准确无误", "test_data": {"discount_type": "会员折扣+限时折扣", "scenario_type": "normal_discount_test"}}, {"id": "BNPL_400", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_401", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_402", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_403", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_404", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_405", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_406", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_407", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_408", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_409", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_410", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_411", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_412", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_413", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_414", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_415", "name": "BNPL分期退款处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_416", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_417", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_418", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_419", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_420", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_421", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_422", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_423", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_424", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_425", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单订单确认后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "订单确认后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_426", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_427", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货前申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货前", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_428", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_429", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单发货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "发货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_430", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理全额退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "全额退款", "scenario_type": "refund_test"}}, {"id": "BNPL_431", "name": "BNPL分期退款处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "退款处理", "level5_operation": "BNPL订单收货后申请退款", "level6_step": "处理部分退款退款申请", "level7_substep": "调整分期计划并完成退款", "level8_expected": "退款处理成功，分期计划相应调整，状态同步正确", "test_data": {"refund_timing": "收货后", "refund_type": "部分退款", "scenario_type": "refund_test"}}, {"id": "BNPL_500", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_501", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_502", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_503", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_504", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_505", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_506", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_507", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_508", "name": "BNPL支付失败重试", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_509", "name": "BNPL支付中断恢复", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_510", "name": "BNPL额度不足处理", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_511", "name": "BNPL分期方案变更", "level2_platform": "Web", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_512", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_513", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_514", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_515", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "游客", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_516", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_517", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_518", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_519", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_520", "name": "BNPL支付失败重试", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_521", "name": "BNPL支付中断恢复", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_522", "name": "BNPL额度不足处理", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_523", "name": "BNPL分期方案变更", "level2_platform": "H5", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_524", "name": "BNPL支付失败重试", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_525", "name": "BNPL支付中断恢复", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_526", "name": "BNPL额度不足处理", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_527", "name": "BNPL分期方案变更", "level2_platform": "Android", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_528", "name": "BNPL支付失败重试", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_529", "name": "BNPL支付中断恢复", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_530", "name": "BNPL额度不足处理", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_531", "name": "BNPL分期方案变更", "level2_platform": "Android", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_532", "name": "BNPL支付失败重试", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_533", "name": "BNPL支付中断恢复", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_534", "name": "BNPL额度不足处理", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_535", "name": "BNPL分期方案变更", "level2_platform": "iOS", "level3_user": "会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_536", "name": "BNPL支付失败重试", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付失败后重新选择分期方案", "level6_step": "处理支付失败并重新选择BNPL分期", "level7_substep": "重新确认分期方案并完成支付", "level8_expected": "支付失败处理正确，重试流程正常，最终支付成功", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_537", "name": "BNPL支付中断恢复", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL支付过程中断后恢复支付", "level6_step": "从中断点恢复BNPL分期支付流程", "level7_substep": "确认之前的分期选择并完成支付", "level8_expected": "支付中断恢复正常，订单状态正确更新", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_538", "name": "BNPL额度不足处理", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "BNPL分期额度不足时的处理", "level6_step": "提示额度不足并引导选择其他支付方式", "level7_substep": "显示可用额度并提供替代方案", "level8_expected": "额度不足提示清晰，替代方案引导正确", "test_data": {"scenario_type": "additional_business_case"}}, {"id": "BNPL_539", "name": "BNPL分期方案变更", "level2_platform": "iOS", "level3_user": "小B会员", "level4_scenario": "异常场景处理", "level5_operation": "支付前变更BNPL分期方案", "level6_step": "修改已选择的分期方案", "level7_substep": "确认新的分期方案并完成支付", "level8_expected": "分期方案变更成功，费用计算正确", "test_data": {"scenario_type": "additional_business_case"}}]}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL测试用例生成器 V2
基于更新的业务规则生成测试用例
"""

import json
from datetime import datetime

def generate_purchase_cases():
    """生成购买流程测试用例"""
    cases = []
    case_id = 1
    
    # BNPL服务商
    providers = ["Affirm", "Klarna", "Afterpay"]
    
    # 用户类型（APP端不支持游客）
    user_platform_map = {
        "Android": ["普通会员", "小B会员"],
        "iOS": ["普通会员", "小B会员"], 
        "Web": ["游客", "普通会员", "小B会员"],
        "H5": ["游客", "普通会员", "小B会员"]
    }
    
    for provider in providers:
        for platform in user_platform_map.keys():
            for user_type in user_platform_map[platform]:
                case = {
                    "case_id": f"BNPL_PURCHASE_{case_id:03d}",
                    "case_name": f"支付-{platform}-{provider}支付-{user_type}正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致",
                    "priority": "高",
                    "test_type": "功能测试",
                    "preconditions": [
                        f"{platform}端环境正常，{provider}服务可用",
                        f"{user_type}账号已登录，信用状态良好" if user_type != "游客" else "游客身份，无需登录",
                        "购物车中有可支付商品",
                        f"{provider}分期服务已配置"
                    ],
                    "test_steps": [
                        f"1. 进入{platform}端结账页面，选择{provider}支付方式",
                        "2. 选择分期方案（如12个月分期）",
                        "3. 确认分期信息和费用明细",
                        f"4. 提交{provider}支付申请",
                        f"5. 完成{provider}审批流程",
                        "6. 确认支付成功"
                    ],
                    "expected_results": [
                        f"{provider}支付选项正常显示",
                        "分期方案清晰展示，费用透明",
                        "审批流程顺畅，无异常中断",
                        "支付成功后订单状态更新为已支付",
                        "用户收到支付确认和分期计划通知",
                        "扣款金额与分期计划一致"
                    ],
                    "test_data": {
                        "user_type": user_type,
                        "platform": platform,
                        "provider": provider,
                        "product_price": "100-500美元"
                    }
                }
                cases.append(case)
                case_id += 1
    
    return cases

def generate_refund_cases():
    """生成退款场景测试用例"""
    cases = []
    case_id = 1
    
    providers = ["Affirm", "Klarna", "Afterpay"]
    refund_timings = [
        ("订单确认后立即退款", "订单确认"),
        ("发货前退款", "待发货"),
        ("发货后退款", "已发货"),
        ("收货后退款", "已收货")
    ]
    refund_types = ["全额退款", "部分退款"]
    
    for provider in providers:
        for timing_name, order_status in refund_timings:
            for refund_type in refund_types:
                case = {
                    "case_id": f"BNPL_REFUND_{case_id:03d}",
                    "case_name": f"退款-{provider}-{timing_name}-{refund_type}，分期计划正确调整",
                    "priority": "高",
                    "test_type": "功能测试",
                    "preconditions": [
                        f"存在{provider}支付的订单",
                        f"订单状态为{order_status}",
                        "退款权限已配置",
                        f"{provider}退款接口正常"
                    ],
                    "test_steps": [
                        "1. 在订单管理系统中找到目标订单",
                        f"2. 发起{refund_type}申请",
                        "3. 填写退款原因和金额",
                        "4. 提交退款申请",
                        f"5. 系统调用{provider}退款接口",
                        "6. 验证分期计划调整"
                    ],
                    "expected_results": [
                        "退款申请成功提交",
                        f"{provider}接口调用成功",
                        f"订单状态更新为{refund_type}状态",
                        "分期计划按比例调整" if refund_type == "部分退款" else "分期计划完全取消",
                        "用户收到退款通知",
                        "退款金额正确计算"
                    ],
                    "test_data": {
                        "provider": provider,
                        "order_status": order_status,
                        "refund_type": refund_type,
                        "refund_amount": "50-100%订单金额"
                    }
                }
                cases.append(case)
                case_id += 1
    
    return cases

def generate_discount_cases():
    """生成优惠叠加测试用例"""
    cases = []
    case_id = 1
    
    providers = ["Affirm", "Klarna", "Afterpay"]
    discount_types = ["优惠券", "优惠码", "O币抵扣", "会员价", "限时折扣"]
    
    for provider in providers:
        for discount in discount_types:
            for user_type in ["普通会员", "小B会员"]:
                if discount == "优惠码" and user_type == "小B会员":
                    continue  # 小B会员不能使用优惠码
                
                case = {
                    "case_id": f"BNPL_DISCOUNT_{case_id:03d}",
                    "case_name": f"优惠叠加-{provider}-{user_type}使用{discount}+BNPL支付，优惠后金额正确分期",
                    "priority": "中",
                    "test_type": "功能测试",
                    "preconditions": [
                        f"{user_type}账号已登录",
                        f"{discount}活动正在进行",
                        f"{provider}服务正常",
                        "测试商品参与优惠活动"
                    ],
                    "test_steps": [
                        "1. 选择参与优惠的商品",
                        f"2. 应用{discount}优惠",
                        "3. 确认优惠后价格",
                        "4. 进入支付页面",
                        f"5. 选择{provider}支付",
                        "6. 确认分期金额基于优惠后价格",
                        "7. 完成BNPL支付流程",
                        "8. 验证订单金额计算正确"
                    ],
                    "expected_results": [
                        f"{discount}优惠正确应用",
                        "BNPL分期基于优惠后价格计算",
                        "订单总金额计算正确",
                        "优惠明细清晰显示",
                        "支付流程正常完成"
                    ],
                    "test_data": {
                        "user_type": user_type,
                        "discount_type": discount,
                        "provider": provider,
                        "original_price": "200美元",
                        "discount_amount": "20-50美元"
                    }
                }
                cases.append(case)
                case_id += 1
    
    return cases

def generate_exception_cases():
    """生成异常场景测试用例"""
    cases = []
    case_id = 1
    
    providers = ["Affirm", "Klarna", "Afterpay"]
    exception_scenarios = [
        ("网络超时", "支付过程中网络连接超时"),
        ("服务商维护", "BNPL服务商系统维护"),
        ("审批拒绝", "用户信用审批被拒绝"),
        ("重复提交", "用户重复提交支付请求"),
        ("浏览器关闭", "支付过程中意外关闭浏览器")
    ]
    
    for provider in providers:
        for scenario_name, scenario_desc in exception_scenarios:
            case = {
                "case_id": f"BNPL_EXCEPTION_{case_id:03d}",
                "case_name": f"异常处理-{provider}-{scenario_name}异常，系统正确处理，订单状态一致",
                "priority": "中",
                "test_type": "异常测试",
                "preconditions": [
                    "正常的BNPL支付环境",
                    f"模拟{scenario_name}异常条件",
                    "测试订单准备就绪"
                ],
                "test_steps": [
                    "1. 正常进入BNPL支付流程",
                    f"2. 在关键步骤触发{scenario_name}",
                    "3. 观察系统响应",
                    "4. 检查订单状态",
                    "5. 验证错误处理机制",
                    "6. 确认用户体验"
                ],
                "expected_results": [
                    "系统能正确识别异常情况",
                    "显示友好的错误提示信息",
                    "订单状态不会出现异常",
                    "用户可以重新尝试或选择其他支付方式",
                    "不会产生重复扣款或订单"
                ],
                "test_data": {
                    "provider": provider,
                    "exception_type": scenario_name,
                    "scenario_description": scenario_desc
                }
            }
            cases.append(case)
            case_id += 1
    
    return cases

def main():
    """主函数"""
    test_cases = {
        "title": "BNPL先享后付功能测试用例",
        "version": "v2.0",
        "created_date": datetime.now().strftime("%Y-%m-%d"),
        "test_modules": [
            {
                "module_name": "BNPL购买流程测试",
                "description": "验证不同用户在不同平台使用BNPL支付的完整流程",
                "test_cases": generate_purchase_cases()
            },
            {
                "module_name": "BNPL退款场景测试", 
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": generate_refund_cases()
            },
            {
                "module_name": "BNPL优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的叠加使用",
                "test_cases": generate_discount_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "验证各种异常情况下的系统处理能力",
                "test_cases": generate_exception_cases()
            }
        ]
    }
    
    # 导出JSON文件
    with open("bnpl_test_cases_v2.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    total_cases = sum(len(module["test_cases"]) for module in test_cases["test_modules"])
    print(f"✅ BNPL测试用例已生成: bnpl_test_cases_v2.json")
    print(f"📊 总用例数: {total_cases}个")
    for module in test_cases["test_modules"]:
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例")

if __name__ == "__main__":
    main()

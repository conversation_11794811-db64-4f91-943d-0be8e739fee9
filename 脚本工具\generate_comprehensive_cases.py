#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL全面测试用例生成器
重点关注异常流问题，生成500-600个用例
反向用例占60-70%
"""

import json
from datetime import datetime

class ComprehensiveBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 精简后的购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接购买"
        ]
        
        # 精简后的商品类型
        self.product_types = [
            "正常商品",
            "限时折扣商品",
            "自由捆绑商品"
        ]
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价"
        ]
        
        # 7大类异常场景（重点扩展）
        self.exception_categories = {
            "网络异常": [
                "网络超时", "网络中断", "网络波动", "连接失败", 
                "DNS解析失败", "代理服务器异常", "CDN异常"
            ],
            "系统异常": [
                "服务商维护", "API调用失败", "数据同步异常", "系统崩溃",
                "服务器过载", "数据库异常", "缓存失效", "负载均衡异常"
            ],
            "支付异常": [
                "支付中断", "支付超时", "支付取消", "余额不足",
                "支付限额", "支付频率限制", "支付渠道异常", "分期方案异常"
            ],
            "业务异常": [
                "商品缺货", "价格变动", "库存不足", "活动过期",
                "权限不足", "商品下架", "地区限制", "年龄限制"
            ],
            "用户操作异常": [
                "浏览器关闭", "应用崩溃", "重复操作", "非法操作",
                "快速点击", "页面刷新", "返回操作", "多窗口操作"
            ],
            "数据异常": [
                "数据篡改", "数据丢失", "数据不一致", "格式错误",
                "编码异常", "字符集异常", "数据溢出", "空值异常"
            ],
            "安全异常": [
                "会话过期", "权限验证失败", "跨站请求", "重放攻击",
                "SQL注入", "XSS攻击", "CSRF攻击", "令牌失效"
            ]
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_positive_cases(self):
        """生成正向用例（约30-40%）"""
        cases = []
        
        # 基础支付流程
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                for scenario in self.purchase_scenarios:
                    for product_type in self.product_types:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": scenario,
                                "operation": f"选择{product_type}进入{scenario}",
                                "action": "点击结算并使用BNPL先享后付功能",
                                "sub_action": "选择分期方案并确认支付",
                                "expected_result": "扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知"
                            },
                            "priority": "高"
                        }
                        cases.append(case)
        
        # 优惠叠加流程
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                if user_type == "游客":
                    continue
                for discount in self.discount_types:
                    if discount == "优惠码" and user_type == "小B会员":
                        continue
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "优惠叠加支付",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{discount}优惠结算",
                            "operation": f"选择参与{discount}活动的商品进入结算",
                            "action": f"应用{discount}后使用BNPL先享后付功能",
                            "sub_action": "确认优惠后价格并完成分期支付",
                            "expected_result": f"{discount}优惠正确应用，BNPL基于折后价计算，分期方案准确显示，支付流程正常，订单状态正确更新"
                        },
                        "priority": "中"
                    }
                    cases.append(case)
        
        # 退款处理流程
        refund_timings = ["订单确认后立即退款", "发货前退款", "发货后退款", "收货后退款"]
        refund_types = ["全额退款", "部分退款"]
        
        for timing in refund_timings:
            for refund_type in refund_types:
                for platform in ["Web", "H5"]:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "退款处理",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{timing}场景",
                            "operation": f"对BNPL支付订单发起{refund_type}",
                            "action": "提交退款申请并处理",
                            "sub_action": "调用BNPL退款接口并更新状态",
                            "expected_result": f"退款申请成功提交，BNPL接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算"
                        },
                        "priority": "高"
                    }
                    cases.append(case)
        
        return cases

    def generate_negative_cases(self):
        """生成反向用例（约60-70%）- 重点关注异常流问题"""
        cases = []
        
        # 为每个异常类别生成大量测试用例
        for category, exceptions in self.exception_categories.items():
            for exception in exceptions:
                # 每个异常在每个平台都要测试
                for platform in self.platform_users.keys():
                    for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                        # 基础异常场景
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": f"{category}处理",
                            "case_type": "反向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{exception}异常场景",
                                "operation": "正常进入BNPL支付流程",
                                "action": f"在支付过程中触发{exception}异常",
                                "sub_action": "观察系统异常处理和恢复机制",
                                "expected_result": f"系统正确识别{exception}异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整"
                            },
                            "exception_category": category,
                            "exception_type": exception,
                            "priority": "高" if category in ["支付异常", "系统异常"] else "中"
                        }
                        cases.append(case)
                        
                        # 异常恢复场景
                        recovery_case = {
                            "case_id": self.get_case_id(),
                            "case_name": f"{category}恢复",
                            "case_type": "反向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{exception}异常恢复场景",
                                "operation": f"在{exception}异常发生后",
                                "action": "尝试恢复并重新进行BNPL支付",
                                "sub_action": "验证异常恢复后的系统状态",
                                "expected_result": f"{exception}异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持，用户体验流畅"
                            },
                            "exception_category": category,
                            "exception_type": f"{exception}_恢复",
                            "priority": "中"
                        }
                        cases.append(recovery_case)
        
        # 组合异常场景
        combination_scenarios = [
            ("网络异常", "支付异常"),
            ("系统异常", "业务异常"),
            ("用户操作异常", "数据异常"),
            ("安全异常", "网络异常")
        ]
        
        for category1, category2 in combination_scenarios:
            exception1 = self.exception_categories[category1][0]
            exception2 = self.exception_categories[category2][0]
            
            for platform in ["Web", "H5", "Android", "iOS"]:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "组合异常处理",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{exception1}+{exception2}组合异常场景",
                        "operation": "正常进入BNPL支付流程",
                        "action": f"同时触发{exception1}和{exception2}异常",
                        "sub_action": "观察系统对组合异常的处理能力",
                        "expected_result": f"系统正确处理{exception1}和{exception2}组合异常，优先处理关键异常，显示准确错误信息，保证数据一致性，提供合理的恢复建议"
                    },
                    "exception_category": f"{category1}+{category2}",
                    "priority": "高"
                }
                cases.append(case)
        
        return cases

    def generate_boundary_cases(self):
        """生成边界值测试用例"""
        cases = []
        
        boundary_scenarios = [
            ("时间边界", "在活动开始/结束时间点进行BNPL支付"),
            ("并发边界", "多用户同时进行BNPL支付"),
            ("数据边界", "使用特殊字符商品名进行BNPL支付"),
            ("会话边界", "支付页面长时间停留后进行BNPL支付"),
            ("频率边界", "短时间内频繁进行BNPL支付"),
            ("容量边界", "购物车包含大量商品时进行BNPL支付")
        ]
        
        for scenario_name, scenario_desc in boundary_scenarios:
            for platform in self.platform_users.keys():
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "边界值测试",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}测试",
                        "operation": scenario_desc,
                        "action": "验证边界条件下的BNPL支付",
                        "sub_action": "确认系统边界值处理和响应",
                        "expected_result": f"系统正确处理{scenario_name}边界值，BNPL支付流程正常或给出合理提示，边界值验证通过，错误提示准确"
                    },
                    "boundary_type": scenario_name,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v7.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "design_principles": [
                "重点关注异常流问题，反向用例占60-70%",
                "按照BNPL先享后付_全面测试用例_v2.0.txt格式风格",
                "7大类异常全面覆盖：网络、系统、支付、业务、用户操作、数据、安全",
                "每种异常都包含基础场景和恢复场景",
                "增加组合异常场景测试",
                "目标用例数量：500-600条"
            ],
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL正向流程测试",
                "description": "验证BNPL支付的正常业务流程",
                "test_cases": self.generate_positive_cases()
            },
            {
                "module_name": "BNPL异常流程测试",
                "description": "重点验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_negative_cases()
            },
            {
                "module_name": "BNPL边界值测试",
                "description": "验证各种边界条件下的系统处理",
                "test_cases": self.generate_boundary_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = ComprehensiveBNPLGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_comprehensive_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL全面测试用例已生成: bnpl_comprehensive_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        case_type_count = {}
        for case in module["test_cases"]:
            case_type = case.get("case_type", "未分类")
            case_type_count[case_type] = case_type_count.get(case_type, 0) + 1
        
        type_info = ", ".join([f"{k}:{v}个" for k, v in case_type_count.items()])
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例 ({type_info})")
    
    # 统计正向反向比例
    total_positive = sum(len([case for case in module["test_cases"] if case.get("case_type") == "正向流程"]) 
                        for module in test_cases["test_modules"])
    total_negative = sum(len([case for case in module["test_cases"] if case.get("case_type") == "反向流程"]) 
                        for module in test_cases["test_modules"])
    
    print(f"\n📈 用例类型分布:")
    print(f"  - 正向流程: {total_positive}个 ({total_positive/test_cases['total_cases']*100:.1f}%)")
    print(f"  - 反向流程: {total_negative}个 ({total_negative/test_cases['total_cases']*100:.1f}%)")
    
    # 异常类别统计
    print(f"\n🔍 异常类别覆盖:")
    exception_stats = {}
    for module in test_cases["test_modules"]:
        for case in module["test_cases"]:
            if case.get("exception_category"):
                category = case["exception_category"]
                exception_stats[category] = exception_stats.get(category, 0) + 1
    
    for category, count in exception_stats.items():
        print(f"  - {category}: {count}个用例")

if __name__ == "__main__":
    main()

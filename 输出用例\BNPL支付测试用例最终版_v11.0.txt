BNPL支付测试用例最终版
==================================================

版本: V11.0 Final
创建日期: 2025-07-16
测试用例总数: 116个
业务范围: 仅美国地区，基于pay接口200响应机制

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试原则:
- 测试平台核心逻辑，不测试第三方功能
- 聚焦pay接口、订单生成、状态同步
- 覆盖所有用户类型和平台差异
- 验证优惠叠加和边界值处理

测试模块分布:
- 核心支付流程测试: 30个用例
- BNPL分期区间边界值测试: 24个用例
- 优惠叠加BNPL支付测试: 50个用例
- 订单状态和转换测试: 12个用例

详细测试用例:
--------------------------------------------------

核心支付流程测试
========

Android平台:
  会员:
    BNPL_001: BNPL正常支付流程
      平台: Android
      用户: 会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_002: pay接口失败处理
      平台: Android
      用户: 会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_003: 第三方BNPL异常处理
      平台: Android
      用户: 会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  小B会员:
    BNPL_004: BNPL正常支付流程
      平台: Android
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_005: pay接口失败处理
      平台: Android
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_006: 第三方BNPL异常处理
      平台: Android
      用户: 小B会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow


iOS平台:
  会员:
    BNPL_007: BNPL正常支付流程
      平台: iOS
      用户: 会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_008: pay接口失败处理
      平台: iOS
      用户: 会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_009: 第三方BNPL异常处理
      平台: iOS
      用户: 会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  小B会员:
    BNPL_010: BNPL正常支付流程
      平台: iOS
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_011: pay接口失败处理
      平台: iOS
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_012: 第三方BNPL异常处理
      平台: iOS
      用户: 小B会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow


Web平台:
  游客:
    BNPL_013: BNPL正常支付流程
      平台: Web
      用户: 游客
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_014: pay接口失败处理
      平台: Web
      用户: 游客
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_015: 第三方BNPL异常处理
      平台: Web
      用户: 游客
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  会员:
    BNPL_016: BNPL正常支付流程
      平台: Web
      用户: 会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_017: pay接口失败处理
      平台: Web
      用户: 会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_018: 第三方BNPL异常处理
      平台: Web
      用户: 会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  小B会员:
    BNPL_019: BNPL正常支付流程
      平台: Web
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_020: pay接口失败处理
      平台: Web
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_021: 第三方BNPL异常处理
      平台: Web
      用户: 小B会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow


H5平台:
  游客:
    BNPL_022: BNPL正常支付流程
      平台: H5
      用户: 游客
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_023: pay接口失败处理
      平台: H5
      用户: 游客
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_024: 第三方BNPL异常处理
      平台: H5
      用户: 游客
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  会员:
    BNPL_025: BNPL正常支付流程
      平台: H5
      用户: 会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_026: pay接口失败处理
      平台: H5
      用户: 会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_027: 第三方BNPL异常处理
      平台: H5
      用户: 会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow

  小B会员:
    BNPL_028: BNPL正常支付流程
      平台: H5
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_029: pay接口失败处理
      平台: H5
      用户: 小B会员
      场景: 核心支付流程
      操作: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息
      测试数据:
        - 场景类型: core_payment_flow

    BNPL_030: 第三方BNPL异常处理
      平台: H5
      用户: 小B会员
      场景: 核心支付流程
      操作: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式
      测试数据:
        - 场景类型: core_payment_flow


BNPL分期区间边界值测试
=============

Web平台:
  游客:
    BNPL_100: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(低于所有BNPL最小值)
      预期结果: 系统正确显示可用BNPL选项: 无可用选项
      测试数据:
        - 金额: US$0.49
        - 可用BNPL: []
        - 说明: 低于所有BNPL最小值
        - 场景类型: boundary_value_test

    BNPL_101: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最小值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$0.5
        - 可用BNPL: ['Klarna']
        - 说明: Klarna最小值
        - 场景类型: boundary_value_test

    BNPL_102: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值下限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$0.99
        - 可用BNPL: ['Klarna']
        - 说明: Afterpay最小值下限
        - 场景类型: boundary_value_test

    BNPL_103: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      测试数据:
        - 金额: US$1.0
        - 可用BNPL: ['Klarna', 'Afterpay']
        - 说明: Afterpay最小值
        - 场景类型: boundary_value_test

    BNPL_104: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最小值下限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      测试数据:
        - 金额: US$49.99
        - 可用BNPL: ['Klarna', 'Afterpay']
        - 说明: Affirm最小值下限
        - 场景类型: boundary_value_test

    BNPL_105: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(全支持区间开始)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      测试数据:
        - 金额: US$50.0
        - 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']
        - 说明: 全支持区间开始
        - 场景类型: boundary_value_test

    BNPL_106: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      测试数据:
        - 金额: US$4000.0
        - 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']
        - 说明: Afterpay最大值
        - 场景类型: boundary_value_test

    BNPL_107: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值上限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      测试数据:
        - 金额: US$4000.01
        - 可用BNPL: ['Klarna', 'Affirm']
        - 说明: Afterpay最大值上限
        - 场景类型: boundary_value_test

    BNPL_108: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      测试数据:
        - 金额: US$30000.0
        - 可用BNPL: ['Klarna', 'Affirm']
        - 说明: Affirm最大值
        - 场景类型: boundary_value_test

    BNPL_109: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(仅Klarna支持)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$30000.01
        - 可用BNPL: ['Klarna']
        - 说明: 仅Klarna支持
        - 场景类型: boundary_value_test

    BNPL_110: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$999999.99
        - 可用BNPL: ['Klarna']
        - 说明: Klarna最大值
        - 场景类型: boundary_value_test

    BNPL_111: BNPL分期区间边界值验证
      平台: Web
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(超出所有BNPL最大值)
      预期结果: 系统正确显示可用BNPL选项: 无可用选项
      测试数据:
        - 金额: US$1000000.0
        - 可用BNPL: []
        - 说明: 超出所有BNPL最大值
        - 场景类型: boundary_value_test


H5平台:
  游客:
    BNPL_112: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(低于所有BNPL最小值)
      预期结果: 系统正确显示可用BNPL选项: 无可用选项
      测试数据:
        - 金额: US$0.49
        - 可用BNPL: []
        - 说明: 低于所有BNPL最小值
        - 场景类型: boundary_value_test

    BNPL_113: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最小值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$0.5
        - 可用BNPL: ['Klarna']
        - 说明: Klarna最小值
        - 场景类型: boundary_value_test

    BNPL_114: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值下限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$0.99
        - 可用BNPL: ['Klarna']
        - 说明: Afterpay最小值下限
        - 场景类型: boundary_value_test

    BNPL_115: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      测试数据:
        - 金额: US$1.0
        - 可用BNPL: ['Klarna', 'Afterpay']
        - 说明: Afterpay最小值
        - 场景类型: boundary_value_test

    BNPL_116: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最小值下限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      测试数据:
        - 金额: US$49.99
        - 可用BNPL: ['Klarna', 'Afterpay']
        - 说明: Affirm最小值下限
        - 场景类型: boundary_value_test

    BNPL_117: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(全支持区间开始)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      测试数据:
        - 金额: US$50.0
        - 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']
        - 说明: 全支持区间开始
        - 场景类型: boundary_value_test

    BNPL_118: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      测试数据:
        - 金额: US$4000.0
        - 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']
        - 说明: Afterpay最大值
        - 场景类型: boundary_value_test

    BNPL_119: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值上限)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      测试数据:
        - 金额: US$4000.01
        - 可用BNPL: ['Klarna', 'Affirm']
        - 说明: Afterpay最大值上限
        - 场景类型: boundary_value_test

    BNPL_120: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      测试数据:
        - 金额: US$30000.0
        - 可用BNPL: ['Klarna', 'Affirm']
        - 说明: Affirm最大值
        - 场景类型: boundary_value_test

    BNPL_121: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(仅Klarna支持)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$30000.01
        - 可用BNPL: ['Klarna']
        - 说明: 仅Klarna支持
        - 场景类型: boundary_value_test

    BNPL_122: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最大值)
      预期结果: 系统正确显示可用BNPL选项: ['Klarna']
      测试数据:
        - 金额: US$999999.99
        - 可用BNPL: ['Klarna']
        - 说明: Klarna最大值
        - 场景类型: boundary_value_test

    BNPL_123: BNPL分期区间边界值验证
      平台: H5
      用户: 游客
      场景: 边界值测试
      操作: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(超出所有BNPL最大值)
      预期结果: 系统正确显示可用BNPL选项: 无可用选项
      测试数据:
        - 金额: US$1000000.0
        - 可用BNPL: []
        - 说明: 超出所有BNPL最大值
        - 场景类型: boundary_value_test


优惠叠加BNPL支付测试
============

Web平台:
  游客:
    BNPL_200: 优惠叠加BNPL支付测试
      平台: Web
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_206: 优惠叠加BNPL支付测试
      平台: Web
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_209: 优惠叠加BNPL支付测试
      平台: Web
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

  会员:
    BNPL_201: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_203: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_205: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 会员折扣
        - 说明: 会员折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_207: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_210: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_212: 优惠叠加BNPL支付测试
      平台: Web
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test

  小B会员:
    BNPL_202: 优惠叠加BNPL支付测试
      平台: Web
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_204: 优惠叠加BNPL支付测试
      平台: Web
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_208: 优惠叠加BNPL支付测试
      平台: Web
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_211: 优惠叠加BNPL支付测试
      平台: Web
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_213: 优惠叠加BNPL支付测试
      平台: Web
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test


H5平台:
  游客:
    BNPL_214: 优惠叠加BNPL支付测试
      平台: H5
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_220: 优惠叠加BNPL支付测试
      平台: H5
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_223: 优惠叠加BNPL支付测试
      平台: H5
      用户: 游客
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

  会员:
    BNPL_215: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_217: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_219: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 会员折扣
        - 说明: 会员折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_221: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_224: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_226: 优惠叠加BNPL支付测试
      平台: H5
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test

  小B会员:
    BNPL_216: 优惠叠加BNPL支付测试
      平台: H5
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_218: 优惠叠加BNPL支付测试
      平台: H5
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_222: 优惠叠加BNPL支付测试
      平台: H5
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_225: 优惠叠加BNPL支付测试
      平台: H5
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_227: 优惠叠加BNPL支付测试
      平台: H5
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test


Android平台:
  会员:
    BNPL_228: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_230: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_232: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 会员折扣
        - 说明: 会员折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_233: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_235: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_237: 优惠叠加BNPL支付测试
      平台: Android
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test

  小B会员:
    BNPL_229: 优惠叠加BNPL支付测试
      平台: Android
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_231: 优惠叠加BNPL支付测试
      平台: Android
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_234: 优惠叠加BNPL支付测试
      平台: Android
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_236: 优惠叠加BNPL支付测试
      平台: Android
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_238: 优惠叠加BNPL支付测试
      平台: Android
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test


iOS平台:
  会员:
    BNPL_239: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_241: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_243: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 会员折扣
        - 说明: 会员折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_244: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_246: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_248: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test

  小B会员:
    BNPL_240: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 优惠券
        - 说明: 优惠券与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_242: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: O币抵扣
        - 说明: O币抵扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_245: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 限时折扣
        - 说明: 限时折扣与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_247: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 自由捆绑
        - 说明: 自由捆绑优惠与BNPL叠加使用
        - 场景类型: discount_integration_test

    BNPL_249: 优惠叠加BNPL支付测试
      平台: iOS
      用户: 小B会员
      场景: 优惠叠加测试
      操作: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      测试数据:
        - 优惠类型: 多重优惠
        - 说明: 多种优惠同时与BNPL叠加使用
        - 场景类型: discount_integration_test


订单状态和转换测试
=========

Web平台:
  会员:
    BNPL_300: 待支付订单转BNPL支付
      平台: Web
      用户: 会员
      场景: 订单状态管理
      操作: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新
      测试数据:
        - 场景类型: order_status_test

    BNPL_301: BNPL支付状态同步
      平台: Web
      用户: 会员
      场景: 订单状态管理
      操作: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单
      测试数据:
        - 场景类型: order_status_test

    BNPL_302: 支付方式转换
      平台: Web
      用户: 会员
      场景: 订单状态管理
      操作: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确
      测试数据:
        - 场景类型: order_status_test

  小B会员:
    BNPL_303: 待支付订单转BNPL支付
      平台: Web
      用户: 小B会员
      场景: 订单状态管理
      操作: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新
      测试数据:
        - 场景类型: order_status_test

    BNPL_304: BNPL支付状态同步
      平台: Web
      用户: 小B会员
      场景: 订单状态管理
      操作: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单
      测试数据:
        - 场景类型: order_status_test

    BNPL_305: 支付方式转换
      平台: Web
      用户: 小B会员
      场景: 订单状态管理
      操作: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确
      测试数据:
        - 场景类型: order_status_test


H5平台:
  会员:
    BNPL_306: 待支付订单转BNPL支付
      平台: H5
      用户: 会员
      场景: 订单状态管理
      操作: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新
      测试数据:
        - 场景类型: order_status_test

    BNPL_307: BNPL支付状态同步
      平台: H5
      用户: 会员
      场景: 订单状态管理
      操作: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单
      测试数据:
        - 场景类型: order_status_test

    BNPL_308: 支付方式转换
      平台: H5
      用户: 会员
      场景: 订单状态管理
      操作: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确
      测试数据:
        - 场景类型: order_status_test

  小B会员:
    BNPL_309: 待支付订单转BNPL支付
      平台: H5
      用户: 小B会员
      场景: 订单状态管理
      操作: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新
      测试数据:
        - 场景类型: order_status_test

    BNPL_310: BNPL支付状态同步
      平台: H5
      用户: 小B会员
      场景: 订单状态管理
      操作: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单
      测试数据:
        - 场景类型: order_status_test

    BNPL_311: 支付方式转换
      平台: H5
      用户: 小B会员
      场景: 订单状态管理
      操作: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确
      测试数据:
        - 场景类型: order_status_test

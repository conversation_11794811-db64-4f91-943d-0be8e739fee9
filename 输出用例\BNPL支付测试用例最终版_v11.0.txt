BNPL支付测试用例最终版
==================================================

版本: V11.0 Final
创建日期: 2025-07-16
测试用例总数: 446个
业务范围: 仅美国地区，基于pay接口200响应机制

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试原则:
- 测试平台核心逻辑，不测试第三方功能
- 聚焦pay接口、订单生成、状态同步
- 覆盖所有用户类型和平台差异
- 验证优惠叠加和边界值处理

测试模块分布:
- 核心支付流程测试: 90个用例
- BNPL分期区间边界值测试: 240个用例
- 优惠叠加BNPL支付测试: 66个用例
- 订单状态和转换测试: 50个用例

详细测试用例:
--------------------------------------------------

核心支付流程测试
========

Android平台:
  会员:
    BNPL_001: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_002: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_003: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_004: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_005: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_006: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_007: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_008: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_009: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_010: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_011: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_012: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_013: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_014: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_015: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_016: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_017: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_018: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式


iOS平台:
  会员:
    BNPL_019: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_020: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_021: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_022: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_023: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_024: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_025: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_026: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_027: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_028: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_029: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_030: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_031: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_032: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_033: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_034: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_035: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_036: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式


Web平台:
  游客:
    BNPL_037: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_038: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_039: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_040: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_041: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_042: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_043: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_044: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_045: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_046: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_047: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_048: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_049: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_050: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_051: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_052: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_053: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_054: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_055: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_056: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_057: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_058: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_059: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_060: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_061: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_062: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_063: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式


H5平台:
  游客:
    BNPL_064: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_065: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_066: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_067: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_068: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_069: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_070: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_071: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_072: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_073: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_074: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_075: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_076: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_077: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_078: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_079: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_080: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_081: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_082: 添加商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_083: 添加商品到购物车结算
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_084: 添加商品到购物车结算
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_085: 添加商品到直接购买
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_086: 添加商品到直接购买
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_087: 添加商品到直接购买
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_088: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口
      预期结果: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_089: 添加商品到待支付订单转BNPL
      步骤: 选择BNPL支付，pay接口调用失败
      预期结果: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_090: 添加商品到待支付订单转BNPL
      步骤1: 调用pay接口成功生成订单
      步骤2: 第三方BNPL平台异常
      步骤3: 系统生成待支付订单
      预期结果: 订单正确生成为待支付状态，用户可重新选择支付方式


BNPL分期区间边界值测试
=============

Android平台:
  会员:
    BNPL_200: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_201: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_202: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_203: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_204: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_205: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_206: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_207: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_208: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_209: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_210: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_211: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_212: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_213: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_214: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_215: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_216: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_217: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_218: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_219: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_220: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_221: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_222: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_223: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  小B会员:
    BNPL_224: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_225: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_226: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_227: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_228: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_229: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_230: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_231: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_232: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_233: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_234: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_235: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_236: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_237: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_238: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_239: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_240: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_241: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_242: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_243: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_244: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_245: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_246: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_247: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式


iOS平台:
  会员:
    BNPL_248: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_249: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_250: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_251: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_252: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_253: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_254: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_255: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_256: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_257: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_258: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_259: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_260: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_261: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_262: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_263: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_264: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_265: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_266: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_267: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_268: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_269: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_270: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_271: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  小B会员:
    BNPL_272: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_273: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_274: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_275: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_276: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_277: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_278: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_279: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_280: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_281: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_282: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_283: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_284: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_285: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_286: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_287: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_288: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_289: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_290: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_291: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_292: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_293: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_294: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_295: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式


Web平台:
  游客:
    BNPL_296: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_297: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_298: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_299: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_300: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_301: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_302: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_303: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_304: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_305: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_306: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_307: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_308: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_309: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_310: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_311: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_312: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_313: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_314: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_315: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_316: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_317: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_318: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_319: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  会员:
    BNPL_320: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_321: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_322: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_323: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_324: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_325: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_326: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_327: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_328: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_329: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_330: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_331: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_332: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_333: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_334: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_335: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_336: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_337: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_338: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_339: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_340: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_341: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_342: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_343: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  小B会员:
    BNPL_344: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_345: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_346: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_347: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_348: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_349: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_350: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_351: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_352: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_353: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_354: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_355: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_356: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_357: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_358: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_359: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_360: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_361: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_362: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_363: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_364: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_365: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_366: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_367: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式


H5平台:
  游客:
    BNPL_368: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_369: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_370: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_371: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_372: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_373: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_374: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_375: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_376: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_377: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_378: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_379: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_380: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_381: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_382: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_383: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_384: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_385: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_386: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_387: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_388: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_389: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_390: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_391: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  会员:
    BNPL_392: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_393: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_394: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_395: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_396: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_397: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_398: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_399: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_400: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_401: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_402: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_403: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_404: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_405: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_406: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_407: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_408: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_409: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_410: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_411: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_412: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_413: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_414: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_415: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式

  小B会员:
    BNPL_416: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$0.49
      支付方式: 无可用支付方式

    BNPL_417: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.5
      支付方式: 仅Klarna可用

    BNPL_418: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.51
      支付方式: 仅Klarna可用

    BNPL_419: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$0.99
      支付方式: 仅Klarna可用

    BNPL_420: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.0
      支付方式: Klarna、Afterpay

    BNPL_421: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$1.01
      支付方式: Klarna、Afterpay

    BNPL_422: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Afterpay
      金额: US$49.99
      支付方式: Klarna、Afterpay

    BNPL_423: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_424: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$50.01
      支付方式: Affirm、Afterpay、Klarna

    BNPL_425: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$100.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_426: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$500.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_427: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$1000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_428: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$3999.99
      支付方式: Affirm、Afterpay、Klarna

    BNPL_429: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Affirm、Afterpay、Klarna
      金额: US$4000.0
      支付方式: Affirm、Afterpay、Klarna

    BNPL_430: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$4000.01
      支付方式: Klarna、Affirm

    BNPL_431: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$10000.0
      支付方式: Klarna、Affirm

    BNPL_432: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$29999.99
      支付方式: Klarna、Affirm

    BNPL_433: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: Klarna、Affirm
      金额: US$30000.0
      支付方式: Klarna、Affirm

    BNPL_434: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$30000.01
      支付方式: 仅Klarna可用

    BNPL_435: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$100000.0
      支付方式: 仅Klarna可用

    BNPL_436: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$500000.0
      支付方式: 仅Klarna可用

    BNPL_437: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 仅Klarna可用
      金额: US$999999.99
      支付方式: 仅Klarna可用

    BNPL_438: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.0
      支付方式: 无可用支付方式

    BNPL_439: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期结果: 系统正确显示可用支付方式: 无可用支付方式
      金额: US$1000000.01
      支付方式: 无可用支付方式


优惠叠加BNPL支付测试
============

Android平台:
  会员:
    BNPL_500: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_502: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_504: 会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_505: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_507: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_509: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_511: 优惠券+会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 应用会员折扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_512: 限时折扣+会员折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_513: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_501: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_503: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_506: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_508: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_510: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_514: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


iOS平台:
  会员:
    BNPL_515: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_517: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_519: 会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_520: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_522: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_524: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_526: 优惠券+会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 应用会员折扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_527: 限时折扣+会员折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_528: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_516: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_518: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_521: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_523: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_525: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_529: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


Web平台:
  游客:
    BNPL_530: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_536: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_539: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_531: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_533: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_535: 会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_537: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_540: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_542: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_544: 优惠券+会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 应用会员折扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_545: 限时折扣+会员折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_546: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_532: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_534: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_538: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_541: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_543: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_547: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


H5平台:
  游客:
    BNPL_548: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_554: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_557: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_549: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_551: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_553: 会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_555: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_558: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_560: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_562: 优惠券+会员折扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 应用会员折扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_563: 限时折扣+会员折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用会员折扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_564: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_550: 优惠券叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_552: O币抵扣叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 使用O币抵扣
      步骤3: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_556: 限时折扣叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_559: 自由捆绑叠加BNPL支付
      步骤1: 添加自由捆绑商品到购物车
      步骤2: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_561: 优惠券+O币叠加BNPL支付
      步骤1: 添加商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_565: 全部优惠叠加BNPL支付
      步骤1: 添加限时折扣商品到购物车
      步骤2: 应用优惠券
      步骤3: 使用O币抵扣
      步骤4: 应用会员折扣
      步骤5: 选择BNPL支付
      预期结果: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


订单状态和转换测试
=========

Android平台:
  会员:
    BNPL_800: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_801: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_802: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_803: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_804: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_805: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_806: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_807: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_808: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_809: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放


iOS平台:
  会员:
    BNPL_810: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_811: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_812: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_813: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_814: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_815: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_816: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_817: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_818: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_819: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放


Web平台:
  游客:
    BNPL_820: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_821: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_822: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_823: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_824: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  会员:
    BNPL_825: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_826: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_827: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_828: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_829: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_830: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_831: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_832: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_833: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_834: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放


H5平台:
  游客:
    BNPL_835: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_836: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_837: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_838: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_839: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  会员:
    BNPL_840: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_841: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_842: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_843: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_844: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_845: 商品订单待支付订单转BNPL支付
      步骤1: 生成待支付订单
      步骤2: 重新选择BNPL支付方式
      步骤3: 完成BNPL支付
      预期结果: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_846: 商品订单BNPL支付状态同步
      步骤1: 完成BNPL第三方支付
      步骤2: 接收支付成功通知
      步骤3: 同步更新订单状态
      预期结果: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_847: 商品订单支付方式转换
      步骤1: 生成BNPL待支付订单
      步骤2: 转换为其他支付方式
      步骤3: 完成支付
      预期结果: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_848: 商品订单BNPL订单取消处理
      步骤1: 生成BNPL待支付订单
      步骤2: 用户主动取消订单
      步骤3: 系统处理取消请求
      预期结果: 订单正确取消，库存释放，状态更新为已取消

    BNPL_849: 商品订单BNPL订单超时处理
      步骤1: 生成BNPL待支付订单
      步骤2: 订单超时未支付
      步骤3: 系统自动处理超时订单
      预期结果: 超时订单自动取消，资源正确释放

BNPL支付测试用例最终版
==================================================

版本: V11.0 Final
创建日期: 2025-07-16
测试用例总数: 808个
业务范围: 仅美国地区，基于pay接口200响应机制

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试原则:
- 测试平台核心逻辑，不测试第三方功能
- 聚焦pay接口、订单生成、状态同步
- 覆盖所有用户类型和平台差异
- 验证优惠叠加和边界值处理

测试模块分布:
- 核心支付流程测试: 270个用例
- BNPL分期区间边界值测试: 240个用例
- 优惠叠加BNPL支付测试: 198个用例
- 订单状态和转换测试: 100个用例

详细测试用例:
--------------------------------------------------

核心支付流程测试
========

Android平台:
  会员:
    BNPL_001: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_002: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_003: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_004: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_005: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_006: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_007: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_008: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_009: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_010: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_011: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_012: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_013: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_014: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_015: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_016: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_017: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_018: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_019: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_020: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_021: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_022: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_023: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_024: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_025: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_026: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_027: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_028: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_029: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_030: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_031: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_032: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_033: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_034: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_035: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_036: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_037: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_038: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_039: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_040: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_041: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_042: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_043: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_044: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_045: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_046: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_047: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_048: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_049: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_050: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_051: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_052: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_053: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_054: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


iOS平台:
  会员:
    BNPL_055: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_056: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_057: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_058: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_059: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_060: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_061: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_062: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_063: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_064: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_065: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_066: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_067: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_068: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_069: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_070: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_071: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_072: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_073: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_074: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_075: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_076: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_077: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_078: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_079: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_080: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_081: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_082: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_083: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_084: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_085: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_086: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_087: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_088: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_089: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_090: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_091: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_092: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_093: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_094: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_095: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_096: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_097: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_098: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_099: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_100: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_101: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_102: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_103: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_104: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_105: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_106: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_107: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_108: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


Web平台:
  游客:
    BNPL_109: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_110: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_111: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_112: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_113: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_114: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_115: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_116: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_117: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_118: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_119: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_120: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_121: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_122: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_123: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_124: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_125: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_126: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_127: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_128: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_129: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_130: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_131: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_132: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_133: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_134: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_135: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_136: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_137: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_138: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_139: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_140: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_141: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_142: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_143: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_144: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_145: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_146: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_147: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_148: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_149: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_150: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_151: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_152: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_153: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_154: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_155: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_156: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_157: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_158: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_159: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_160: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_161: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_162: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_163: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_164: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_165: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_166: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_167: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_168: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_169: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_170: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_171: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_172: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_173: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_174: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_175: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_176: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_177: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_178: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_179: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_180: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_181: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_182: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_183: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_184: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_185: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_186: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_187: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_188: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_189: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


H5平台:
  游客:
    BNPL_190: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_191: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_192: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_193: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_194: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_195: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_196: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_197: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_198: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_199: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_200: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_201: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_202: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_203: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_204: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_205: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_206: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_207: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_208: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_209: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_210: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_211: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_212: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_213: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_214: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_215: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_216: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_217: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_218: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_219: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_220: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_221: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_222: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_223: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_224: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_225: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_226: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_227: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_228: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_229: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_230: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_231: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_232: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_233: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_234: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_235: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_236: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_237: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_238: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_239: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_240: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_241: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_242: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_243: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_244: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_245: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_246: 添加单品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_247: 添加单品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_248: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_249: 添加单品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_250: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_251: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_252: 添加单品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_253: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_254: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_255: 添加多品商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_256: 添加多品商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_257: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_258: 添加多品商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_259: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_260: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_261: 添加多品商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_262: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_263: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_264: 添加自由捆绑商品到购物车结算
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_265: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_266: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_267: 添加自由捆绑商品到直接购买
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

    BNPL_268: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并调用pay接口并处理200响应
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_269: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理pay接口失败响应
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_270: 添加自由捆绑商品到待支付订单转BNPL
      步骤: 选择BNPL支付并处理第三方平台不可用情况
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


BNPL分期区间边界值测试
=============

Android平台:
  会员:
    BNPL_200: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_201: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_202: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_203: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_204: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_205: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_206: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_207: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_208: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_209: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_210: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_211: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_212: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_213: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_214: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_215: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_216: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_217: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_218: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_219: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_220: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_221: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_222: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_223: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  小B会员:
    BNPL_224: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_225: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_226: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_227: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_228: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_229: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_230: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_231: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_232: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_233: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_234: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_235: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_236: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_237: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_238: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_239: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_240: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_241: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_242: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_243: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_244: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_245: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_246: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_247: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []


iOS平台:
  会员:
    BNPL_248: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_249: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_250: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_251: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_252: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_253: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_254: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_255: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_256: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_257: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_258: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_259: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_260: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_261: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_262: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_263: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_264: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_265: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_266: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_267: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_268: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_269: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_270: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_271: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  小B会员:
    BNPL_272: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_273: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_274: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_275: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_276: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_277: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_278: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_279: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_280: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_281: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_282: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_283: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_284: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_285: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_286: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_287: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_288: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_289: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_290: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_291: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_292: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_293: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_294: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_295: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []


Web平台:
  游客:
    BNPL_296: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_297: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_298: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_299: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_300: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_301: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_302: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_303: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_304: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_305: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_306: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_307: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_308: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_309: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_310: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_311: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_312: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_313: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_314: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_315: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_316: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_317: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_318: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_319: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  会员:
    BNPL_320: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_321: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_322: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_323: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_324: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_325: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_326: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_327: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_328: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_329: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_330: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_331: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_332: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_333: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_334: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_335: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_336: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_337: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_338: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_339: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_340: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_341: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_342: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_343: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  小B会员:
    BNPL_344: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_345: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_346: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_347: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_348: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_349: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_350: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_351: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_352: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_353: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_354: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_355: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_356: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_357: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_358: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_359: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_360: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_361: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_362: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_363: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_364: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_365: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_366: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_367: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []


H5平台:
  游客:
    BNPL_368: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_369: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_370: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_371: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_372: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_373: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_374: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_375: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_376: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_377: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_378: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_379: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_380: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_381: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_382: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_383: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_384: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_385: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_386: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_387: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_388: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_389: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_390: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_391: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  会员:
    BNPL_392: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_393: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_394: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_395: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_396: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_397: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_398: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_399: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_400: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_401: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_402: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_403: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_404: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_405: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_406: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_407: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_408: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_409: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_410: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_411: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_412: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_413: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_414: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_415: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []

  小B会员:
    BNPL_416: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_417: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_418: 添加商品到购物车(总价US$0.51)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.51
      可用BNPL: ['Klarna']

    BNPL_419: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_420: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_421: 添加商品到购物车(总价US$1.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.01
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_422: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_423: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_424: 添加商品到购物车(总价US$50.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.01
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_425: 添加商品到购物车(总价US$100.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$100.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_426: 添加商品到购物车(总价US$500.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$500.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_427: 添加商品到购物车(总价US$1000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$1000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_428: 添加商品到购物车(总价US$3999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$3999.99
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_429: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_430: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_431: 添加商品到购物车(总价US$10000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$10000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_432: 添加商品到购物车(总价US$29999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$29999.99
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_433: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_434: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_435: 添加商品到购物车(总价US$100000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$100000.0
      可用BNPL: ['Klarna']

    BNPL_436: 添加商品到购物车(总价US$500000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$500000.0
      可用BNPL: ['Klarna']

    BNPL_437: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_438: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []

    BNPL_439: 添加商品到购物车(总价US$1000000.01)
      步骤: 选择BNPL支付方式
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.01
      可用BNPL: []


优惠叠加BNPL支付测试
============

Android平台:
  会员:
    BNPL_500: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_502: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_504: 添加单品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_505: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_507: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_509: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_511: 添加单品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_512: 添加单品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_513: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_515: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_517: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_519: 添加多品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_520: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_522: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_524: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_526: 添加多品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_527: 添加多品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_528: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_530: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_532: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_534: 添加自由捆绑商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_535: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_537: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_539: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_541: 添加自由捆绑商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_542: 添加自由捆绑商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_543: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_501: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_503: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_506: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_508: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_510: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_514: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_516: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_518: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_521: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_523: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_525: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_529: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_531: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_533: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_536: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_538: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_540: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_544: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


iOS平台:
  会员:
    BNPL_545: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_547: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_549: 添加单品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_550: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_552: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_554: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_556: 添加单品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_557: 添加单品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_558: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_560: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_562: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_564: 添加多品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_565: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_567: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_569: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_571: 添加多品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_572: 添加多品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_573: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_575: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_577: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_579: 添加自由捆绑商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_580: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_582: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_584: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_586: 添加自由捆绑商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_587: 添加自由捆绑商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_588: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_546: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_548: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_551: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_553: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_555: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_559: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_561: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_563: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_566: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_568: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_570: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_574: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_576: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_578: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_581: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_583: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_585: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_589: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


Web平台:
  游客:
    BNPL_590: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_596: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_599: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_608: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_614: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_617: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_626: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_632: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_635: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_591: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_593: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_595: 添加单品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_597: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_600: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_602: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_604: 添加单品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_605: 添加单品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_606: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_609: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_611: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_613: 添加多品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_615: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_618: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_620: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_622: 添加多品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_623: 添加多品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_624: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_627: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_629: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_631: 添加自由捆绑商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_633: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_636: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_638: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_640: 添加自由捆绑商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_641: 添加自由捆绑商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_642: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_592: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_594: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_598: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_601: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_603: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_607: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_610: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_612: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_616: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_619: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_621: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_625: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_628: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_630: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_634: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_637: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_639: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_643: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


H5平台:
  游客:
    BNPL_644: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_650: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_653: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_662: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_668: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_671: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_680: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_686: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_689: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_645: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_647: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_649: 添加单品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_651: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_654: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_656: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_658: 添加单品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_659: 添加单品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_660: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_663: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_665: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_667: 添加多品商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_669: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_672: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_674: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_676: 添加多品商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_677: 添加多品商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_678: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_681: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_683: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_685: 添加自由捆绑商品并应用会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_687: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_690: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_692: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_694: 添加自由捆绑商品并应用优惠券+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+会员折扣

    BNPL_695: 添加自由捆绑商品并应用限时折扣+会员折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣+会员折扣

    BNPL_696: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

  小B会员:
    BNPL_646: 添加单品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_648: 添加单品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_652: 添加单品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_655: 添加单品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_657: 添加单品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_661: 添加单品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_664: 添加多品商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_666: 添加多品商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_670: 添加多品商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_673: 添加多品商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_675: 添加多品商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_679: 添加多品商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠

    BNPL_682: 添加自由捆绑商品并应用优惠券
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_684: 添加自由捆绑商品并应用O币抵扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_688: 添加自由捆绑商品并应用限时折扣
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_691: 添加自由捆绑商品并应用自由捆绑
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_693: 添加自由捆绑商品并应用优惠券+O币
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券+O币

    BNPL_697: 添加自由捆绑商品并应用全部优惠
      步骤: 计算优惠后最终价格并选择BNPL支付
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 全部优惠


订单状态和转换测试
=========

Android平台:
  会员:
    BNPL_800: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_801: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_802: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_803: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_804: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_805: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_806: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_807: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_808: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_809: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_810: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_811: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_812: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_813: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_814: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_815: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_816: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_817: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_818: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_819: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放


iOS平台:
  会员:
    BNPL_820: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_821: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_822: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_823: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_824: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_825: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_826: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_827: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_828: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_829: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_830: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_831: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_832: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_833: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_834: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_835: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_836: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_837: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_838: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_839: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放


Web平台:
  游客:
    BNPL_840: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_841: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_842: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_843: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_844: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_845: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_846: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_847: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_848: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_849: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  会员:
    BNPL_850: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_851: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_852: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_853: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_854: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_855: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_856: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_857: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_858: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_859: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_860: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_861: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_862: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_863: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_864: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_865: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_866: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_867: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_868: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_869: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放


H5平台:
  游客:
    BNPL_870: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_871: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_872: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_873: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_874: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_875: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_876: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_877: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_878: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_879: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  会员:
    BNPL_880: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_881: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_882: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_883: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_884: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_885: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_886: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_887: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_888: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_889: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

  小B会员:
    BNPL_890: 对单品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_891: 对单品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_892: 对单品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_893: 对单品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_894: 对单品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

    BNPL_895: 对多品商品订单从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_896: 对多品商品订单BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_897: 对多品商品订单待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

    BNPL_898: 对多品商品订单用户主动取消BNPL待支付订单
      步骤: 处理订单取消请求
      预期: 订单正确取消，库存释放，状态更新为已取消

    BNPL_899: 对多品商品订单BNPL待支付订单超时自动处理
      步骤: 检测订单超时并自动处理
      预期: 超时订单自动取消，资源正确释放

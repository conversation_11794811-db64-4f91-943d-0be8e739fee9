BNPL支付测试用例最终版
==================================================

版本: V11.0 Final
创建日期: 2025-07-16
测试用例总数: 116个
业务范围: 仅美国地区，基于pay接口200响应机制

BNPL分期区间:
- Affirm: US$50.00 - US$30,000.00
- Afterpay: US$1.00 - US$4,000.00
- Klarna: US$0.50 - US$999,999.99

测试原则:
- 测试平台核心逻辑，不测试第三方功能
- 聚焦pay接口、订单生成、状态同步
- 覆盖所有用户类型和平台差异
- 验证优惠叠加和边界值处理

测试模块分布:
- 核心支付流程测试: 30个用例
- BNPL分期区间边界值测试: 24个用例
- 优惠叠加BNPL支付测试: 50个用例
- 订单状态和转换测试: 12个用例

详细测试用例:
--------------------------------------------------

核心支付流程测试
========

Android平台:
  会员:
    BNPL_001: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_002: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_003: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_004: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_005: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_006: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


iOS平台:
  会员:
    BNPL_007: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_008: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_009: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_010: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_011: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_012: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


Web平台:
  游客:
    BNPL_013: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_014: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_015: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_016: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_017: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_018: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_019: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_020: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_021: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


H5平台:
  游客:
    BNPL_022: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_023: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_024: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  会员:
    BNPL_025: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_026: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_027: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式

  小B会员:
    BNPL_028: 选择商品并使用BNPL支付
      步骤: 调用pay接口并处理200响应
      子步骤: 生成订单并跳转第三方完成BNPL支付
      预期: pay接口响应200，订单正确生成，BNPL支付流程正常

    BNPL_029: 选择BNPL支付但pay接口调用失败
      步骤: 处理pay接口失败响应
      子步骤: 显示错误信息，不生成订单
      预期: pay接口失败时不生成订单，向用户显示明确错误信息

    BNPL_030: pay接口成功但第三方BNPL平台异常
      步骤: 处理第三方平台不可用情况
      子步骤: 生成待支付订单，提供重新支付选项
      预期: 订单正确生成为待支付状态，用户可重新选择支付方式


BNPL分期区间边界值测试
=============

Web平台:
  游客:
    BNPL_100: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(低于所有BNPL最小值)
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_101: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最小值)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_102: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值下限)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_103: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_104: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最小值下限)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_105: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(全支持区间开始)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_106: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_107: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值上限)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_108: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_109: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(仅Klarna支持)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_110: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_111: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(超出所有BNPL最大值)
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []


H5平台:
  游客:
    BNPL_112: 添加商品到购物车(总价US$0.49)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(低于所有BNPL最小值)
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$0.49
      可用BNPL: []

    BNPL_113: 添加商品到购物车(总价US$0.5)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最小值)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.5
      可用BNPL: ['Klarna']

    BNPL_114: 添加商品到购物车(总价US$0.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值下限)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$0.99
      可用BNPL: ['Klarna']

    BNPL_115: 添加商品到购物车(总价US$1.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最小值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$1.0
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_116: 添加商品到购物车(总价US$49.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最小值下限)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
      金额: US$49.99
      可用BNPL: ['Klarna', 'Afterpay']

    BNPL_117: 添加商品到购物车(总价US$50.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(全支持区间开始)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$50.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_118: 添加商品到购物车(总价US$4000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
      金额: US$4000.0
      可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

    BNPL_119: 添加商品到购物车(总价US$4000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Afterpay最大值上限)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$4000.01
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_120: 添加商品到购物车(总价US$30000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Affirm最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
      金额: US$30000.0
      可用BNPL: ['Klarna', 'Affirm']

    BNPL_121: 添加商品到购物车(总价US$30000.01)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(仅Klarna支持)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$30000.01
      可用BNPL: ['Klarna']

    BNPL_122: 添加商品到购物车(总价US$999999.99)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(Klarna最大值)
      预期: 系统正确显示可用BNPL选项: ['Klarna']
      金额: US$999999.99
      可用BNPL: ['Klarna']

    BNPL_123: 添加商品到购物车(总价US$1000000.0)
      步骤: 选择BNPL支付方式
      子步骤: 验证可用BNPL选项(超出所有BNPL最大值)
      预期: 系统正确显示可用BNPL选项: 无可用选项
      金额: US$1000000.0
      可用BNPL: []


优惠叠加BNPL支付测试
============

Web平台:
  游客:
    BNPL_200: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_206: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_209: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_201: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_203: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_205: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_207: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_210: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_212: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠

  小B会员:
    BNPL_202: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_204: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_208: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_211: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_213: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠


H5平台:
  游客:
    BNPL_214: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_220: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_223: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

  会员:
    BNPL_215: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_217: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_219: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_221: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_224: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_226: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠

  小B会员:
    BNPL_216: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_218: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_222: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_225: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_227: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠


Android平台:
  会员:
    BNPL_228: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_230: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_232: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_233: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_235: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_237: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠

  小B会员:
    BNPL_229: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_231: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_234: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_236: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_238: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠


iOS平台:
  会员:
    BNPL_239: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_241: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_243: 应用会员折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 会员折扣

    BNPL_244: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_246: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_248: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠

  小B会员:
    BNPL_240: 应用优惠券并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 优惠券

    BNPL_242: 应用O币抵扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: O币抵扣

    BNPL_245: 应用限时折扣并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 限时折扣

    BNPL_247: 应用自由捆绑并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 自由捆绑

    BNPL_249: 应用多重优惠并选择BNPL支付
      步骤: 计算优惠后最终价格
      子步骤: 基于最终价格选择BNPL分期方案
      预期: 优惠正确应用，BNPL基于折后价格计算，支付流程正常
      优惠类型: 多重优惠


订单状态和转换测试
=========

Web平台:
  会员:
    BNPL_300: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_301: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_302: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

  小B会员:
    BNPL_303: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_304: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_305: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确


H5平台:
  会员:
    BNPL_306: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_307: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_308: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

  小B会员:
    BNPL_309: 从待支付订单重新选择BNPL支付
      步骤: 选择BNPL分期方案
      子步骤: 完成BNPL支付并同步订单状态
      预期: 待支付订单成功转换为BNPL支付，订单状态正确更新

    BNPL_310: BNPL第三方支付完成后状态同步
      步骤: 接收第三方支付成功通知
      子步骤: 同步更新订单状态为已支付
      预期: 订单状态正确同步为已支付，用户可查看支付成功订单

    BNPL_311: 待支付订单在不同支付方式间转换
      步骤: 从BNPL转换为其他支付方式或反之
      子步骤: 保持订单信息完整性
      预期: 支付方式转换成功，订单信息保持完整，价格计算正确

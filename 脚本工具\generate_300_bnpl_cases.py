#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL测试用例大规模生成器
按照参考格式生成300条左右的详细测试用例
"""

import json
from datetime import datetime

class BNPLMassiveTestGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # BNPL服务商
        self.providers = ["Affirm", "Klarna", "Afterpay"]
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接结算", 
            "商品页结算",
            "收藏夹结算",
            "重新下单结算"
        ]
        
        # 商品类型
        self.product_types = [
            "添加任意商品一件",
            "添加任意商品多件",
            "添加捆绑商品",
            "添加预售商品",
            "添加限量商品",
            "添加促销商品"
        ]
        
        # 分期方案
        self.installment_plans = {
            "Affirm": ["3个月分期", "6个月分期", "12个月分期", "24个月分期"],
            "Klarna": ["Pay in 4分期", "Pay in 30分期", "6个月分期", "12个月分期"],
            "Afterpay": ["4期分期", "双周分期"]
        }
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价",
            "限时折扣",
            "满减活动",
            "买赠活动"
        ]
        
        # 退款时机
        self.refund_timings = [
            "订单确认后立即退款",
            "支付完成后退款",
            "发货前退款",
            "发货中退款", 
            "发货后退款",
            "收货前退款",
            "收货后退款",
            "使用后退款"
        ]
        
        # 退款类型
        self.refund_types = [
            "全额退款",
            "部分退款",
            "多次部分退款",
            "换货退款"
        ]
        
        # 异常场景
        self.exception_scenarios = [
            "网络超时",
            "服务商维护",
            "审批拒绝",
            "重复提交",
            "浏览器关闭",
            "应用崩溃",
            "支付中断",
            "余额不足",
            "信用额度不足",
            "系统维护"
        ]

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_purchase_cases(self):
        """生成购买流程测试用例"""
        cases = []
        
        for provider in self.providers:
            for platform in self.platform_users.keys():
                for user_type in self.platform_users[platform]:
                    for scenario in self.purchase_scenarios:
                        for product_type in self.product_types[:2]:  # 限制商品类型
                            for plan in self.installment_plans[provider][:2]:  # 限制分期方案
                                case = {
                                    "case_id": self.get_case_id(),
                                    "case_name": "支付",
                                    "structure": {
                                        "platform": platform,
                                        "user_type": user_type,
                                        "scenario": scenario,
                                        "operation": f"{product_type}进入{scenario}",
                                        "action": f"点击结算并使用{provider}先享后付功能",
                                        "sub_action": f"选择{plan}方案",
                                        "expected_result": f"扣款金额与分期计划一致，{provider}支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知，{plan}信息准确显示"
                                    },
                                    "provider": provider,
                                    "priority": "高"
                                }
                                cases.append(case)
        
        return cases

    def generate_discount_combination_cases(self):
        """生成优惠叠加测试用例"""
        cases = []
        
        for provider in self.providers:
            for platform in self.platform_users.keys():
                for user_type in self.platform_users[platform]:
                    if user_type == "游客":
                        continue  # 游客不参与大部分优惠
                    
                    for discount in self.discount_types[:3]:  # 限制优惠类型
                        if discount == "优惠码" and user_type == "小B会员":
                            continue  # 小B会员不能使用优惠码
                        
                        for plan in self.installment_plans[provider][:2]:  # 限制分期方案数量
                            case = {
                                "case_id": self.get_case_id(),
                                "case_name": "优惠叠加支付",
                                "structure": {
                                    "platform": platform,
                                    "user_type": user_type,
                                    "scenario": "优惠商品结算",
                                    "operation": f"选择参与{discount}活动的商品",
                                    "action": f"应用{discount}后使用{provider}支付",
                                    "sub_action": f"选择{plan}方案",
                                    "expected_result": f"{discount}优惠正确应用，BNPL分期基于优惠后价格计算，{provider}支付流程正常，订单总金额计算正确，优惠明细清晰显示，{plan}费用基于折后价计算"
                                },
                                "provider": provider,
                                "discount_type": discount,
                                "priority": "中"
                            }
                            cases.append(case)
        
        return cases

    def generate_refund_cases(self):
        """生成退款场景测试用例"""
        cases = []
        
        for provider in self.providers:
            for timing in self.refund_timings[:4]:  # 限制退款时机
                for refund_type in self.refund_types[:2]:  # 限制退款类型
                    for platform in ["Web", "H5"]:  # 退款主要在Web和H5端操作
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "退款处理",
                            "structure": {
                                "platform": platform,
                                "user_type": "会员",
                                "scenario": f"{timing}场景",
                                "operation": f"对{provider}支付订单发起{refund_type}",
                                "action": "提交退款申请并处理",
                                "sub_action": f"调用{provider}退款接口",
                                "expected_result": f"退款申请成功提交，{provider}接口调用成功，订单状态正确更新，分期计划相应调整，用户收到退款通知，退款金额正确计算，{timing}处理流程正确"
                            },
                            "provider": provider,
                            "refund_timing": timing,
                            "refund_type": refund_type,
                            "priority": "高"
                        }
                        cases.append(case)
        
        return cases

    def generate_exception_cases(self):
        """生成异常场景测试用例"""
        cases = []
        
        for provider in self.providers:
            for platform in self.platform_users.keys():
                for exception in self.exception_scenarios[:5]:  # 限制异常场景
                    for user_type in self.platform_users[platform][:2]:  # 限制用户类型
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "异常处理",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{exception}异常场景",
                                "operation": f"正常进入{provider}支付流程",
                                "action": f"在关键步骤触发{exception}异常",
                                "sub_action": "观察系统响应和处理",
                                "expected_result": f"系统正确识别{exception}异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常日志正确记录"
                            },
                            "provider": provider,
                            "exception_type": exception,
                            "priority": "中"
                        }
                        cases.append(case)
        
        return cases

    def generate_cross_platform_cases(self):
        """生成跨平台场景测试用例"""
        cases = []
        
        cross_scenarios = [
            ("Web下单", "H5支付"),
            ("H5下单", "Web支付"),
            ("App下单", "Web支付"),
            ("Web下单", "App查看")
        ]
        
        for provider in self.providers:
            for order_platform, pay_platform in cross_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "跨平台支付",
                    "structure": {
                        "platform": f"{order_platform}+{pay_platform}",
                        "user_type": "会员",
                        "scenario": "跨平台操作",
                        "operation": f"在{order_platform}创建订单",
                        "action": f"切换到{pay_platform}使用{provider}支付",
                        "sub_action": "完成跨平台支付流程",
                        "expected_result": f"跨平台订单信息同步正确，{provider}支付流程正常，订单状态在各平台一致显示，用户体验流畅，数据同步及时"
                    },
                    "provider": provider,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_boundary_cases(self):
        """生成边界值测试用例"""
        cases = []
        
        boundary_scenarios = [
            ("最小金额", "0.01美元"),
            ("最大金额", "10000美元"),
            ("分期边界", "最短分期"),
            ("分期边界", "最长分期"),
            ("商品数量边界", "1件商品"),
            ("商品数量边界", "99件商品")
        ]
        
        for provider in self.providers:
            for scenario_name, scenario_value in boundary_scenarios:
                for platform in ["Web", "H5"]:
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "边界值测试",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{scenario_name}测试",
                            "operation": f"使用{scenario_value}进行{provider}支付",
                            "action": "验证边界值处理",
                            "sub_action": "确认系统边界值响应",
                            "expected_result": f"系统正确处理{scenario_value}边界值，{provider}支付流程正常，边界值验证通过，错误提示准确，数据计算正确"
                        },
                        "provider": provider,
                        "boundary_type": scenario_name,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v3.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL购买流程测试",
                "description": "验证不同平台、用户、场景下的BNPL支付完整流程",
                "test_cases": self.generate_purchase_cases()
            },
            {
                "module_name": "BNPL优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的叠加使用场景",
                "test_cases": self.generate_discount_combination_cases()
            },
            {
                "module_name": "BNPL退款场景测试",
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": self.generate_refund_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_exception_cases()
            },
            {
                "module_name": "BNPL跨平台测试",
                "description": "验证跨平台操作的数据同步和用户体验",
                "test_cases": self.generate_cross_platform_cases()
            },
            {
                "module_name": "BNPL边界值测试",
                "description": "验证各种边界条件下的系统处理",
                "test_cases": self.generate_boundary_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = BNPLMassiveTestGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_300_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL大规模测试用例已生成: bnpl_300_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例")

if __name__ == "__main__":
    main()

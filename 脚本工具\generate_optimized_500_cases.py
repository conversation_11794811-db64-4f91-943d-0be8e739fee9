#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL优化测试用例生成器
生成500+个优化的测试用例，合并支付方式，简化预期结果
"""

import json
from datetime import datetime

class OptimizedBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接结算", 
            "商品页结算",
            "收藏夹结算",
            "重新下单结算",
            "立即购买",
            "批量结算"
        ]
        
        # 商品类型
        self.product_types = [
            "添加任意商品一件",
            "添加任意商品多件",
            "添加捆绑商品",
            "添加预售商品",
            "添加限量商品",
            "添加促销商品",
            "添加新品商品",
            "添加热销商品"
        ]
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价",
            "限时折扣",
            "满减活动",
            "买赠活动",
            "新用户优惠",
            "生日优惠"
        ]
        
        # 退款时机
        self.refund_timings = [
            "订单确认后立即退款",
            "支付完成后退款",
            "发货前退款",
            "发货中退款", 
            "发货后退款",
            "收货前退款",
            "收货后退款",
            "使用后退款",
            "过期后退款"
        ]
        
        # 异常场景（排除审批拒绝、重复提交）
        self.exception_scenarios = [
            "网络超时",
            "服务商维护",
            "支付中断",
            "浏览器关闭",
            "应用崩溃",
            "系统维护",
            "API调用失败",
            "数据同步异常"
        ]
        
        # 简化的预期结果模板
        self.result_templates = {
            "normal_payment": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知",
            "discount_payment": "优惠正确应用，BNPL基于折后价计算，金额准确无误",
            "refund_process": "退款处理成功，分期计划相应调整，状态同步正确",
            "exception_handle": "系统正确处理异常，显示友好提示，订单状态保持一致",
            "cross_platform": "跨平台数据同步正确，用户体验流畅一致",
            "boundary_test": "边界值处理正确，系统响应正常，验证通过"
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_positive_purchase_cases(self):
        """生成正向购买流程测试用例"""
        cases = []
        
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                for scenario in self.purchase_scenarios:
                    for product_type in self.product_types:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": scenario,
                                "operation": f"{product_type}进入{scenario}",
                                "action": "点击结算并使用BNPL先享后付功能",
                                "sub_action": "选择分期方案并确认支付",
                                "expected_result": self.result_templates["normal_payment"]
                            },
                            "priority": "高"
                        }
                        cases.append(case)
        
        return cases

    def generate_negative_purchase_cases(self):
        """生成反向购买流程测试用例"""
        cases = []
        
        negative_scenarios = [
            ("商品缺货", "选择缺货商品进行BNPL支付"),
            ("价格变动", "支付过程中商品价格发生变化"),
            ("库存不足", "购买数量超过库存进行BNPL支付"),
            ("活动过期", "使用过期活动商品进行BNPL支付"),
            ("支付取消", "BNPL支付过程中主动取消"),
            ("会话超时", "支付页面停留过久导致会话超时")
        ]
        
        for platform in ["Web", "H5", "Android"]:  # 主要平台
            for scenario_name, scenario_desc in negative_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "支付异常",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统处理和用户提示",
                        "sub_action": "验证订单状态和错误处理",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_discount_positive_cases(self):
        """生成正向优惠叠加测试用例"""
        cases = []
        
        for platform in ["Web", "H5"]:  # 主要平台
            for user_type in ["会员", "小B会员"]:
                for discount in self.discount_types:
                    if discount == "优惠码" and user_type == "小B会员":
                        continue
                    
                    for scenario in ["购物车结算", "直接结算"]:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "优惠叠加支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{discount}优惠结算",
                                "operation": f"选择参与{discount}活动的商品进入{scenario}",
                                "action": f"应用{discount}后使用BNPL先享后付功能",
                                "sub_action": "确认优惠后价格并完成分期支付",
                                "expected_result": self.result_templates["discount_payment"]
                            },
                            "discount_type": discount,
                            "priority": "中"
                        }
                        cases.append(case)
        
        return cases

    def generate_discount_negative_cases(self):
        """生成反向优惠叠加测试用例"""
        cases = []
        
        negative_discount_scenarios = [
            ("优惠券过期", "使用过期优惠券进行BNPL支付"),
            ("优惠码无效", "使用无效优惠码进行BNPL支付"),
            ("O币余额不足", "O币余额不足时进行BNPL支付"),
            ("优惠条件不满足", "不满足优惠条件时强制使用优惠"),
            ("优惠叠加冲突", "使用冲突的优惠组合进行BNPL支付")
        ]
        
        for platform in ["Web", "H5"]:
            for scenario_name, scenario_desc in negative_discount_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "优惠异常处理",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统优惠验证和错误处理",
                        "sub_action": "验证BNPL支付流程的异常处理",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_refund_positive_cases(self):
        """生成正向退款场景测试用例"""
        cases = []
        
        refund_types = ["全额退款", "部分退款", "多次部分退款"]
        
        for timing in self.refund_timings:
            for refund_type in refund_types:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "退款处理",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": "Web",
                        "user_type": "会员",
                        "scenario": f"{timing}场景",
                        "operation": f"对BNPL支付订单发起{refund_type}",
                        "action": "提交退款申请并处理",
                        "sub_action": "调用BNPL退款接口并更新状态",
                        "expected_result": self.result_templates["refund_process"]
                    },
                    "refund_timing": timing,
                    "refund_type": refund_type,
                    "priority": "高"
                }
                cases.append(case)
        
        return cases

    def generate_refund_negative_cases(self):
        """生成反向退款场景测试用例"""
        cases = []
        
        negative_refund_scenarios = [
            ("退款金额超限", "退款金额超过订单金额"),
            ("重复退款申请", "对同一订单重复申请退款"),
            ("退款时效过期", "超过退款时效期申请退款"),
            ("退款接口异常", "BNPL退款接口调用失败"),
            ("分期已完成", "分期付款已完成后申请退款")
        ]
        
        for scenario_name, scenario_desc in negative_refund_scenarios:
            case = {
                "case_id": self.get_case_id(),
                "case_name": "退款异常处理",
                "case_type": "反向流程",
                "structure": {
                    "platform": "Web",
                    "user_type": "会员",
                    "scenario": f"{scenario_name}场景",
                    "operation": scenario_desc,
                    "action": "观察系统退款验证和错误处理",
                    "sub_action": "验证退款异常的处理机制",
                    "expected_result": self.result_templates["exception_handle"]
                },
                "priority": "中"
            }
            cases.append(case)
        
        return cases

    def generate_exception_cases(self):
        """生成异常场景测试用例"""
        cases = []
        
        for platform in self.platform_users.keys():
            for exception in self.exception_scenarios:
                for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "异常处理",
                        "case_type": "反向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{exception}异常场景",
                            "operation": "正常进入BNPL支付流程",
                            "action": f"在关键步骤触发{exception}异常",
                            "sub_action": "观察系统异常处理和恢复机制",
                            "expected_result": self.result_templates["exception_handle"]
                        },
                        "exception_type": exception,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_cross_platform_cases(self):
        """生成跨平台场景测试用例"""
        cases = []
        
        cross_scenarios = [
            ("Web下单", "H5支付", "Web查看"),
            ("H5下单", "Web支付", "App查看"),
            ("App下单", "Web支付", "H5查看"),
            ("Web下单", "App查看", "Web退款"),
            ("H5下单", "Web退款", "App查看")
        ]
        
        for scenario in cross_scenarios:
            case = {
                "case_id": self.get_case_id(),
                "case_name": "跨平台操作",
                "case_type": "正向流程",
                "structure": {
                    "platform": "+".join(scenario),
                    "user_type": "会员",
                    "scenario": "跨平台操作",
                    "operation": f"在{scenario[0]}→{scenario[1]}→{scenario[2]}",
                    "action": "完成跨平台BNPL支付和查看流程",
                    "sub_action": "验证各平台数据同步和状态一致性",
                    "expected_result": self.result_templates["cross_platform"]
                },
                "priority": "中"
            }
            cases.append(case)
        
        return cases

    def generate_boundary_cases(self):
        """生成边界值测试用例"""
        cases = []
        
        boundary_scenarios = [
            ("金额边界", "使用极小金额进行BNPL支付"),
            ("金额边界", "使用极大金额进行BNPL支付"),
            ("时间边界", "在活动开始时间点进行BNPL支付"),
            ("时间边界", "在活动结束时间点进行BNPL支付"),
            ("并发边界", "多用户同时进行BNPL支付"),
            ("数据边界", "使用特殊字符商品名进行BNPL支付")
        ]
        
        for scenario_name, scenario_desc in boundary_scenarios:
            for platform in ["Web", "H5"]:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "边界值测试",
                    "case_type": "正向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}测试",
                        "operation": scenario_desc,
                        "action": "验证边界条件下的BNPL支付",
                        "sub_action": "确认系统边界值处理和响应",
                        "expected_result": self.result_templates["boundary_test"]
                    },
                    "boundary_type": scenario_name,
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v4.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "optimization_notes": [
                "合并三个BNPL服务商为统一功能",
                "简化预期结果，减少重复表述",
                "扩展正向与反向测试场景",
                "排除审批拒绝、重复提交等服务商处理场景",
                "暂不包含商品边界值和金额限制（待产品补充）"
            ],
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL正向购买流程测试",
                "description": "验证各种正常场景下的BNPL支付完整流程",
                "test_cases": self.generate_positive_purchase_cases()
            },
            {
                "module_name": "BNPL反向购买流程测试",
                "description": "验证异常场景下的BNPL支付处理能力",
                "test_cases": self.generate_negative_purchase_cases()
            },
            {
                "module_name": "BNPL正向优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的正常叠加使用",
                "test_cases": self.generate_discount_positive_cases()
            },
            {
                "module_name": "BNPL反向优惠叠加测试",
                "description": "验证优惠异常情况下的BNPL处理机制",
                "test_cases": self.generate_discount_negative_cases()
            },
            {
                "module_name": "BNPL正向退款场景测试",
                "description": "验证各种正常退款场景的处理流程",
                "test_cases": self.generate_refund_positive_cases()
            },
            {
                "module_name": "BNPL反向退款场景测试",
                "description": "验证退款异常情况的处理机制",
                "test_cases": self.generate_refund_negative_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "验证各种系统异常情况下的处理能力",
                "test_cases": self.generate_exception_cases()
            },
            {
                "module_name": "BNPL跨平台测试",
                "description": "验证跨平台操作的数据同步和用户体验",
                "test_cases": self.generate_cross_platform_cases()
            },
            {
                "module_name": "BNPL边界值测试",
                "description": "验证各种边界条件下的系统处理",
                "test_cases": self.generate_boundary_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = OptimizedBNPLGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_optimized_500_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL优化测试用例已生成: bnpl_optimized_500_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        case_type_count = {}
        for case in module["test_cases"]:
            case_type = case.get("case_type", "未分类")
            case_type_count[case_type] = case_type_count.get(case_type, 0) + 1
        
        type_info = ", ".join([f"{k}:{v}个" for k, v in case_type_count.items()])
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例 ({type_info})")

if __name__ == "__main__":
    main()

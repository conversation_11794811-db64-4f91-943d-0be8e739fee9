BNPL先享后付分期测试用例

├── 版本信息 V10.0
    ├── 创建日期: 2025-07-16
    ├── 测试用例总数: 246个
    ├── 测试范围: Affirm, Afterpay, Klarna
    ├── 测试类型: 分期区间、下单流程、优惠叠加、退款处理
    └── 覆盖平台: Android、iOS、Web、H5

├── BNPL分期区间
    ├── Affirm: US$50.00 - US$30,000.00
    ├── Afterpay: US$1.00 - US$4,000.00
    └── Klarna: US$0.50 - US$999,999.99

├── BNPL分期下单流程测试
    ├── BNPL_001: BNPL分期下单流程
        ├── Android
            ├── 会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_004: BNPL分期下单流程
        ├── Android
            ├── 会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_007: BNPL分期下单流程
        ├── Android
            ├── 会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_010: BNPL分期下单流程
        ├── Android
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_013: BNPL分期下单流程
        ├── Android
            ├── 小B会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_016: BNPL分期下单流程
        ├── Android
            ├── 小B会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_019: BNPL分期下单流程
        ├── iOS
            ├── 会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_022: BNPL分期下单流程
        ├── iOS
            ├── 会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_025: BNPL分期下单流程
        ├── iOS
            ├── 会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_028: BNPL分期下单流程
        ├── iOS
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_031: BNPL分期下单流程
        ├── iOS
            ├── 小B会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_034: BNPL分期下单流程
        ├── iOS
            ├── 小B会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_037: BNPL分期下单流程
        ├── Web
            ├── 游客
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_040: BNPL分期下单流程
        ├── Web
            ├── 游客
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_043: BNPL分期下单流程
        ├── Web
            ├── 游客
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_046: BNPL分期下单流程
        ├── Web
            ├── 会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_049: BNPL分期下单流程
        ├── Web
            ├── 会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_052: BNPL分期下单流程
        ├── Web
            ├── 会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_055: BNPL分期下单流程
        ├── Web
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_058: BNPL分期下单流程
        ├── Web
            ├── 小B会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_061: BNPL分期下单流程
        ├── Web
            ├── 小B会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_064: BNPL分期下单流程
        ├── H5
            ├── 游客
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_067: BNPL分期下单流程
        ├── H5
            ├── 游客
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_070: BNPL分期下单流程
        ├── H5
            ├── 游客
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_073: BNPL分期下单流程
        ├── H5
            ├── 会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_076: BNPL分期下单流程
        ├── H5
            ├── 会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_079: BNPL分期下单流程
        ├── H5
            ├── 会员
                ├── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_082: BNPL分期下单流程
        ├── H5
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加单品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到购物车结算
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到购物车结算
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    ├── BNPL_085: BNPL分期下单流程
        ├── H5
            ├── 小B会员
                ├── 直接购买
                    ├── 添加单品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到直接购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到直接购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0
    └── BNPL_088: BNPL分期下单流程
        └── H5
            └── 小B会员
                └── 立即购买
                    ├── 添加单品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    ├── 添加多品商品到立即购买
                        ├── 选择BNPL先享后付并确认支付
                            ├── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

                    └── 添加自由捆绑商品商品到立即购买
                        └── 选择BNPL先享后付并确认支付
                            └── 选择分期方案(金额US$100.0)
                                └── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
                                    └── 测试数据: 金额: US$100.0

├── BNPL分期金额边界值测试
    ├── BNPL_200: BNPL分期金额边界值测试
        ├── Web
            ├── 游客
                ├── 购物车结算
                    ├── 添加商品到购物车(总价US$0.49)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(低于所有BNPL最小值)
                                └── 系统正确显示可用BNPL选项: 无可用选项
                                    └── 测试数据: 金额: US$0.49; 可用BNPL: []

                    ├── 添加商品到购物车(总价US$0.5)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Klarna最小值)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$0.5; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$0.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最小值下限)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$0.99; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$1.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最小值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
                                    └── 测试数据: 金额: US$1.0; 可用BNPL: ['Klarna', 'Afterpay']

                    ├── 添加商品到购物车(总价US$49.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Affirm最小值下限)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
                                    └── 测试数据: 金额: US$49.99; 可用BNPL: ['Klarna', 'Afterpay']

                    ├── 添加商品到购物车(总价US$50.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(全支持区间开始)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
                                    └── 测试数据: 金额: US$50.0; 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

                    ├── 添加商品到购物车(总价US$4000.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
                                    └── 测试数据: 金额: US$4000.0; 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

                    ├── 添加商品到购物车(总价US$4000.01)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最大值上限)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
                                    └── 测试数据: 金额: US$4000.01; 可用BNPL: ['Klarna', 'Affirm']

                    ├── 添加商品到购物车(总价US$30000.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Affirm最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
                                    └── 测试数据: 金额: US$30000.0; 可用BNPL: ['Klarna', 'Affirm']

                    ├── 添加商品到购物车(总价US$30000.01)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(仅Klarna支持)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$30000.01; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$999999.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Klarna最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$999999.99; 可用BNPL: ['Klarna']

                    └── 添加商品到购物车(总价US$1000000.0)
                        └── 选择BNPL先享后付支付方式
                            └── 验证可用BNPL选项(超出所有BNPL最大值)
                                └── 系统正确显示可用BNPL选项: 无可用选项
                                    └── 测试数据: 金额: US$1000000.0; 可用BNPL: []
    └── BNPL_212: BNPL分期金额边界值测试
        └── H5
            └── 游客
                └── 购物车结算
                    ├── 添加商品到购物车(总价US$0.49)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(低于所有BNPL最小值)
                                └── 系统正确显示可用BNPL选项: 无可用选项
                                    └── 测试数据: 金额: US$0.49; 可用BNPL: []

                    ├── 添加商品到购物车(总价US$0.5)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Klarna最小值)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$0.5; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$0.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最小值下限)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$0.99; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$1.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最小值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
                                    └── 测试数据: 金额: US$1.0; 可用BNPL: ['Klarna', 'Afterpay']

                    ├── 添加商品到购物车(总价US$49.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Affirm最小值下限)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay']
                                    └── 测试数据: 金额: US$49.99; 可用BNPL: ['Klarna', 'Afterpay']

                    ├── 添加商品到购物车(总价US$50.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(全支持区间开始)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
                                    └── 测试数据: 金额: US$50.0; 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

                    ├── 添加商品到购物车(总价US$4000.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Afterpay', 'Affirm']
                                    └── 测试数据: 金额: US$4000.0; 可用BNPL: ['Klarna', 'Afterpay', 'Affirm']

                    ├── 添加商品到购物车(总价US$4000.01)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Afterpay最大值上限)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
                                    └── 测试数据: 金额: US$4000.01; 可用BNPL: ['Klarna', 'Affirm']

                    ├── 添加商品到购物车(总价US$30000.0)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Affirm最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna', 'Affirm']
                                    └── 测试数据: 金额: US$30000.0; 可用BNPL: ['Klarna', 'Affirm']

                    ├── 添加商品到购物车(总价US$30000.01)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(仅Klarna支持)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$30000.01; 可用BNPL: ['Klarna']

                    ├── 添加商品到购物车(总价US$999999.99)
                        ├── 选择BNPL先享后付支付方式
                            ├── 验证可用BNPL选项(Klarna最大值)
                                └── 系统正确显示可用BNPL选项: ['Klarna']
                                    └── 测试数据: 金额: US$999999.99; 可用BNPL: ['Klarna']

                    └── 添加商品到购物车(总价US$1000000.0)
                        └── 选择BNPL先享后付支付方式
                            └── 验证可用BNPL选项(超出所有BNPL最大值)
                                └── 系统正确显示可用BNPL选项: 无可用选项
                                    └── 测试数据: 金额: US$1000000.0; 可用BNPL: []

├── BNPL分期优惠叠加测试
    ├── BNPL_300: BNPL分期优惠叠加测试
        ├── Web
            ├── 会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$110.0, 优惠US$10.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$1.5, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$0.5)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    ├── 添加商品并应用优惠码+限时折扣优惠
                        ├── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣

                    └── 添加商品并应用会员折扣+限时折扣优惠
                        └── 使用BNPL支付(原价US$250.0, 优惠US$100.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣+限时折扣
    ├── BNPL_301: BNPL分期优惠叠加测试
        ├── Web
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用小B会员折扣优惠
                        ├── 使用BNPL支付(原价US$130.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 小B会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣
    ├── BNPL_302: BNPL分期优惠叠加测试
        ├── Web
            ├── 游客
                ├── 购物车结算
                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣
    ├── BNPL_328: BNPL分期优惠叠加测试
        ├── H5
            ├── 会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$110.0, 优惠US$10.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$1.5, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$0.5)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    ├── 添加商品并应用优惠码+限时折扣优惠
                        ├── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣

                    └── 添加商品并应用会员折扣+限时折扣优惠
                        └── 使用BNPL支付(原价US$250.0, 优惠US$100.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣+限时折扣
    ├── BNPL_329: BNPL分期优惠叠加测试
        ├── H5
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用小B会员折扣优惠
                        ├── 使用BNPL支付(原价US$130.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 小B会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣
    ├── BNPL_330: BNPL分期优惠叠加测试
        ├── H5
            ├── 游客
                ├── 购物车结算
                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣
    ├── BNPL_356: BNPL分期优惠叠加测试
        ├── Android
            ├── 会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$110.0, 优惠US$10.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$1.5, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$0.5)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    ├── 添加商品并应用优惠码+限时折扣优惠
                        ├── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣

                    └── 添加商品并应用会员折扣+限时折扣优惠
                        └── 使用BNPL支付(原价US$250.0, 优惠US$100.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣+限时折扣
    ├── BNPL_357: BNPL分期优惠叠加测试
        ├── Android
            ├── 小B会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用小B会员折扣优惠
                        ├── 使用BNPL支付(原价US$130.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 小B会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣
    ├── BNPL_378: BNPL分期优惠叠加测试
        ├── iOS
            ├── 会员
                ├── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$110.0, 优惠US$10.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用会员折扣优惠
                        ├── 使用BNPL支付(原价US$1.5, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$0.5)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    ├── 添加商品并应用优惠码+限时折扣优惠
                        ├── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣

                    └── 添加商品并应用会员折扣+限时折扣优惠
                        └── 使用BNPL支付(原价US$250.0, 优惠US$100.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 会员折扣+限时折扣
    └── BNPL_379: BNPL分期优惠叠加测试
        └── iOS
            └── 小B会员
                └── 购物车结算
                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$120.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$150.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用优惠码优惠
                        ├── 使用BNPL支付(原价US$140.0, 优惠US$20.0)
                            ├── 确认BNPL分期(最终价格US$120.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码

                    ├── 添加商品并应用限时折扣优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 限时折扣

                    ├── 添加商品并应用小B会员折扣优惠
                        ├── 使用BNPL支付(原价US$130.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$100.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 小B会员折扣

                    ├── 添加商品并应用自由捆绑优惠
                        ├── 使用BNPL支付(原价US$180.0, 优惠US$30.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 自由捆绑

                    ├── 添加商品并应用优惠券优惠
                        ├── 使用BNPL支付(原价US$51.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$50.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠券

                    ├── 添加商品并应用O币抵扣优惠
                        ├── 使用BNPL支付(原价US$4001.0, 优惠US$1.0)
                            ├── 确认BNPL分期(最终价格US$4000.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币抵扣

                    ├── 添加商品并应用O币+优惠券优惠
                        ├── 使用BNPL支付(原价US$200.0, 优惠US$50.0)
                            ├── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: O币+优惠券

                    └── 添加商品并应用优惠码+限时折扣优惠
                        └── 使用BNPL支付(原价US$220.0, 优惠US$70.0)
                            └── 确认BNPL分期(最终价格US$150.0)
                                └── 优惠正确应用，BNPL基于折后价计算，金额准确无误
                                    └── 测试数据: 优惠类型: 优惠码+限时折扣

└── BNPL分期退款处理测试
    ├── BNPL_400: BNPL分期退款处理
        ├── Web
            ├── 会员
                ├── 退款处理
                    ├── BNPL订单订单确认后申请退款
                        ├── 处理全额退款(订单金额US$100.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单订单确认后申请退款
                        ├── 处理部分退款(订单金额US$200.0)
                            ├── 退款金额US$50.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理全额退款(订单金额US$150.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理部分退款(订单金额US$300.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理全额退款(订单金额US$250.0)
                            ├── 退款金额US$250.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理部分退款(订单金额US$400.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单收货后申请退款
                        ├── 处理全额退款(订单金额US$180.0)
                            ├── 退款金额US$180.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    └── BNPL订单收货后申请退款
                        └── 处理部分退款(订单金额US$350.0)
                            └── 退款金额US$120.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款
    ├── BNPL_408: BNPL分期退款处理
        ├── Web
            ├── 小B会员
                ├── 退款处理
                    ├── BNPL订单订单确认后申请退款
                        ├── 处理全额退款(订单金额US$100.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单订单确认后申请退款
                        ├── 处理部分退款(订单金额US$200.0)
                            ├── 退款金额US$50.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理全额退款(订单金额US$150.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理部分退款(订单金额US$300.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理全额退款(订单金额US$250.0)
                            ├── 退款金额US$250.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理部分退款(订单金额US$400.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单收货后申请退款
                        ├── 处理全额退款(订单金额US$180.0)
                            ├── 退款金额US$180.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    └── BNPL订单收货后申请退款
                        └── 处理部分退款(订单金额US$350.0)
                            └── 退款金额US$120.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款
    ├── BNPL_416: BNPL分期退款处理
        ├── H5
            ├── 会员
                ├── 退款处理
                    ├── BNPL订单订单确认后申请退款
                        ├── 处理全额退款(订单金额US$100.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单订单确认后申请退款
                        ├── 处理部分退款(订单金额US$200.0)
                            ├── 退款金额US$50.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理全额退款(订单金额US$150.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理部分退款(订单金额US$300.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理全额退款(订单金额US$250.0)
                            ├── 退款金额US$250.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理部分退款(订单金额US$400.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单收货后申请退款
                        ├── 处理全额退款(订单金额US$180.0)
                            ├── 退款金额US$180.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    └── BNPL订单收货后申请退款
                        └── 处理部分退款(订单金额US$350.0)
                            └── 退款金额US$120.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款
    └── BNPL_424: BNPL分期退款处理
        └── H5
            └── 小B会员
                └── 退款处理
                    ├── BNPL订单订单确认后申请退款
                        ├── 处理全额退款(订单金额US$100.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单订单确认后申请退款
                        ├── 处理部分退款(订单金额US$200.0)
                            ├── 退款金额US$50.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理全额退款(订单金额US$150.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货前申请退款
                        ├── 处理部分退款(订单金额US$300.0)
                            ├── 退款金额US$100.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理全额退款(订单金额US$250.0)
                            ├── 退款金额US$250.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    ├── BNPL订单发货后申请退款
                        ├── 处理部分退款(订单金额US$400.0)
                            ├── 退款金额US$150.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款

                    ├── BNPL订单收货后申请退款
                        ├── 处理全额退款(订单金额US$180.0)
                            ├── 退款金额US$180.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 全额退款

                    └── BNPL订单收货后申请退款
                        └── 处理部分退款(订单金额US$350.0)
                            └── 退款金额US$120.0并调整分期计划
                                └── 退款处理成功，分期计划相应调整，状态同步正确
                                    └── 测试数据: 退款类型: 部分退款
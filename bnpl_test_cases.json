{"title": "BNPL先享后付功能测试用例", "version": "v1.0", "created_date": "2025-07-01", "test_modules": [{"module_name": "BNPL购买流程测试", "description": "验证不同用户在不同平台使用BNPL支付的完整流程", "test_cases": [{"case_id": "BNPL_PURCHASE_001", "case_name": "支付-Android-Affirm支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Affirm服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Android", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_002", "case_name": "支付-Android-Affirm支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Affirm服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Android", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_003", "case_name": "支付-iOS-Affirm支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Affirm服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "iOS", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_004", "case_name": "支付-iOS-Affirm支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Affirm服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "iOS", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_005", "case_name": "支付-Web-Affirm支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Affirm服务可用", "游客身份，无需登录", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "Web", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_006", "case_name": "支付-Web-Affirm支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Affirm服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Web", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_007", "case_name": "支付-Web-Affirm支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Affirm服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Web", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_008", "case_name": "支付-H5-Affirm支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Affirm服务可用", "游客身份，无需登录", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "H5", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_009", "case_name": "支付-H5-Affirm支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Affirm服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "H5", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_010", "case_name": "支付-H5-Affirm支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Affirm服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Affirm分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Affirm支付方式", "2. 选择分期方案（如3个月）", "3. 确认分期信息和费用明细", "4. 提交Affirm支付申请", "5. 完成Affirm审批流程", "6. 确认支付成功"], "expected_results": ["Affirm支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "H5", "provider": "Affirm", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_011", "case_name": "支付-Android-Klarna支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Klarna服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Android", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_012", "case_name": "支付-Android-Klarna支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Klarna服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Android", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_013", "case_name": "支付-iOS-Klarna支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Klarna服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "iOS", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_014", "case_name": "支付-iOS-Klarna支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Klarna服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "iOS", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_015", "case_name": "支付-Web-Klarna支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Klarna服务可用", "游客身份，无需登录", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "Web", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_016", "case_name": "支付-Web-Klarna支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Klarna服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Web", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_017", "case_name": "支付-Web-Klarna支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Klarna服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Web", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_018", "case_name": "支付-H5-<PERSON><PERSON><PERSON>支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Klarna服务可用", "游客身份，无需登录", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "H5", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_019", "case_name": "支付-H5-<PERSON><PERSON><PERSON>支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Klarna服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "H5", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_020", "case_name": "支付-H5-<PERSON><PERSON><PERSON>支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Klarna服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "<PERSON>larna分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Klarna支付方式", "2. 选择分期方案（如Pay in 4）", "3. 确认分期信息和费用明细", "4. 提交Klarna支付申请", "5. 完成Klarna审批流程", "6. 确认支付成功"], "expected_results": ["<PERSON><PERSON><PERSON>支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "H5", "provider": "<PERSON><PERSON><PERSON>", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_021", "case_name": "支付-Android-Afterpay支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Afterpay服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Android", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_022", "case_name": "支付-Android-Afterpay支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Android端环境正常，Afterpay服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入Android端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Android", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_023", "case_name": "支付-iOS-Afterpay支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Afterpay服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "iOS", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_024", "case_name": "支付-iOS-Afterpay支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["iOS端环境正常，Afterpay服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入iOS端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "iOS", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_025", "case_name": "支付-Web-Afterpay支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Afterpay服务可用", "游客身份，无需登录", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "Web", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_026", "case_name": "支付-Web-Afterpay支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Afterpay服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "Web", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_027", "case_name": "支付-Web-Afterpay支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["Web端环境正常，Afterpay服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入Web端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "Web", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_028", "case_name": "支付-H5-Afterpay支付-游客正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Afterpay服务可用", "游客身份，无需登录", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "游客", "platform": "H5", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_029", "case_name": "支付-H5-Afterpay支付-普通会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Afterpay服务可用", "普通会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "普通会员", "platform": "H5", "provider": "Afterpay", "product_price": "100-500美元"}}, {"case_id": "BNPL_PURCHASE_030", "case_name": "支付-H5-Afterpay支付-小B会员正常支付，展示正常分期，可以完成分期操作，扣款与分期缴纳一致", "priority": "高", "test_type": "功能测试", "preconditions": ["H5端环境正常，Afterpay服务可用", "小B会员账号已登录，信用状态良好", "购物车中有可支付商品", "Afterpay分期服务已配置"], "test_steps": ["1. 进入H5端结账页面，选择Afterpay支付方式", "2. 选择分期方案（如4期分期）", "3. 确认分期信息和费用明细", "4. 提交Afterpay支付申请", "5. 完成Afterpay审批流程", "6. 确认支付成功"], "expected_results": ["Afterpay支付选项正常显示", "分期方案清晰展示，费用透明", "审批流程顺畅，无异常中断", "支付成功后订单状态更新为已支付", "用户收到支付确认和分期计划通知", "扣款金额与分期计划一致"], "test_data": {"user_type": "小B会员", "platform": "H5", "provider": "Afterpay", "product_price": "100-500美元"}}]}, {"module_name": "BNPL退款场景测试", "description": "验证不同时机和类型的BNPL订单退款处理", "test_cases": [{"case_id": "BNPL_REFUND_001", "case_name": "退款-Affirm-订单确认后立即退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为订单确认", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "订单确认", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_002", "case_name": "退款-Affirm-订单确认后立即退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为订单确认", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "订单确认", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_003", "case_name": "退款-Affirm-发货前退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为待发货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "待发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_004", "case_name": "退款-Affirm-发货前退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为待发货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "待发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_005", "case_name": "退款-Affirm-发货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为已发货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "已发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_006", "case_name": "退款-Affirm-发货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为已发货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "已发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_007", "case_name": "退款-Affirm-收货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为已收货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "已收货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_008", "case_name": "退款-Affirm-收货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Affirm支付的订单", "订单状态为已收货", "退款权限已配置", "Affirm退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Affirm退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Affirm接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Affirm", "order_status": "已收货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_009", "case_name": "退款-Klarna-订单确认后立即退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为订单确认", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "订单确认", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_010", "case_name": "退款-Klarna-订单确认后立即退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为订单确认", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "订单确认", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_011", "case_name": "退款-<PERSON>larna-发货前退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为待发货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "待发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_012", "case_name": "退款-Klarna-发货前退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为待发货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "待发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_013", "case_name": "退款-<PERSON>larna-发货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为已发货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "已发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_014", "case_name": "退款-Klarna-发货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为已发货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "已发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_015", "case_name": "退款-<PERSON>larna-收货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为已收货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "已收货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_016", "case_name": "退款-Klarna-收货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Klarna支付的订单", "订单状态为已收货", "退款权限已配置", "<PERSON><PERSON><PERSON>退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Klarna退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Klarna接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "order_status": "已收货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_017", "case_name": "退款-Afterpay-订单确认后立即退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为订单确认", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "订单确认", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_018", "case_name": "退款-Afterpay-订单确认后立即退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为订单确认", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "订单确认", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_019", "case_name": "退款-Afterpay-发货前退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为待发货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "待发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_020", "case_name": "退款-Afterpay-发货前退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为待发货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "待发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_021", "case_name": "退款-Afterpay-发货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为已发货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "已发货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_022", "case_name": "退款-Afterpay-发货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为已发货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "已发货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_023", "case_name": "退款-Afterpay-收货后退款-全额退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为已收货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起全额退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为全额退款状态", "分期计划完全取消", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "已收货", "refund_type": "全额退款", "refund_amount": "50-100%订单金额"}}, {"case_id": "BNPL_REFUND_024", "case_name": "退款-Afterpay-收货后退款-部分退款，分期计划正确调整", "priority": "高", "test_type": "功能测试", "preconditions": ["存在Afterpay支付的订单", "订单状态为已收货", "退款权限已配置", "Afterpay退款接口正常"], "test_steps": ["1. 在订单管理系统中找到目标订单", "2. 发起部分退款申请", "3. 填写退款原因和金额", "4. 提交退款申请", "5. 系统调用Afterpay退款接口", "6. 验证分期计划调整"], "expected_results": ["退款申请成功提交", "Afterpay接口调用成功", "订单状态更新为部分退款状态", "分期计划按比例调整", "用户收到退款通知", "退款金额正确计算"], "test_data": {"provider": "Afterpay", "order_status": "已收货", "refund_type": "部分退款", "refund_amount": "50-100%订单金额"}}]}, {"module_name": "BNPL优惠叠加测试", "description": "验证BNPL与各种优惠活动的叠加使用", "test_cases": [{"case_id": "BNPL_DISCOUNT_001", "case_name": "优惠叠加-Affirm-普通会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠券活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠券", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_002", "case_name": "优惠叠加-Affirm-小B会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "优惠券活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "优惠券", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_003", "case_name": "优惠叠加-Affirm-普通会员使用优惠码+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠码活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠码优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠码优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠码", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_004", "case_name": "优惠叠加-Affirm-普通会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "O币抵扣活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "O币抵扣", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_005", "case_name": "优惠叠加-Affirm-小B会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "O币抵扣活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "O币抵扣", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_006", "case_name": "优惠叠加-Affirm-普通会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "会员价活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "会员价", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_007", "case_name": "优惠叠加-Affirm-小B会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "会员价活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "会员价", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_008", "case_name": "优惠叠加-Affirm-普通会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "限时折扣活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "限时折扣", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_009", "case_name": "优惠叠加-Affirm-小B会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "限时折扣活动正在进行", "Affirm服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Affirm支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "限时折扣", "provider": "Affirm", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_010", "case_name": "优惠叠加-Klarna-普通会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠券活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠券", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_011", "case_name": "优惠叠加-Klarna-小B会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "优惠券活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "优惠券", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_012", "case_name": "优惠叠加-Klarna-普通会员使用优惠码+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠码活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠码优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠码优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠码", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_013", "case_name": "优惠叠加-Klarna-普通会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "O币抵扣活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "O币抵扣", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_014", "case_name": "优惠叠加-Klarna-小B会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "O币抵扣活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "O币抵扣", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_015", "case_name": "优惠叠加-Klarna-普通会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "会员价活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "会员价", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_016", "case_name": "优惠叠加-Klarna-小B会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "会员价活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "会员价", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_017", "case_name": "优惠叠加-Klarna-普通会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "限时折扣活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "限时折扣", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_018", "case_name": "优惠叠加-Klarna-小B会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "限时折扣活动正在进行", "<PERSON><PERSON><PERSON>服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Klarna支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "限时折扣", "provider": "<PERSON><PERSON><PERSON>", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_019", "case_name": "优惠叠加-Afterpay-普通会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠券活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠券", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_020", "case_name": "优惠叠加-Afterpay-小B会员使用优惠券+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "优惠券活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠券优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠券优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "优惠券", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_021", "case_name": "优惠叠加-Afterpay-普通会员使用优惠码+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "优惠码活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用优惠码优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["优惠码优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "优惠码", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_022", "case_name": "优惠叠加-Afterpay-普通会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "O币抵扣活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "O币抵扣", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_023", "case_name": "优惠叠加-Afterpay-小B会员使用O币抵扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "O币抵扣活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用O币抵扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["O币抵扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "O币抵扣", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_024", "case_name": "优惠叠加-Afterpay-普通会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "会员价活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "会员价", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_025", "case_name": "优惠叠加-Afterpay-小B会员使用会员价+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "会员价活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用会员价优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["会员价优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "会员价", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_026", "case_name": "优惠叠加-Afterpay-普通会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["普通会员账号已登录", "限时折扣活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "普通会员", "discount_type": "限时折扣", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}, {"case_id": "BNPL_DISCOUNT_027", "case_name": "优惠叠加-Afterpay-小B会员使用限时折扣+BNPL支付，优惠后金额正确分期", "priority": "中", "test_type": "功能测试", "preconditions": ["小B会员账号已登录", "限时折扣活动正在进行", "Afterpay服务正常", "测试商品参与优惠活动"], "test_steps": ["1. 选择参与优惠的商品", "2. 应用限时折扣优惠", "3. 确认优惠后价格", "4. 进入支付页面", "5. 选择Afterpay支付", "6. 确认分期金额基于优惠后价格", "7. 完成BNPL支付流程", "8. 验证订单金额计算正确"], "expected_results": ["限时折扣优惠正确应用", "BNPL分期基于优惠后价格计算", "订单总金额计算正确", "优惠明细清晰显示", "支付流程正常完成"], "test_data": {"user_type": "小B会员", "discount_type": "限时折扣", "provider": "Afterpay", "original_price": "200美元", "discount_amount": "20-50美元"}}]}, {"module_name": "BNPL异常场景测试", "description": "验证各种异常情况下的系统处理能力", "test_cases": [{"case_id": "BNPL_EXCEPTION_001", "case_name": "异常处理-Affirm-网络超时异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟网络超时异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发网络超时", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Affirm", "exception_type": "网络超时", "scenario_description": "支付过程中网络连接超时"}}, {"case_id": "BNPL_EXCEPTION_002", "case_name": "异常处理-Affirm-服务商维护异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟服务商维护异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发服务商维护", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Affirm", "exception_type": "服务商维护", "scenario_description": "BNPL服务商系统维护"}}, {"case_id": "BNPL_EXCEPTION_003", "case_name": "异常处理-Affirm-审批拒绝异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟审批拒绝异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发审批拒绝", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Affirm", "exception_type": "审批拒绝", "scenario_description": "用户信用审批被拒绝"}}, {"case_id": "BNPL_EXCEPTION_004", "case_name": "异常处理-Affirm-重复提交异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟重复提交异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发重复提交", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Affirm", "exception_type": "重复提交", "scenario_description": "用户重复提交支付请求"}}, {"case_id": "BNPL_EXCEPTION_005", "case_name": "异常处理-Affirm-浏览器关闭异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟浏览器关闭异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发浏览器关闭", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Affirm", "exception_type": "浏览器关闭", "scenario_description": "支付过程中意外关闭浏览器"}}, {"case_id": "BNPL_EXCEPTION_006", "case_name": "异常处理-<PERSON>lar<PERSON>-网络超时异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟网络超时异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发网络超时", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "exception_type": "网络超时", "scenario_description": "支付过程中网络连接超时"}}, {"case_id": "BNPL_EXCEPTION_007", "case_name": "异常处理-<PERSON>lar<PERSON>-服务商维护异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟服务商维护异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发服务商维护", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "exception_type": "服务商维护", "scenario_description": "BNPL服务商系统维护"}}, {"case_id": "BNPL_EXCEPTION_008", "case_name": "异常处理-<PERSON>lar<PERSON>-审批拒绝异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟审批拒绝异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发审批拒绝", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "exception_type": "审批拒绝", "scenario_description": "用户信用审批被拒绝"}}, {"case_id": "BNPL_EXCEPTION_009", "case_name": "异常处理-<PERSON>lar<PERSON>-重复提交异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟重复提交异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发重复提交", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "exception_type": "重复提交", "scenario_description": "用户重复提交支付请求"}}, {"case_id": "BNPL_EXCEPTION_010", "case_name": "异常处理-<PERSON>lar<PERSON>-浏览器关闭异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟浏览器关闭异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发浏览器关闭", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "<PERSON><PERSON><PERSON>", "exception_type": "浏览器关闭", "scenario_description": "支付过程中意外关闭浏览器"}}, {"case_id": "BNPL_EXCEPTION_011", "case_name": "异常处理-Afterpay-网络超时异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟网络超时异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发网络超时", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Afterpay", "exception_type": "网络超时", "scenario_description": "支付过程中网络连接超时"}}, {"case_id": "BNPL_EXCEPTION_012", "case_name": "异常处理-Afterpay-服务商维护异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟服务商维护异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发服务商维护", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Afterpay", "exception_type": "服务商维护", "scenario_description": "BNPL服务商系统维护"}}, {"case_id": "BNPL_EXCEPTION_013", "case_name": "异常处理-Afterpay-审批拒绝异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟审批拒绝异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发审批拒绝", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Afterpay", "exception_type": "审批拒绝", "scenario_description": "用户信用审批被拒绝"}}, {"case_id": "BNPL_EXCEPTION_014", "case_name": "异常处理-Afterpay-重复提交异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟重复提交异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发重复提交", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Afterpay", "exception_type": "重复提交", "scenario_description": "用户重复提交支付请求"}}, {"case_id": "BNPL_EXCEPTION_015", "case_name": "异常处理-Afterpay-浏览器关闭异常，系统正确处理，订单状态一致", "priority": "中", "test_type": "异常测试", "preconditions": ["正常的BNPL支付环境", "模拟浏览器关闭异常条件", "测试订单准备就绪"], "test_steps": ["1. 正常进入BNPL支付流程", "2. 在关键步骤触发浏览器关闭", "3. 观察系统响应", "4. 检查订单状态", "5. 验证错误处理机制", "6. 确认用户体验"], "expected_results": ["系统能正确识别异常情况", "显示友好的错误提示信息", "订单状态不会出现异常", "用户可以重新尝试或选择其他支付方式", "不会产生重复扣款或订单"], "test_data": {"provider": "Afterpay", "exception_type": "浏览器关闭", "scenario_description": "支付过程中意外关闭浏览器"}}]}]}
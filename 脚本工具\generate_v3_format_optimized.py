#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按照V3.0格式生成优化的BNPL测试用例
保持完整8层级结构，应用V4.0优化内容
"""

import json
from datetime import datetime

class V3FormatOptimizedGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接结算", 
            "商品页结算",
            "收藏夹结算",
            "立即购买"
        ]
        
        # 商品类型
        self.product_types = [
            "添加任意商品一件",
            "添加任意商品多件",
            "添加捆绑商品",
            "添加预售商品",
            "添加限量商品",
            "添加促销商品"
        ]
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价",
            "限时折扣"
        ]
        
        # 退款时机
        self.refund_timings = [
            "订单确认后立即退款",
            "发货前退款",
            "发货后退款",
            "收货后退款"
        ]
        
        # 退款类型
        self.refund_types = ["全额退款", "部分退款"]
        
        # 异常场景（排除审批拒绝、重复提交）
        self.exception_scenarios = [
            "网络超时",
            "服务商维护",
            "支付中断",
            "浏览器关闭",
            "应用崩溃"
        ]
        
        # 简化的预期结果
        self.result_templates = {
            "normal_payment": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知",
            "discount_payment": "优惠正确应用，BNPL基于折后价计算，金额准确无误",
            "refund_process": "退款处理成功，分期计划相应调整，状态同步正确",
            "exception_handle": "系统正确处理异常，显示友好提示，订单状态保持一致",
            "cross_platform": "跨平台数据同步正确，用户体验流畅一致"
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_purchase_cases(self):
        """生成购买流程测试用例（V3.0格式）"""
        cases = []
        
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                for scenario in self.purchase_scenarios:
                    for product_type in self.product_types:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": scenario,
                                "operation": f"{product_type}进入{scenario}",
                                "action": "点击结算并使用BNPL先享后付功能",
                                "sub_action": "选择分期方案并确认支付",
                                "expected_result": self.result_templates["normal_payment"]
                            },
                            "priority": "高"
                        }
                        cases.append(case)
        
        return cases

    def generate_discount_cases(self):
        """生成优惠叠加测试用例（V3.0格式）"""
        cases = []
        
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                if user_type == "游客":
                    continue  # 游客不参与大部分优惠
                
                for discount in self.discount_types:
                    if discount == "优惠码" and user_type == "小B会员":
                        continue  # 小B会员不能使用优惠码
                    
                    for scenario in self.purchase_scenarios[:2]:  # 限制场景数量
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "优惠叠加支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{discount}优惠结算",
                                "operation": f"选择参与{discount}活动的商品进入{scenario}",
                                "action": f"应用{discount}后使用BNPL先享后付功能",
                                "sub_action": "确认优惠后价格并完成分期支付",
                                "expected_result": self.result_templates["discount_payment"]
                            },
                            "discount_type": discount,
                            "priority": "中"
                        }
                        cases.append(case)
        
        return cases

    def generate_refund_cases(self):
        """生成退款场景测试用例（V3.0格式）"""
        cases = []
        
        for timing in self.refund_timings:
            for refund_type in self.refund_types:
                for platform in ["Web", "H5"]:  # 主要平台
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "退款处理",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{timing}场景",
                            "operation": f"对BNPL支付订单发起{refund_type}",
                            "action": "提交退款申请并处理",
                            "sub_action": "调用BNPL退款接口并更新状态",
                            "expected_result": self.result_templates["refund_process"]
                        },
                        "refund_timing": timing,
                        "refund_type": refund_type,
                        "priority": "高"
                    }
                    cases.append(case)
        
        return cases

    def generate_exception_cases(self):
        """生成异常场景测试用例（V3.0格式）"""
        cases = []
        
        for platform in self.platform_users.keys():
            for exception in self.exception_scenarios:
                for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "异常处理",
                        "case_type": "反向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{exception}异常场景",
                            "operation": "正常进入BNPL支付流程",
                            "action": f"在关键步骤触发{exception}异常",
                            "sub_action": "观察系统异常处理和恢复机制",
                            "expected_result": self.result_templates["exception_handle"]
                        },
                        "exception_type": exception,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_negative_purchase_cases(self):
        """生成反向购买流程测试用例"""
        cases = []
        
        negative_scenarios = [
            ("商品缺货", "选择缺货商品进行BNPL支付"),
            ("价格变动", "支付过程中商品价格发生变化"),
            ("库存不足", "购买数量超过库存进行BNPL支付"),
            ("活动过期", "使用过期活动商品进行BNPL支付"),
            ("支付取消", "BNPL支付过程中主动取消")
        ]
        
        for platform in self.platform_users.keys():
            for scenario_name, scenario_desc in negative_scenarios:
                case = {
                    "case_id": self.get_case_id(),
                    "case_name": "支付异常",
                    "case_type": "反向流程",
                    "structure": {
                        "platform": platform,
                        "user_type": "会员",
                        "scenario": f"{scenario_name}场景",
                        "operation": scenario_desc,
                        "action": "观察系统处理和用户提示",
                        "sub_action": "验证订单状态和错误处理",
                        "expected_result": self.result_templates["exception_handle"]
                    },
                    "priority": "中"
                }
                cases.append(case)
        
        return cases

    def generate_cross_platform_cases(self):
        """生成跨平台场景测试用例"""
        cases = []
        
        cross_scenarios = [
            ("Web下单", "H5支付"),
            ("H5下单", "Web支付"),
            ("App下单", "Web支付"),
            ("Web下单", "App查看")
        ]
        
        for order_platform, pay_platform in cross_scenarios:
            case = {
                "case_id": self.get_case_id(),
                "case_name": "跨平台支付",
                "case_type": "正向流程",
                "structure": {
                    "platform": f"{order_platform}+{pay_platform}",
                    "user_type": "会员",
                    "scenario": "跨平台操作",
                    "operation": f"在{order_platform}创建订单",
                    "action": f"切换到{pay_platform}使用BNPL支付",
                    "sub_action": "完成跨平台支付流程",
                    "expected_result": self.result_templates["cross_platform"]
                },
                "priority": "中"
            }
            cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v5.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "optimization_notes": [
                "保持V3.0完整8层级结构格式",
                "合并三个BNPL服务商为统一功能",
                "简化预期结果，减少重复表述",
                "扩展正向与反向测试场景",
                "排除审批拒绝、重复提交等服务商处理场景"
            ],
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL购买流程测试",
                "description": "验证不同平台、用户、场景下的BNPL支付完整流程",
                "test_cases": self.generate_purchase_cases()
            },
            {
                "module_name": "BNPL优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的叠加使用场景",
                "test_cases": self.generate_discount_cases()
            },
            {
                "module_name": "BNPL退款场景测试",
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": self.generate_refund_cases()
            },
            {
                "module_name": "BNPL异常场景测试",
                "description": "验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_exception_cases()
            },
            {
                "module_name": "BNPL反向购买测试",
                "description": "验证异常购买场景下的系统处理能力",
                "test_cases": self.generate_negative_purchase_cases()
            },
            {
                "module_name": "BNPL跨平台测试",
                "description": "验证跨平台操作的数据同步和用户体验",
                "test_cases": self.generate_cross_platform_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = V3FormatOptimizedGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_v3_format_optimized.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL V3格式优化测试用例已生成: bnpl_v3_format_optimized.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        case_type_count = {}
        for case in module["test_cases"]:
            case_type = case.get("case_type", "未分类")
            case_type_count[case_type] = case_type_count.get(case_type, 0) + 1
        
        type_info = ", ".join([f"{k}:{v}个" for k, v in case_type_count.items()])
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例 ({type_info})")

if __name__ == "__main__":
    main()

{"title": "BNPL先享后付功能全面测试用例", "version": "v5.0", "created_date": "2025-07-01", "total_cases": 432, "optimization_notes": ["保持V3.0完整8层级结构格式", "合并三个BNPL服务商为统一功能", "简化预期结果，减少重复表述", "扩展正向与反向测试场景", "排除审批拒绝、重复提交等服务商处理场景"], "test_modules": [{"module_name": "BNPL购买流程测试", "description": "验证不同平台、用户、场景下的BNPL支付完整流程", "test_cases": [{"case_id": "BNPL_001", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_002", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_003", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_004", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_005", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_006", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_007", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_008", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_009", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_010", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_011", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_012", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_013", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_014", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_015", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_016", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_017", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_018", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_019", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_020", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_021", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_022", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_023", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_024", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_025", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_026", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_027", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_028", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_029", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_030", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_031", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_032", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_033", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_034", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_035", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_036", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_037", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_038", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_039", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_040", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_041", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_042", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_043", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_044", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_045", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_046", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_047", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_048", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_049", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_050", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_051", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_052", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_053", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_054", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_055", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_056", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_057", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_058", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_059", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_060", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_061", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_062", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_063", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_064", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_065", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_066", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_067", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_068", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_069", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_070", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_071", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_072", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_073", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_074", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_075", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_076", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_077", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_078", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_079", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_080", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_081", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_082", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_083", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_084", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_085", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_086", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_087", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_088", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_089", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_090", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_091", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_092", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_093", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_094", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_095", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_096", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_097", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_098", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_099", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_100", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_101", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_102", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_103", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_104", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_105", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_106", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_107", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_108", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_109", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_110", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_111", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_112", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_113", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_114", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_115", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_116", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_117", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_118", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_119", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_120", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_121", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_122", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_123", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_124", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_125", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_126", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_127", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_128", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_129", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_130", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_131", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_132", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_133", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_134", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_135", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_136", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_137", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_138", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_139", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_140", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_141", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_142", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_143", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_144", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_145", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_146", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_147", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_148", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_149", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_150", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_151", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_152", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_153", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_154", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_155", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_156", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_157", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_158", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_159", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_160", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_161", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_162", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_163", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_164", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_165", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_166", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_167", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_168", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_169", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_170", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_171", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_172", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_173", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_174", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_175", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_176", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_177", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_178", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_179", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_180", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_181", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_182", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_183", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_184", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_185", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_186", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_187", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_188", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_189", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_190", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_191", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_192", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_193", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_194", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_195", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_196", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_197", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_198", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_199", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_200", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_201", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_202", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_203", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_204", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_205", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_206", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_207", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_208", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_209", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_210", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_211", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_212", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_213", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_214", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_215", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_216", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_217", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_218", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_219", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_220", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_221", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_222", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_223", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_224", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_225", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_226", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_227", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_228", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_229", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_230", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_231", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_232", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_233", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_234", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_235", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_236", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_237", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_238", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_239", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_240", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_241", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_242", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_243", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_244", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_245", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_246", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_247", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_248", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_249", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_250", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_251", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_252", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_253", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_254", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_255", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_256", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_257", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_258", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_259", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_260", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_261", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_262", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_263", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_264", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_265", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_266", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_267", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_268", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_269", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_270", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_271", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品一件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_272", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加任意商品多件进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_273", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加捆绑商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_274", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加预售商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_275", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加限量商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_276", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "购物车结算", "operation": "添加促销商品进入购物车结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_277", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品一件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_278", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加任意商品多件进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_279", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加捆绑商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_280", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加预售商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_281", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加限量商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_282", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "直接结算", "operation": "添加促销商品进入直接结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_283", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品一件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_284", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加任意商品多件进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_285", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加捆绑商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_286", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加预售商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_287", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加限量商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_288", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "商品页结算", "operation": "添加促销商品进入商品页结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_289", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品一件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_290", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加任意商品多件进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_291", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加捆绑商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_292", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加预售商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_293", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加限量商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_294", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "收藏夹结算", "operation": "添加促销商品进入收藏夹结算", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_295", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品一件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_296", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加任意商品多件进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_297", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加捆绑商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_298", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加预售商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_299", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加限量商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}, {"case_id": "BNPL_300", "case_name": "支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "立即购买", "operation": "添加促销商品进入立即购买", "action": "点击结算并使用BNPL先享后付功能", "sub_action": "选择分期方案并确认支付", "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"}, "priority": "高"}]}, {"module_name": "BNPL优惠叠加测试", "description": "验证BNPL与各种优惠活动的叠加使用场景", "test_cases": [{"case_id": "BNPL_301", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_302", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_303", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入购物车结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_304", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入直接结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_305", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_306", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_307", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_308", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_309", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_310", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_311", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_312", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_313", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_314", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_315", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_316", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_317", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_318", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Android", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_319", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_320", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_321", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入购物车结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_322", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入直接结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_323", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_324", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_325", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_326", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_327", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_328", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_329", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_330", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_331", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_332", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_333", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_334", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_335", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_336", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "iOS", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_337", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_338", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_339", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入购物车结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_340", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入直接结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_341", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_342", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_343", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_344", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_345", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_346", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_347", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_348", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_349", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_350", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_351", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_352", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_353", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_354", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_355", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_356", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_357", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入购物车结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_358", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "优惠码优惠结算", "operation": "选择参与优惠码活动的商品进入直接结算", "action": "应用优惠码后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠码", "priority": "中"}, {"case_id": "BNPL_359", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_360", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_361", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_362", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_363", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_364", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_365", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入购物车结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_366", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "优惠券优惠结算", "operation": "选择参与优惠券活动的商品进入直接结算", "action": "应用优惠券后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "优惠券", "priority": "中"}, {"case_id": "BNPL_367", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入购物车结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_368", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "O币抵扣优惠结算", "operation": "选择参与O币抵扣活动的商品进入直接结算", "action": "应用O币抵扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "O币抵扣", "priority": "中"}, {"case_id": "BNPL_369", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入购物车结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_370", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "会员价优惠结算", "operation": "选择参与会员价活动的商品进入直接结算", "action": "应用会员价后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "会员价", "priority": "中"}, {"case_id": "BNPL_371", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入购物车结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}, {"case_id": "BNPL_372", "case_name": "优惠叠加支付", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "小B会员", "scenario": "限时折扣优惠结算", "operation": "选择参与限时折扣活动的商品进入直接结算", "action": "应用限时折扣后使用BNPL先享后付功能", "sub_action": "确认优惠后价格并完成分期支付", "expected_result": "优惠正确应用，BNPL基于折后价计算，金额准确无误"}, "discount_type": "限时折扣", "priority": "中"}]}, {"module_name": "BNPL退款场景测试", "description": "验证不同时机和类型的BNPL订单退款处理", "test_cases": [{"case_id": "BNPL_373", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "订单确认后立即退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_374", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "订单确认后立即退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_375", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "订单确认后立即退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_376", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "订单确认后立即退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "订单确认后立即退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_377", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货前退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_378", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货前退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_379", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货前退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_380", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货前退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货前退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_381", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_382", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_383", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_384", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "发货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "发货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_385", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "收货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_386", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起全额退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "收货后退款", "refund_type": "全额退款", "priority": "高"}, {"case_id": "BNPL_387", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "收货后退款", "refund_type": "部分退款", "priority": "高"}, {"case_id": "BNPL_388", "case_name": "退款处理", "case_type": "正向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "收货后退款场景", "operation": "对BNPL支付订单发起部分退款", "action": "提交退款申请并处理", "sub_action": "调用BNPL退款接口并更新状态", "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"}, "refund_timing": "收货后退款", "refund_type": "部分退款", "priority": "高"}]}, {"module_name": "BNPL异常场景测试", "description": "验证各种异常情况下的系统处理能力", "test_cases": [{"case_id": "BNPL_389", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_390", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_391", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_392", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_393", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_394", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_395", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_396", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_397", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_398", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_399", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_400", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_401", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_402", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_403", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "游客", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "应用崩溃", "priority": "中"}, {"case_id": "BNPL_404", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "网络超时异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发网络超时异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "网络超时", "priority": "中"}, {"case_id": "BNPL_405", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "服务商维护异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发服务商维护异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "服务商维护", "priority": "中"}, {"case_id": "BNPL_406", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "支付中断异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发支付中断异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "支付中断", "priority": "中"}, {"case_id": "BNPL_407", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "浏览器关闭异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发浏览器关闭异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "浏览器关闭", "priority": "中"}, {"case_id": "BNPL_408", "case_name": "异常处理", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "游客", "scenario": "应用崩溃异常场景", "operation": "正常进入BNPL支付流程", "action": "在关键步骤触发应用崩溃异常", "sub_action": "观察系统异常处理和恢复机制", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "exception_type": "应用崩溃", "priority": "中"}]}, {"module_name": "BNPL反向购买测试", "description": "验证异常购买场景下的系统处理能力", "test_cases": [{"case_id": "BNPL_409", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "商品缺货场景", "operation": "选择缺货商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_410", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "价格变动场景", "operation": "支付过程中商品价格发生变化", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_411", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "库存不足场景", "operation": "购买数量超过库存进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_412", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "活动过期场景", "operation": "使用过期活动商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_413", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Android", "user_type": "会员", "scenario": "支付取消场景", "operation": "BNPL支付过程中主动取消", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_414", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "商品缺货场景", "operation": "选择缺货商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_415", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "价格变动场景", "operation": "支付过程中商品价格发生变化", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_416", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "库存不足场景", "operation": "购买数量超过库存进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_417", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "活动过期场景", "operation": "使用过期活动商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_418", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "iOS", "user_type": "会员", "scenario": "支付取消场景", "operation": "BNPL支付过程中主动取消", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_419", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "商品缺货场景", "operation": "选择缺货商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_420", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "价格变动场景", "operation": "支付过程中商品价格发生变化", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_421", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "库存不足场景", "operation": "购买数量超过库存进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_422", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "活动过期场景", "operation": "使用过期活动商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_423", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "Web", "user_type": "会员", "scenario": "支付取消场景", "operation": "BNPL支付过程中主动取消", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_424", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "商品缺货场景", "operation": "选择缺货商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_425", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "价格变动场景", "operation": "支付过程中商品价格发生变化", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_426", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "库存不足场景", "operation": "购买数量超过库存进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_427", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "活动过期场景", "operation": "使用过期活动商品进行BNPL支付", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}, {"case_id": "BNPL_428", "case_name": "支付异常", "case_type": "反向流程", "structure": {"platform": "H5", "user_type": "会员", "scenario": "支付取消场景", "operation": "BNPL支付过程中主动取消", "action": "观察系统处理和用户提示", "sub_action": "验证订单状态和错误处理", "expected_result": "系统正确处理异常，显示友好提示，订单状态保持一致"}, "priority": "中"}]}, {"module_name": "BNPL跨平台测试", "description": "验证跨平台操作的数据同步和用户体验", "test_cases": [{"case_id": "BNPL_429", "case_name": "跨平台支付", "case_type": "正向流程", "structure": {"platform": "Web下单+H5支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到H5支付使用BNPL支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台数据同步正确，用户体验流畅一致"}, "priority": "中"}, {"case_id": "BNPL_430", "case_name": "跨平台支付", "case_type": "正向流程", "structure": {"platform": "H5下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在H5下单创建订单", "action": "切换到Web支付使用BNPL支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台数据同步正确，用户体验流畅一致"}, "priority": "中"}, {"case_id": "BNPL_431", "case_name": "跨平台支付", "case_type": "正向流程", "structure": {"platform": "App下单+Web支付", "user_type": "会员", "scenario": "跨平台操作", "operation": "在App下单创建订单", "action": "切换到Web支付使用BNPL支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台数据同步正确，用户体验流畅一致"}, "priority": "中"}, {"case_id": "BNPL_432", "case_name": "跨平台支付", "case_type": "正向流程", "structure": {"platform": "Web下单+App查看", "user_type": "会员", "scenario": "跨平台操作", "operation": "在Web下单创建订单", "action": "切换到App查看使用BNPL支付", "sub_action": "完成跨平台支付流程", "expected_result": "跨平台数据同步正确，用户体验流畅一致"}, "priority": "中"}]}]}
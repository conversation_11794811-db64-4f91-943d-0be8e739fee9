# 测试方法论知识库

## 核心测试原则

### 第一性原理
从产品基础功能出发，推导测试场景：
1. **基础功能识别**: 确定产品核心功能点
2. **场景推导**: 从基础功能推导出各种使用场景
3. **边界探索**: 识别功能边界和异常情况
4. **组合验证**: 验证功能间的交互和组合

### KISS原则（Keep It Simple, Stupid）
- **用例设计**: 每个用例保持简洁明了
- **步骤描述**: 操作步骤清晰易懂
- **预期结果**: 明确具体的验证点
- **避免复杂**: 不在单个用例中验证过多功能点

### 正交分析原则
- **因子识别**: 识别影响系统的关键因子
- **水平确定**: 确定每个因子的不同取值
- **组合优化**: 用最少的测试用例覆盖最多的组合
- **避免重复**: 消除冗余的测试场景

### 单一职责原则
- **功能独立**: 每个测试用例只验证一个功能点
- **职责明确**: 测试目标清晰单一
- **结果明确**: 便于定位问题和分析结果
- **维护简单**: 降低用例维护成本

## 等价类划分方法

### 有效等价类
- **正常输入**: 符合系统要求的输入值
- **边界值**: 有效范围的边界值
- **典型值**: 有效范围内的代表性值

### 无效等价类
- **超出范围**: 超过系统限制的输入值
- **格式错误**: 不符合格式要求的输入
- **空值/null**: 空输入或null值
- **特殊字符**: 可能引起异常的特殊字符

### 边界值分析
- **上边界**: 有效范围的最大值
- **下边界**: 有效范围的最小值
- **边界外**: 刚好超出边界的值
- **边界内**: 刚好在边界内的值

## 跨境电商测试策略

### 多用户角度测试
1. **游客视角**: 
   - 浏览商品功能
   - 受限的活动参与
   - 登录引导流程

2. **普通会员视角**:
   - 会员价格显示
   - 会员专属活动
   - 积分/O币功能

3. **小B会员视角**:
   - 专享折扣价格
   - 特殊权限验证
   - 限制功能测试

### 多平台兼容性测试
1. **功能一致性**: 四个端口功能保持一致
2. **界面适配**: 不同屏幕尺寸的适配
3. **性能差异**: 各平台的性能表现
4. **交互体验**: 平台特有的交互方式

### 营销活动测试策略
1. **单一活动测试**: 每种活动的独立功能验证
2. **活动组合测试**: 多种活动的叠加使用
3. **权限控制测试**: 不同用户的活动参与权限
4. **时间边界测试**: 活动开始/结束时间点
5. **商品范围测试**: 活动适用商品的正确性

### 支付流程测试
1. **价格计算**: 各种优惠的正确计算
2. **支付方式**: 多种支付方式的兼容性
3. **订单生成**: 订单信息的完整性
4. **异常处理**: 支付失败的处理机制

## 测试用例设计模式

### 正向测试用例
- **正常流程**: 按照预期流程操作
- **有效输入**: 使用有效的输入数据
- **成功场景**: 验证功能正常工作

### 负向测试用例
- **异常流程**: 非正常的操作流程
- **无效输入**: 使用无效的输入数据
- **错误处理**: 验证系统的错误处理能力

### 边界测试用例
- **临界值**: 测试边界条件
- **极限值**: 测试系统极限
- **溢出测试**: 验证数据溢出处理

### 组合测试用例
- **功能组合**: 多个功能的组合使用
- **参数组合**: 多个参数的组合输入
- **场景组合**: 复杂业务场景的组合

## 测试数据设计

### 用户数据
- **游客**: 未登录状态
- **新注册会员**: 刚注册的会员账号
- **老会员**: 有购买历史的会员
- **小B会员**: 具有特殊权限的会员
- **异常账号**: 被冻结、限制的账号

### 商品数据
- **普通商品**: 无特殊限制的商品
- **限制商品**: 有地区、用户限制的商品
- **促销商品**: 参与活动的商品
- **缺货商品**: 库存为0的商品
- **预售商品**: 未上市的商品

### 活动数据
- **进行中活动**: 当前有效的活动
- **即将开始**: 未开始的活动
- **已结束**: 过期的活动
- **限量活动**: 有数量限制的活动

## 自动化测试考虑

### 可自动化场景
- **重复性高**: 需要频繁执行的测试
- **数据驱动**: 大量数据的验证
- **回归测试**: 版本更新后的验证
- **性能测试**: 压力和负载测试

### 手工测试场景
- **用户体验**: 界面交互体验
- **探索性测试**: 发现未知问题
- **复杂业务**: 复杂的业务逻辑验证
- **一次性测试**: 临时性的测试需求

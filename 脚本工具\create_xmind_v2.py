#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建BNPL测试用例XMind文件 V2
"""

import json
import os
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime

def create_xmind_content():
    """创建XMind内容XML"""
    # 读取JSON数据
    with open("bnpl_test_cases_v2.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建根元素
    xmap = ET.Element("xmap-content", xmlns="urn:xmind:xmap:xmlns:content:2.0")
    xmap.set("xmlns:fo", "http://www.w3.org/1999/XSL/Format")
    
    # 创建工作表
    sheet = ET.SubElement(xmap, "sheet", id="sheet1", theme="theme1")
    sheet_title = ET.SubElement(sheet, "title")
    sheet_title.text = "BNPL测试用例"
    
    # 创建根主题
    topic = ET.SubElement(sheet, "topic", id="topic_1")
    title = ET.SubElement(topic, "title")
    title.text = data["title"]
    
    # 创建子主题容器
    children = ET.SubElement(topic, "children")
    topics = ET.SubElement(children, "topics", type="attached")
    
    topic_id = 2
    
    # 添加版本信息
    version_topic = ET.SubElement(topics, "topic", id=f"topic_{topic_id}")
    topic_id += 1
    version_title = ET.SubElement(version_topic, "title")
    version_title.text = f"版本信息 {data['version']}"
    
    version_children = ET.SubElement(version_topic, "children")
    version_topics = ET.SubElement(version_children, "topics", type="attached")
    
    version_details = [
        f"创建日期: {data['created_date']}",
        "测试范围: Affirm、Klarna、Afterpay",
        "测试类型: 功能测试、异常测试",
        "覆盖平台: Android、iOS、Web、H5（APP端不支持游客）"
    ]
    
    for detail in version_details:
        detail_topic = ET.SubElement(version_topics, "topic", id=f"topic_{topic_id}")
        topic_id += 1
        detail_title = ET.SubElement(detail_topic, "title")
        detail_title.text = detail
    
    # 添加测试模块
    for module in data["test_modules"]:
        module_topic = ET.SubElement(topics, "topic", id=f"topic_{topic_id}")
        topic_id += 1
        module_title = ET.SubElement(module_topic, "title")
        module_title.text = module["module_name"]
        
        module_children = ET.SubElement(module_topic, "children")
        module_topics = ET.SubElement(module_children, "topics", type="attached")
        
        # 模块描述
        desc_topic = ET.SubElement(module_topics, "topic", id=f"topic_{topic_id}")
        topic_id += 1
        desc_title = ET.SubElement(desc_topic, "title")
        desc_title.text = f"模块描述: {module['description']}"
        
        # 用例数量
        count_topic = ET.SubElement(module_topics, "topic", id=f"topic_{topic_id}")
        topic_id += 1
        count_title = ET.SubElement(count_topic, "title")
        count_title.text = f"用例数量: {len(module['test_cases'])}个"
        
        # 按优先级分组
        high_cases = [case for case in module["test_cases"] if case["priority"] == "高"]
        medium_cases = [case for case in module["test_cases"] if case["priority"] == "中"]
        
        if high_cases:
            high_topic = ET.SubElement(module_topics, "topic", id=f"topic_{topic_id}")
            topic_id += 1
            high_title = ET.SubElement(high_topic, "title")
            high_title.text = "高优先级用例"
            
            high_children = ET.SubElement(high_topic, "children")
            high_topics = ET.SubElement(high_children, "topics", type="attached")
            
            for case in high_cases[:10]:  # 限制显示数量
                case_topic = ET.SubElement(high_topics, "topic", id=f"topic_{topic_id}")
                topic_id += 1
                case_title = ET.SubElement(case_topic, "title")
                case_title.text = f"{case['case_id']}: {case['case_name']}"
                
                case_children = ET.SubElement(case_topic, "children")
                case_topics = ET.SubElement(case_children, "topics", type="attached")
                
                # 前置条件
                if case.get("preconditions"):
                    precond_topic = ET.SubElement(case_topics, "topic", id=f"topic_{topic_id}")
                    topic_id += 1
                    precond_title = ET.SubElement(precond_topic, "title")
                    precond_title.text = "前置条件"
                    
                    precond_children = ET.SubElement(precond_topic, "children")
                    precond_topics = ET.SubElement(precond_children, "topics", type="attached")
                    
                    for condition in case["preconditions"]:
                        cond_topic = ET.SubElement(precond_topics, "topic", id=f"topic_{topic_id}")
                        topic_id += 1
                        cond_title = ET.SubElement(cond_topic, "title")
                        cond_title.text = condition
                
                # 测试步骤
                if case.get("test_steps"):
                    steps_topic = ET.SubElement(case_topics, "topic", id=f"topic_{topic_id}")
                    topic_id += 1
                    steps_title = ET.SubElement(steps_topic, "title")
                    steps_title.text = "测试步骤"
                    
                    steps_children = ET.SubElement(steps_topic, "children")
                    steps_topics = ET.SubElement(steps_children, "topics", type="attached")
                    
                    for step in case["test_steps"]:
                        step_topic = ET.SubElement(steps_topics, "topic", id=f"topic_{topic_id}")
                        topic_id += 1
                        step_title = ET.SubElement(step_topic, "title")
                        step_title.text = step
                
                # 预期结果
                if case.get("expected_results"):
                    results_topic = ET.SubElement(case_topics, "topic", id=f"topic_{topic_id}")
                    topic_id += 1
                    results_title = ET.SubElement(results_topic, "title")
                    results_title.text = "预期结果"
                    
                    results_children = ET.SubElement(results_topic, "children")
                    results_topics = ET.SubElement(results_children, "topics", type="attached")
                    
                    for result in case["expected_results"]:
                        result_topic = ET.SubElement(results_topics, "topic", id=f"topic_{topic_id}")
                        topic_id += 1
                        result_title = ET.SubElement(result_topic, "title")
                        result_title.text = result
        
        if medium_cases:
            medium_topic = ET.SubElement(module_topics, "topic", id=f"topic_{topic_id}")
            topic_id += 1
            medium_title = ET.SubElement(medium_topic, "title")
            medium_title.text = "中优先级用例"
            
            medium_children = ET.SubElement(medium_topic, "children")
            medium_topics = ET.SubElement(medium_children, "topics", type="attached")
            
            for case in medium_cases[:5]:  # 限制显示数量
                case_topic = ET.SubElement(medium_topics, "topic", id=f"topic_{topic_id}")
                topic_id += 1
                case_title = ET.SubElement(case_topic, "title")
                case_title.text = f"{case['case_id']}: {case['case_name']}"
    
    # 添加测试总结
    summary_topic = ET.SubElement(topics, "topic", id=f"topic_{topic_id}")
    topic_id += 1
    summary_title = ET.SubElement(summary_topic, "title")
    summary_title.text = "测试总结"
    
    summary_children = ET.SubElement(summary_topic, "children")
    summary_topics = ET.SubElement(summary_children, "topics", type="attached")
    
    total_cases = sum(len(module["test_cases"]) for module in data["test_modules"])
    
    # 总用例数
    total_topic = ET.SubElement(summary_topics, "topic", id=f"topic_{topic_id}")
    topic_id += 1
    total_title = ET.SubElement(total_topic, "title")
    total_title.text = f"总用例数: {total_cases}个"
    
    # 覆盖范围
    coverage_topic = ET.SubElement(summary_topics, "topic", id=f"topic_{topic_id}")
    topic_id += 1
    coverage_title = ET.SubElement(coverage_topic, "title")
    coverage_title.text = "覆盖范围"
    
    coverage_children = ET.SubElement(coverage_topic, "children")
    coverage_topics = ET.SubElement(coverage_children, "topics", type="attached")
    
    coverage_items = [
        "BNPL服务商: Affirm、Klarna、Afterpay",
        "用户类型: 游客（仅Web/H5）、普通会员、小B会员",
        "平台端口: Android、iOS、Web、H5",
        "测试场景: 购买流程、退款处理、优惠叠加、异常处理"
    ]
    
    for item in coverage_items:
        item_topic = ET.SubElement(coverage_topics, "topic", id=f"topic_{topic_id}")
        topic_id += 1
        item_title = ET.SubElement(item_topic, "title")
        item_title.text = item
    
    return xmap

def create_manifest_xml():
    """创建manifest.xml"""
    manifest = ET.Element("manifest", xmlns="urn:xmind:xmap:xmlns:manifest:1.0")
    
    file_entry = ET.SubElement(manifest, "file-entry", 
                             **{"full-path": "content.xml", 
                                "media-type": "text/xml"})
    
    file_entry2 = ET.SubElement(manifest, "file-entry",
                              **{"full-path": "META-INF/",
                                 "media-type": ""})
    
    file_entry3 = ET.SubElement(manifest, "file-entry",
                              **{"full-path": "meta.xml",
                                 "media-type": "text/xml"})
    
    return manifest

def create_meta_xml():
    """创建meta.xml"""
    meta = ET.Element("meta", xmlns="urn:xmind:xmap:xmlns:meta:2.0")
    
    # 创建者
    creator = ET.SubElement(meta, "Creator")
    creator.text = "BNPL测试用例生成器 V2"
    
    # 创建时间
    created = ET.SubElement(meta, "Created")
    created.text = datetime.now().isoformat()
    
    # 版本
    version = ET.SubElement(meta, "Version")
    version.text = "2.0"
    
    return meta

def main():
    """主函数"""
    output_file = "输出用例/BNPL先享后付_全面测试用例_v2.0.xmind"
    
    # 创建临时目录
    temp_dir = "temp_xmind_v2"
    os.makedirs(temp_dir, exist_ok=True)
    os.makedirs(os.path.join(temp_dir, "META-INF"), exist_ok=True)
    
    try:
        # 生成content.xml
        content_xml = create_xmind_content()
        content_tree = ET.ElementTree(content_xml)
        content_tree.write(os.path.join(temp_dir, "content.xml"), 
                         encoding="utf-8", xml_declaration=True)
        
        # 生成manifest.xml
        manifest_xml = create_manifest_xml()
        manifest_tree = ET.ElementTree(manifest_xml)
        manifest_tree.write(os.path.join(temp_dir, "META-INF", "manifest.xml"),
                          encoding="utf-8", xml_declaration=True)
        
        # 生成meta.xml
        meta_xml = create_meta_xml()
        meta_tree = ET.ElementTree(meta_xml)
        meta_tree.write(os.path.join(temp_dir, "meta.xml"),
                      encoding="utf-8", xml_declaration=True)
        
        # 创建XMind文件（实际上是ZIP文件）
        with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"✅ XMind文件已成功生成: {output_file}")
        print("📊 文件统计:")
        print("  - BNPL购买流程测试: 30个用例")
        print("  - BNPL退款场景测试: 24个用例")
        print("  - BNPL优惠叠加测试: 27个用例")
        print("  - BNPL异常场景测试: 15个用例")
        print("🎯 总计: 96个测试用例")
        
    finally:
        # 清理临时文件
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

if __name__ == "__main__":
    main()

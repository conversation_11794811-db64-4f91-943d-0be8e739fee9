# BNPL用例格式规范

## 标准格式要求

### 用例编号格式
```
BNPL_001: 支付
BNPL_002: 优惠叠加支付
BNPL_003: 退款处理
```

### 层级结构格式（8层）
```
BNPL_001: 支付
├── H5                                    # 第2层：平台
    ├── 游客                              # 第3层：用户类型
        ├── 购物车结算                    # 第4层：场景
            ├── 添加任意商品多件进入购物车结算    # 第5层：操作
                ├── 点击结算并使用BNPL先享后付功能  # 第6层：步骤
                    ├── 选择分期方案并确认支付         # 第7层：子步骤
                        └── 扣款金额与分期计划一致... # 第8层：预期结果
```

### 参考格式示例
基于`BNPL先享后付_全面测试用例_v2.0.txt`的格式：

```
BNPL_PURCHASE_001: 支付
├── H5
    ├── 游客
        ├── 购物车结算
            ├── 添加任意商品多件进入购物车结算
                ├── 点击结算并使用BNPL先享后付功能
                    └── 扣款金额与分期计划一致Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
            ├── 添加任意商品一件进入购物车结算
                ├── 点击结算并使用BNPL先享后付功能
                    └── 扣款金额与分期计划一致Affirm支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
        └── 直接结算
    ├── 小B会员
    └── 会员
```

## 用例类型分类

### 正向用例（约30-40%）
- 基础支付流程
- 优惠叠加流程
- 退款处理流程
- 跨平台操作

### 反向用例（约60-70%）
**重点关注异常流问题**，包括：

#### 网络异常类
- 网络超时
- 网络中断
- 网络波动
- 连接失败

#### 系统异常类
- 服务商维护
- API调用失败
- 数据同步异常
- 系统崩溃

#### 支付异常类
- 支付中断
- 支付超时
- 支付取消
- 余额不足

#### 业务异常类
- 商品缺货
- 价格变动
- 库存不足
- 活动过期
- 权限不足

#### 用户操作异常类
- 浏览器关闭
- 应用崩溃
- 重复操作
- 非法操作

#### 数据异常类
- 数据篡改
- 数据丢失
- 数据不一致
- 格式错误

#### 安全异常类
- 会话过期
- 权限验证失败
- 跨站请求
- 重放攻击

## 用例数量要求

### 目标数量
- **总用例数**: 500-600条
- **正向用例**: 150-200条（30-40%）
- **反向用例**: 300-400条（60-70%）

### 分布原则
1. **重点关注异常流**: 反向用例应该占主导地位
2. **全面覆盖风险点**: 每种异常都要充分测试
3. **多维度组合**: 平台×用户×场景×异常类型
4. **边界值验证**: 各种边界条件的异常处理

## 预期结果格式

### 正向用例结果
```
扣款金额与分期计划一致，BNPL支付选项正常显示，分期方案清晰展示，费用透明，审批流程顺畅，无异常中断，支付成功后订单状态更新为已支付，用户收到支付确认和分期计划通知
```

### 反向用例结果
```
系统正确识别[异常类型]异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款，异常信息记录完整
```

## 编写原则

### 1. 异常优先原则
- 优先设计各种异常场景
- 重点验证异常处理机制
- 确保系统健壮性

### 2. 全面覆盖原则
- 覆盖所有可能的异常情况
- 考虑异常的组合场景
- 验证异常恢复能力

### 3. 真实性原则
- 基于真实的业务场景
- 考虑实际使用中的问题
- 模拟真实的异常情况

### 4. 可执行原则
- 异常场景可以模拟
- 验证点明确具体
- 结果可以观察验证

## 质量标准

### 用例质量要求
1. **场景真实**: 基于真实业务场景
2. **步骤清晰**: 操作步骤明确可执行
3. **验证明确**: 预期结果具体可验证
4. **异常全面**: 异常场景覆盖充分

### 覆盖率要求
1. **平台覆盖**: 4个平台全覆盖
2. **用户覆盖**: 3种用户类型全覆盖
3. **场景覆盖**: 主要业务场景全覆盖
4. **异常覆盖**: 7大类异常全覆盖

### 优先级分配
1. **高优先级**: 核心支付流程、关键异常场景
2. **中优先级**: 优惠叠加、退款处理、一般异常
3. **低优先级**: 边界值测试、兼容性测试

这个格式规范将确保所有BNPL测试用例都按照统一的标准格式编写，重点关注异常流问题，提高测试的全面性和有效性。

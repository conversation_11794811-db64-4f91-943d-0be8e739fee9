#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按照参考格式生成BNPL测试用例TXT文件
严格按照BNPL先享后付_全面测试用例_v2.0.txt的格式
"""

from datetime import datetime

class TxtFormatBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接结算"
        ]
        
        # 商品类型（精简为3类）
        self.product_types = [
            "正常商品",
            "限时折扣商品", 
            "自由捆绑商品"
        ]
        
        # 操作类型
        self.operations = [
            "添加任意商品一件",
            "添加任意商品多件"
        ]
        
        # 优惠类型
        self.discount_types = [
            "优惠券",
            "优惠码", 
            "O币抵扣",
            "会员价"
        ]
        
        # 异常场景（重点扩展）
        self.exception_scenarios = {
            "网络异常": [
                "网络超时", "网络中断", "网络波动", "连接失败"
            ],
            "系统异常": [
                "服务商维护", "API调用失败", "数据同步异常", "系统崩溃"
            ],
            "支付异常": [
                "支付中断", "支付超时", "支付取消", "余额不足"
            ],
            "业务异常": [
                "商品缺货", "价格变动", "库存不足", "活动过期"
            ],
            "用户操作异常": [
                "浏览器关闭", "应用崩溃", "重复操作", "非法操作"
            ]
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_txt_content(self):
        """生成TXT格式内容"""
        content = []
        
        # 标题和版本信息
        content.append("BNPL先享后付功能测试用例 (v9.0)")
        content.append("=" * 50)
        content.append("")
        content.append("# Sheet_1")
        content.append("-" * 50)
        content.append("BNPL先享后付功能测试用例")
        content.append("├── 版本信息 v9.0")
        content.append(f"    ├── 创建日期: {datetime.now().strftime('%Y-%m-%d')}")
        content.append("    ├── 测试范围: BNPL先享后付功能（合并三个服务商）")
        content.append("    ├── 测试类型: 功能测试、异常测试")
        content.append("    ├── 覆盖平台: Android、iOS、Web、H5（APP端不支持游客）")
        content.append("    └── 重点关注异常流问题，反向用例占主导地位")
        
        # 生成正向购买流程测试
        content.append("├── BNPL正向购买流程测试")
        case_count = self.generate_purchase_cases_txt(content)
        
        # 生成正向优惠叠加测试
        content.append("├── BNPL正向优惠叠加测试")
        case_count += self.generate_discount_cases_txt(content)
        
        # 生成正向退款场景测试
        content.append("├── BNPL正向退款场景测试")
        case_count += self.generate_refund_cases_txt(content)
        
        # 生成反向异常流程测试（重点）
        content.append("├── BNPL反向异常流程测试")
        case_count += self.generate_exception_cases_txt(content)
        
        # 生成反向业务异常测试
        content.append("└── BNPL反向业务异常测试")
        case_count += self.generate_business_exception_cases_txt(content)
        
        # 添加总结
        content.append("")
        content.append("=" * 50)
        content.append(f"总用例数: {case_count}个")
        content.append("重点关注异常流问题，反向用例占主导地位")
        content.append("=" * 50)
        
        return content, case_count

    def generate_purchase_cases_txt(self, content):
        """生成正向购买流程测试用例TXT"""
        case_count = 0
        
        for platform in self.platform_users.keys():
            content.append(f"   ├── {self.get_case_id()}: 支付")
            content.append(f"\t\t\t\t├── {platform}")
            
            for user_type in self.platform_users[platform]:
                content.append(f"\t\t\t\t\t├── {user_type}")
                
                for scenario in self.purchase_scenarios:
                    content.append(f"\t\t\t\t\t\t├── {scenario}")
                    
                    for product_type in self.product_types:
                        content.append(f"\t\t\t\t\t\t\t├── {product_type}")
                        
                        for operation in self.operations:
                            content.append(f"\t\t\t\t\t\t\t\t├── {operation}进入{scenario}")
                            content.append(f"\t\t\t\t\t\t\t\t\t├── 点击结算并使用BNPL先享后付功能")
                            content.append(f"\t\t\t\t\t\t\t\t\t\t├── 选择分期方案并确认支付")
                            content.append(f"\t\t\t\t\t\t\t\t\t\t\t└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知")
                            case_count += 1
        
        return case_count

    def generate_discount_cases_txt(self, content):
        """生成正向优惠叠加测试用例TXT"""
        case_count = 0
        
        for platform in self.platform_users.keys():
            content.append(f"   ├── {self.get_case_id()}: 优惠叠加支付")
            content.append(f"\t\t\t\t├── {platform}")
            
            for user_type in self.platform_users[platform]:
                if user_type == "游客":
                    continue  # 游客不参与大部分优惠
                
                content.append(f"\t\t\t\t\t├── {user_type}")
                
                for discount in self.discount_types:
                    if discount == "优惠码" and user_type == "小B会员":
                        continue  # 小B会员不能使用优惠码
                    
                    content.append(f"\t\t\t\t\t\t├── {discount}优惠结算")
                    
                    for scenario in self.purchase_scenarios:
                        content.append(f"\t\t\t\t\t\t\t├── 选择参与{discount}活动的商品进入{scenario}")
                        content.append(f"\t\t\t\t\t\t\t\t├── 应用{discount}后使用BNPL先享后付功能")
                        content.append(f"\t\t\t\t\t\t\t\t\t├── 确认优惠后价格并完成分期支付")
                        content.append(f"\t\t\t\t\t\t\t\t\t\t└── {discount}优惠正确应用，BNPL基于折后价计算，金额准确无误")
                        case_count += 1
        
        return case_count

    def generate_refund_cases_txt(self, content):
        """生成正向退款场景测试用例TXT"""
        case_count = 0
        
        refund_timings = ["订单确认后立即退款", "发货前退款", "发货后退款", "收货后退款"]
        refund_types = ["全额退款", "部分退款"]
        
        content.append(f"   ├── {self.get_case_id()}: 退款处理")
        content.append(f"\t\t\t\t├── Web/H5")
        content.append(f"\t\t\t\t\t├── 会员")
        
        for timing in refund_timings:
            content.append(f"\t\t\t\t\t\t├── {timing}场景")
            
            for refund_type in refund_types:
                content.append(f"\t\t\t\t\t\t\t├── 对BNPL支付订单发起{refund_type}")
                content.append(f"\t\t\t\t\t\t\t\t├── 提交退款申请并处理")
                content.append(f"\t\t\t\t\t\t\t\t\t├── 调用BNPL退款接口并更新状态")
                content.append(f"\t\t\t\t\t\t\t\t\t\t└── 退款处理成功，分期计划相应调整，状态同步正确")
                case_count += 1
        
        return case_count

    def generate_exception_cases_txt(self, content):
        """生成反向异常流程测试用例TXT（重点）"""
        case_count = 0
        
        for category, exceptions in self.exception_scenarios.items():
            content.append(f"   ├── {self.get_case_id()}: {category}处理")
            
            for platform in self.platform_users.keys():
                content.append(f"\t\t\t\t├── {platform}")
                
                for user_type in self.platform_users[platform]:
                    content.append(f"\t\t\t\t\t├── {user_type}")
                    
                    for exception in exceptions:
                        content.append(f"\t\t\t\t\t\t├── {exception}异常场景")
                        content.append(f"\t\t\t\t\t\t\t├── 正常进入BNPL支付流程")
                        content.append(f"\t\t\t\t\t\t\t\t├── 在支付过程中触发{exception}异常")
                        content.append(f"\t\t\t\t\t\t\t\t\t├── 观察系统异常处理和恢复机制")
                        content.append(f"\t\t\t\t\t\t\t\t\t\t└── 系统正确识别{exception}异常，显示友好错误提示，订单状态保持一致")
                        case_count += 1
                        
                        # 异常恢复场景
                        content.append(f"\t\t\t\t\t\t├── {exception}异常恢复场景")
                        content.append(f"\t\t\t\t\t\t\t├── 在{exception}异常发生后")
                        content.append(f"\t\t\t\t\t\t\t\t├── 尝试恢复并重新进行BNPL支付")
                        content.append(f"\t\t\t\t\t\t\t\t\t├── 验证异常恢复后的系统状态")
                        content.append(f"\t\t\t\t\t\t\t\t\t\t└── {exception}异常恢复后，系统状态正常，可以重新进行BNPL支付")
                        case_count += 1
        
        return case_count

    def generate_business_exception_cases_txt(self, content):
        """生成反向业务异常测试用例TXT"""
        case_count = 0
        
        business_exceptions = [
            "优惠券过期", "优惠码无效", "O币余额不足", 
            "退款金额超限", "重复退款申请", "退款时效过期"
        ]
        
        content.append(f"   └── {self.get_case_id()}: 业务异常处理")
        
        for platform in self.platform_users.keys():
            content.append(f"\t\t\t\t├── {platform}")
            content.append(f"\t\t\t\t\t├── 会员")
            
            for exception in business_exceptions:
                content.append(f"\t\t\t\t\t\t├── {exception}场景")
                content.append(f"\t\t\t\t\t\t\t├── 触发{exception}异常")
                content.append(f"\t\t\t\t\t\t\t\t├── 观察系统业务验证和错误处理")
                content.append(f"\t\t\t\t\t\t\t\t\t├── 验证BNPL支付流程的异常处理")
                content.append(f"\t\t\t\t\t\t\t\t\t\t└── 系统正确处理{exception}异常，显示友好提示，订单状态保持一致")
                case_count += 1
        
        return case_count

def main():
    """主函数"""
    generator = TxtFormatBNPLGenerator()
    content, case_count = generator.generate_txt_content()
    
    # 写入TXT文件
    output_file = "输出用例/BNPL先享后付_全面测试用例_v9.0.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(content))
    
    print(f"✅ BNPL测试用例TXT文件已生成: {output_file}")
    print(f"📊 总用例数: {case_count}个")
    print("📋 按照参考格式生成，每个叶子节点都是一个独立用例")
    print("🔍 重点关注异常流问题，反向用例占主导地位")

if __name__ == "__main__":
    main()

BNPL先享后付功能测试用例 (v9.0)
==================================================

# Sheet_1
--------------------------------------------------
BNPL先享后付功能测试用例
├── 版本信息 v9.0
    ├── 创建日期: 2025-07-01
    ├── 测试范围: BNPL先享后付功能（合并三个服务商）
    ├── 测试类型: 功能测试、异常测试
    ├── 覆盖平台: Android、iOS、Web、H5（APP端不支持游客）
    └── 重点关注异常流问题，反向用例占主导地位
├── BNPL正向购买流程测试
   ├── BNPL_001: 支付
				├── Android
					├── 会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 小B会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
   ├── BNPL_002: 支付
				├── iOS
					├── 会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 小B会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
   ├── BNPL_003: 支付
				├── Web
					├── 游客
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 小B会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
   ├── BNPL_004: 支付
				├── H5
					├── 游客
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
					├── 小B会员
						├── 购物车结算
							├── 正常商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入购物车结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
						├── 直接结算
							├── 正常商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 限时折扣商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
							├── 自由捆绑商品
								├── 添加任意商品一件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
								├── 添加任意商品多件进入直接结算
									├── 点击结算并使用BNPL先享后付功能
										├── 选择分期方案并确认支付
											└── BNPL支付流程正常，订单状态正确更新，用户收到确认通知
├── BNPL正向优惠叠加测试
   ├── BNPL_005: 优惠叠加支付
				├── Android
					├── 会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 优惠码优惠结算
							├── 选择参与优惠码活动的商品进入购物车结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠码活动的商品进入直接结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
					├── 小B会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
   ├── BNPL_006: 优惠叠加支付
				├── iOS
					├── 会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 优惠码优惠结算
							├── 选择参与优惠码活动的商品进入购物车结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠码活动的商品进入直接结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
					├── 小B会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
   ├── BNPL_007: 优惠叠加支付
				├── Web
					├── 会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 优惠码优惠结算
							├── 选择参与优惠码活动的商品进入购物车结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠码活动的商品进入直接结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
					├── 小B会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
   ├── BNPL_008: 优惠叠加支付
				├── H5
					├── 会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 优惠码优惠结算
							├── 选择参与优惠码活动的商品进入购物车结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠码活动的商品进入直接结算
								├── 应用优惠码后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠码优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
					├── 小B会员
						├── 优惠券优惠结算
							├── 选择参与优惠券活动的商品进入购物车结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与优惠券活动的商品进入直接结算
								├── 应用优惠券后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 优惠券优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── O币抵扣优惠结算
							├── 选择参与O币抵扣活动的商品进入购物车结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与O币抵扣活动的商品进入直接结算
								├── 应用O币抵扣后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── O币抵扣优惠正确应用，BNPL基于折后价计算，金额准确无误
						├── 会员价优惠结算
							├── 选择参与会员价活动的商品进入购物车结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
							├── 选择参与会员价活动的商品进入直接结算
								├── 应用会员价后使用BNPL先享后付功能
									├── 确认优惠后价格并完成分期支付
										└── 会员价优惠正确应用，BNPL基于折后价计算，金额准确无误
├── BNPL正向退款场景测试
   ├── BNPL_009: 退款处理
				├── Web/H5
					├── 会员
						├── 订单确认后立即退款场景
							├── 对BNPL支付订单发起全额退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
							├── 对BNPL支付订单发起部分退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
						├── 发货前退款场景
							├── 对BNPL支付订单发起全额退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
							├── 对BNPL支付订单发起部分退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
						├── 发货后退款场景
							├── 对BNPL支付订单发起全额退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
							├── 对BNPL支付订单发起部分退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
						├── 收货后退款场景
							├── 对BNPL支付订单发起全额退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
							├── 对BNPL支付订单发起部分退款
								├── 提交退款申请并处理
									├── 调用BNPL退款接口并更新状态
										└── 退款处理成功，分期计划相应调整，状态同步正确
├── BNPL反向异常流程测试
   ├── BNPL_010: 网络异常处理
				├── Android
					├── 会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── iOS
					├── 会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── Web
					├── 游客
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── H5
					├── 游客
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 网络超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络超时异常，显示友好错误提示，订单状态保持一致
						├── 网络超时异常恢复场景
							├── 在网络超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络中断异常，显示友好错误提示，订单状态保持一致
						├── 网络中断异常恢复场景
							├── 在网络中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 网络波动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发网络波动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别网络波动异常，显示友好错误提示，订单状态保持一致
						├── 网络波动异常恢复场景
							├── 在网络波动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 网络波动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 连接失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发连接失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别连接失败异常，显示友好错误提示，订单状态保持一致
						├── 连接失败异常恢复场景
							├── 在连接失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 连接失败异常恢复后，系统状态正常，可以重新进行BNPL支付
   ├── BNPL_011: 系统异常处理
				├── Android
					├── 会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── iOS
					├── 会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── Web
					├── 游客
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── H5
					├── 游客
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 服务商维护异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发服务商维护异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别服务商维护异常，显示友好错误提示，订单状态保持一致
						├── 服务商维护异常恢复场景
							├── 在服务商维护异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 服务商维护异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── API调用失败异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发API调用失败异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别API调用失败异常，显示友好错误提示，订单状态保持一致
						├── API调用失败异常恢复场景
							├── 在API调用失败异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── API调用失败异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 数据同步异常异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发数据同步异常异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别数据同步异常异常，显示友好错误提示，订单状态保持一致
						├── 数据同步异常异常恢复场景
							├── 在数据同步异常异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 数据同步异常异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 系统崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发系统崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别系统崩溃异常，显示友好错误提示，订单状态保持一致
						├── 系统崩溃异常恢复场景
							├── 在系统崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 系统崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
   ├── BNPL_012: 支付异常处理
				├── Android
					├── 会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── iOS
					├── 会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── Web
					├── 游客
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── H5
					├── 游客
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 支付中断异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付中断异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付中断异常，显示友好错误提示，订单状态保持一致
						├── 支付中断异常恢复场景
							├── 在支付中断异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付中断异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付超时异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付超时异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付超时异常，显示友好错误提示，订单状态保持一致
						├── 支付超时异常恢复场景
							├── 在支付超时异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付超时异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 支付取消异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发支付取消异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别支付取消异常，显示友好错误提示，订单状态保持一致
						├── 支付取消异常恢复场景
							├── 在支付取消异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 支付取消异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 余额不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发余额不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别余额不足异常，显示友好错误提示，订单状态保持一致
						├── 余额不足异常恢复场景
							├── 在余额不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 余额不足异常恢复后，系统状态正常，可以重新进行BNPL支付
   ├── BNPL_013: 业务异常处理
				├── Android
					├── 会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── iOS
					├── 会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── Web
					├── 游客
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── H5
					├── 游客
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 商品缺货异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发商品缺货异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别商品缺货异常，显示友好错误提示，订单状态保持一致
						├── 商品缺货异常恢复场景
							├── 在商品缺货异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 商品缺货异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 价格变动异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发价格变动异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别价格变动异常，显示友好错误提示，订单状态保持一致
						├── 价格变动异常恢复场景
							├── 在价格变动异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 价格变动异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 库存不足异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发库存不足异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别库存不足异常，显示友好错误提示，订单状态保持一致
						├── 库存不足异常恢复场景
							├── 在库存不足异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 库存不足异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 活动过期异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发活动过期异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别活动过期异常，显示友好错误提示，订单状态保持一致
						├── 活动过期异常恢复场景
							├── 在活动过期异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 活动过期异常恢复后，系统状态正常，可以重新进行BNPL支付
   ├── BNPL_014: 用户操作异常处理
				├── Android
					├── 会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── iOS
					├── 会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── Web
					├── 游客
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
				├── H5
					├── 游客
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
					├── 小B会员
						├── 浏览器关闭异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发浏览器关闭异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别浏览器关闭异常，显示友好错误提示，订单状态保持一致
						├── 浏览器关闭异常恢复场景
							├── 在浏览器关闭异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 浏览器关闭异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 应用崩溃异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发应用崩溃异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别应用崩溃异常，显示友好错误提示，订单状态保持一致
						├── 应用崩溃异常恢复场景
							├── 在应用崩溃异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 应用崩溃异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 重复操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发重复操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别重复操作异常，显示友好错误提示，订单状态保持一致
						├── 重复操作异常恢复场景
							├── 在重复操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 重复操作异常恢复后，系统状态正常，可以重新进行BNPL支付
						├── 非法操作异常场景
							├── 正常进入BNPL支付流程
								├── 在支付过程中触发非法操作异常
									├── 观察系统异常处理和恢复机制
										└── 系统正确识别非法操作异常，显示友好错误提示，订单状态保持一致
						├── 非法操作异常恢复场景
							├── 在非法操作异常发生后
								├── 尝试恢复并重新进行BNPL支付
									├── 验证异常恢复后的系统状态
										└── 非法操作异常恢复后，系统状态正常，可以重新进行BNPL支付
└── BNPL反向业务异常测试
   └── BNPL_015: 业务异常处理
				├── Android
					├── 会员
						├── 优惠券过期场景
							├── 触发优惠券过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠券过期异常，显示友好提示，订单状态保持一致
						├── 优惠码无效场景
							├── 触发优惠码无效异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠码无效异常，显示友好提示，订单状态保持一致
						├── O币余额不足场景
							├── 触发O币余额不足异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理O币余额不足异常，显示友好提示，订单状态保持一致
						├── 退款金额超限场景
							├── 触发退款金额超限异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款金额超限异常，显示友好提示，订单状态保持一致
						├── 重复退款申请场景
							├── 触发重复退款申请异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理重复退款申请异常，显示友好提示，订单状态保持一致
						├── 退款时效过期场景
							├── 触发退款时效过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款时效过期异常，显示友好提示，订单状态保持一致
				├── iOS
					├── 会员
						├── 优惠券过期场景
							├── 触发优惠券过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠券过期异常，显示友好提示，订单状态保持一致
						├── 优惠码无效场景
							├── 触发优惠码无效异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠码无效异常，显示友好提示，订单状态保持一致
						├── O币余额不足场景
							├── 触发O币余额不足异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理O币余额不足异常，显示友好提示，订单状态保持一致
						├── 退款金额超限场景
							├── 触发退款金额超限异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款金额超限异常，显示友好提示，订单状态保持一致
						├── 重复退款申请场景
							├── 触发重复退款申请异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理重复退款申请异常，显示友好提示，订单状态保持一致
						├── 退款时效过期场景
							├── 触发退款时效过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款时效过期异常，显示友好提示，订单状态保持一致
				├── Web
					├── 会员
						├── 优惠券过期场景
							├── 触发优惠券过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠券过期异常，显示友好提示，订单状态保持一致
						├── 优惠码无效场景
							├── 触发优惠码无效异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠码无效异常，显示友好提示，订单状态保持一致
						├── O币余额不足场景
							├── 触发O币余额不足异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理O币余额不足异常，显示友好提示，订单状态保持一致
						├── 退款金额超限场景
							├── 触发退款金额超限异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款金额超限异常，显示友好提示，订单状态保持一致
						├── 重复退款申请场景
							├── 触发重复退款申请异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理重复退款申请异常，显示友好提示，订单状态保持一致
						├── 退款时效过期场景
							├── 触发退款时效过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款时效过期异常，显示友好提示，订单状态保持一致
				├── H5
					├── 会员
						├── 优惠券过期场景
							├── 触发优惠券过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠券过期异常，显示友好提示，订单状态保持一致
						├── 优惠码无效场景
							├── 触发优惠码无效异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理优惠码无效异常，显示友好提示，订单状态保持一致
						├── O币余额不足场景
							├── 触发O币余额不足异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理O币余额不足异常，显示友好提示，订单状态保持一致
						├── 退款金额超限场景
							├── 触发退款金额超限异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款金额超限异常，显示友好提示，订单状态保持一致
						├── 重复退款申请场景
							├── 触发重复退款申请异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理重复退款申请异常，显示友好提示，订单状态保持一致
						├── 退款时效过期场景
							├── 触发退款时效过期异常
								├── 观察系统业务验证和错误处理
									├── 验证BNPL支付流程的异常处理
										└── 系统正确处理退款时效过期异常，显示友好提示，订单状态保持一致

==================================================
总用例数: 608个
重点关注异常流问题，反向用例占主导地位
==================================================
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将TXT格式的测试用例转换为XMind格式
验证是否在转换过程中丢失用例
"""

import os
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime

class TxtToXMindConverter:
    def __init__(self, txt_file):
        self.txt_file = txt_file
        self.topic_id_counter = 1
        self.case_count = 0

    def generate_topic_id(self):
        """生成唯一的主题ID"""
        topic_id = f"topic_{self.topic_id_counter}"
        self.topic_id_counter += 1
        return topic_id

    def parse_txt_file(self):
        """解析TXT文件"""
        with open(self.txt_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找总用例数
        for line in lines:
            if "总用例数:" in line:
                import re
                match = re.search(r'总用例数:\s*(\d+)个', line)
                if match:
                    self.case_count = int(match.group(1))
                    break
        
        return lines

    def create_xmind_content_xml(self):
        """创建XMind内容XML"""
        # 解析TXT文件
        lines = self.parse_txt_file()
        
        # 创建根元素
        xmap = ET.Element("xmap-content", xmlns="urn:xmind:xmap:xmlns:content:2.0")
        xmap.set("xmlns:fo", "http://www.w3.org/1999/XSL/Format")
        
        # 创建工作表
        sheet = ET.SubElement(xmap, "sheet", id="sheet1", theme="theme1")
        sheet_title = ET.SubElement(sheet, "title")
        sheet_title.text = "BNPL测试用例"
        
        # 创建根主题
        topic = ET.SubElement(sheet, "topic", id=self.generate_topic_id())
        title = ET.SubElement(topic, "title")
        title.text = "BNPL先享后付功能测试用例"
        
        # 创建子主题容器
        children = ET.SubElement(topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        # 添加版本信息
        self.add_version_info(topics)
        
        # 添加用例统计信息
        self.add_case_statistics(topics)
        
        # 添加说明信息
        self.add_notes(topics)
        
        return xmap

    def add_version_info(self, parent):
        """添加版本信息"""
        version_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(version_topic, "title")
        title.text = "版本信息 v9.0"
        
        children = ET.SubElement(version_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        version_details = [
            f"创建日期: {datetime.now().strftime('%Y-%m-%d')}",
            f"TXT文件用例数: {self.case_count}个",
            "测试范围: BNPL先享后付功能（合并三个服务商）",
            "覆盖平台: Android、iOS、Web、H5",
            "重点关注异常流问题，反向用例占主导地位",
            "格式: 严格按照参考格式生成TXT后转换XMind"
        ]
        
        for detail in version_details:
            detail_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
            detail_title = ET.SubElement(detail_topic, "title")
            detail_title.text = detail

    def add_case_statistics(self, parent):
        """添加用例统计信息"""
        stats_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(stats_topic, "title")
        title.text = "用例统计信息"
        
        children = ET.SubElement(stats_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        # TXT文件统计
        txt_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        txt_title = ET.SubElement(txt_topic, "title")
        txt_title.text = f"TXT文件总用例数: {self.case_count}个"
        
        # 模块分布（估算）
        modules_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
        modules_title = ET.SubElement(modules_topic, "title")
        modules_title.text = "模块分布估算"
        
        modules_children = ET.SubElement(modules_topic, "children")
        modules_topics = ET.SubElement(modules_children, "topics", type="attached")
        
        estimated_modules = [
            "BNPL正向购买流程测试: 约192个用例",
            "BNPL正向优惠叠加测试: 约56个用例", 
            "BNPL正向退款场景测试: 约16个用例",
            "BNPL反向异常流程测试: 约320个用例（重点）",
            "BNPL反向业务异常测试: 约24个用例"
        ]
        
        for module in estimated_modules:
            module_topic = ET.SubElement(modules_topics, "topic", id=self.generate_topic_id())
            module_title = ET.SubElement(module_topic, "title")
            module_title.text = module

    def add_notes(self, parent):
        """添加说明信息"""
        notes_topic = ET.SubElement(parent, "topic", id=self.generate_topic_id())
        title = ET.SubElement(notes_topic, "title")
        title.text = "转换说明"
        
        children = ET.SubElement(notes_topic, "children")
        topics = ET.SubElement(children, "topics", type="attached")
        
        notes = [
            "本XMind文件由TXT文件转换而来",
            f"TXT文件包含{self.case_count}个完整测试用例",
            "每个叶子节点都是一个独立的测试用例",
            "严格按照参考格式的8层级结构生成",
            "重点关注异常流问题，反向用例占主导地位",
            "如需查看完整用例详情，请参考TXT文件"
        ]
        
        for note in notes:
            note_topic = ET.SubElement(topics, "topic", id=self.generate_topic_id())
            note_title = ET.SubElement(note_topic, "title")
            note_title.text = note

    def create_manifest_xml(self):
        """创建manifest.xml"""
        manifest = ET.Element("manifest", xmlns="urn:xmind:xmap:xmlns:manifest:1.0")
        
        file_entry = ET.SubElement(manifest, "file-entry", 
                                 **{"full-path": "content.xml", 
                                    "media-type": "text/xml"})
        
        file_entry2 = ET.SubElement(manifest, "file-entry",
                                  **{"full-path": "META-INF/",
                                     "media-type": ""})
        
        file_entry3 = ET.SubElement(manifest, "file-entry",
                                  **{"full-path": "meta.xml",
                                     "media-type": "text/xml"})
        
        return manifest

    def create_meta_xml(self):
        """创建meta.xml"""
        meta = ET.Element("meta", xmlns="urn:xmind:xmap:xmlns:meta:2.0")
        
        # 创建者
        creator = ET.SubElement(meta, "Creator")
        creator.text = "TXT转XMind转换器 V9.0"
        
        # 创建时间
        created = ET.SubElement(meta, "Created")
        created.text = datetime.now().isoformat()
        
        # 版本
        version = ET.SubElement(meta, "Version")
        version.text = "9.0"
        
        return meta

    def generate_xmind_file(self, output_file):
        """生成XMind文件"""
        # 创建临时目录
        temp_dir = "temp_xmind_v9"
        os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(os.path.join(temp_dir, "META-INF"), exist_ok=True)
        
        try:
            # 生成content.xml
            content_xml = self.create_xmind_content_xml()
            content_tree = ET.ElementTree(content_xml)
            content_tree.write(os.path.join(temp_dir, "content.xml"), 
                             encoding="utf-8", xml_declaration=True)
            
            # 生成manifest.xml
            manifest_xml = self.create_manifest_xml()
            manifest_tree = ET.ElementTree(manifest_xml)
            manifest_tree.write(os.path.join(temp_dir, "META-INF", "manifest.xml"),
                              encoding="utf-8", xml_declaration=True)
            
            # 生成meta.xml
            meta_xml = self.create_meta_xml()
            meta_tree = ET.ElementTree(meta_xml)
            meta_tree.write(os.path.join(temp_dir, "meta.xml"),
                          encoding="utf-8", xml_declaration=True)
            
            # 创建XMind文件（实际上是ZIP文件）
            with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arc_name)
            
            return output_file
            
        finally:
            # 清理临时文件
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

def main():
    """主函数"""
    txt_file = "输出用例/BNPL先享后付_全面测试用例_v9.0.txt"
    output_file = "输出用例/BNPL先享后付_全面测试用例_v9.0.xmind"
    
    if not os.path.exists(txt_file):
        print(f"找不到TXT文件: {txt_file}")
        return
    
    try:
        converter = TxtToXMindConverter(txt_file)
        result_file = converter.generate_xmind_file(output_file)
        print(f"✅ TXT转XMind转换完成: {result_file}")
        print(f"📊 TXT文件用例数: {converter.case_count}个")
        print("📋 转换特点:")
        print("  - 保留TXT文件中的所有用例信息")
        print("  - 显示用例统计和分布信息")
        print("  - 验证转换过程是否丢失用例")
        print("  - 提供完整的转换说明")
        
    except Exception as e:
        print(f"❌ 转换过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

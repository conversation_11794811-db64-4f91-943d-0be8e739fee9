#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BNPL正确格式测试用例生成器
严格按照V3.0的8层级结构，每个叶子节点都是一个独立用例
重点关注异常流问题，反向用例占60-70%
"""

import json
from datetime import datetime

class CorrectFormatBNPLGenerator:
    def __init__(self):
        self.case_id_counter = 1
        
        # 平台和用户类型映射
        self.platform_users = {
            "Android": ["会员", "小B会员"],
            "iOS": ["会员", "小B会员"],
            "Web": ["游客", "会员", "小B会员"],
            "H5": ["游客", "会员", "小B会员"]
        }
        
        # 购买场景
        self.purchase_scenarios = [
            "购物车结算",
            "直接购买"
        ]
        
        # 商品类型（精简为3类）
        self.product_types = [
            "正常商品",
            "限时折扣商品", 
            "自由捆绑商品"
        ]
        
        # 操作类型
        self.operations = [
            "添加任意商品一件",
            "添加任意商品多件"
        ]
        
        # 异常场景（重点扩展）
        self.exception_scenarios = {
            "网络异常": [
                "网络超时", "网络中断", "网络波动", "连接失败"
            ],
            "系统异常": [
                "服务商维护", "API调用失败", "数据同步异常", "系统崩溃"
            ],
            "支付异常": [
                "支付中断", "支付超时", "支付取消", "余额不足"
            ],
            "业务异常": [
                "商品缺货", "价格变动", "库存不足", "活动过期"
            ],
            "用户操作异常": [
                "浏览器关闭", "应用崩溃", "重复操作", "非法操作"
            ]
        }

    def get_case_id(self):
        """获取用例ID"""
        case_id = f"BNPL_{self.case_id_counter:03d}"
        self.case_id_counter += 1
        return case_id

    def generate_positive_purchase_cases(self):
        """生成正向购买流程测试用例（每个叶子节点都是一个用例）"""
        cases = []
        
        # 按照8层级结构生成：平台→用户→场景→商品类型→操作→动作→子动作→预期结果
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                for scenario in self.purchase_scenarios:
                    for product_type in self.product_types:
                        for operation in self.operations:
                            case = {
                                "case_id": self.get_case_id(),
                                "case_name": "支付",
                                "case_type": "正向流程",
                                "structure": {
                                    "platform": platform,
                                    "user_type": user_type,
                                    "scenario": scenario,
                                    "product_type": product_type,
                                    "operation": f"{operation}进入{scenario}",
                                    "action": "点击结算并使用BNPL先享后付功能",
                                    "sub_action": "选择分期方案并确认支付",
                                    "expected_result": "BNPL支付流程正常，订单状态正确更新，用户收到确认通知"
                                },
                                "priority": "高"
                            }
                            cases.append(case)
        
        return cases

    def generate_positive_discount_cases(self):
        """生成正向优惠叠加测试用例"""
        cases = []
        
        discount_types = ["优惠券", "优惠码", "O币抵扣", "会员价"]
        
        for platform in self.platform_users.keys():
            for user_type in self.platform_users[platform]:
                if user_type == "游客":
                    continue  # 游客不参与大部分优惠
                
                for discount in discount_types:
                    if discount == "优惠码" and user_type == "小B会员":
                        continue  # 小B会员不能使用优惠码
                    
                    for scenario in self.purchase_scenarios:
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": "优惠叠加支付",
                            "case_type": "正向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{discount}优惠结算",
                                "product_type": "参与优惠活动商品",
                                "operation": f"选择参与{discount}活动的商品进入{scenario}",
                                "action": f"应用{discount}后使用BNPL先享后付功能",
                                "sub_action": "确认优惠后价格并完成分期支付",
                                "expected_result": f"{discount}优惠正确应用，BNPL基于折后价计算，金额准确无误"
                            },
                            "discount_type": discount,
                            "priority": "中"
                        }
                        cases.append(case)
        
        return cases

    def generate_positive_refund_cases(self):
        """生成正向退款场景测试用例"""
        cases = []
        
        refund_timings = ["订单确认后立即退款", "发货前退款", "发货后退款", "收货后退款"]
        refund_types = ["全额退款", "部分退款"]
        
        for timing in refund_timings:
            for refund_type in refund_types:
                for platform in ["Web", "H5"]:  # 主要平台
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "退款处理",
                        "case_type": "正向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": "会员",
                            "scenario": f"{timing}场景",
                            "product_type": "BNPL支付商品",
                            "operation": f"对BNPL支付订单发起{refund_type}",
                            "action": "提交退款申请并处理",
                            "sub_action": "调用BNPL退款接口并更新状态",
                            "expected_result": "退款处理成功，分期计划相应调整，状态同步正确"
                        },
                        "refund_timing": timing,
                        "refund_type": refund_type,
                        "priority": "高"
                    }
                    cases.append(case)
        
        return cases

    def generate_negative_exception_cases(self):
        """生成反向异常场景测试用例（重点扩展）"""
        cases = []
        
        # 为每个异常类别生成大量测试用例
        for category, exceptions in self.exception_scenarios.items():
            for exception in exceptions:
                # 每个异常在每个平台都要测试
                for platform in self.platform_users.keys():
                    for user_type in self.platform_users[platform]:
                        # 基础异常场景
                        case = {
                            "case_id": self.get_case_id(),
                            "case_name": f"{category}处理",
                            "case_type": "反向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{exception}异常场景",
                                "product_type": "任意商品",
                                "operation": "正常进入BNPL支付流程",
                                "action": f"在支付过程中触发{exception}异常",
                                "sub_action": "观察系统异常处理和恢复机制",
                                "expected_result": f"系统正确识别{exception}异常，显示友好错误提示，订单状态保持一致，用户可重新尝试或选择其他支付方式，不产生重复扣款"
                            },
                            "exception_category": category,
                            "exception_type": exception,
                            "priority": "高" if category in ["支付异常", "系统异常"] else "中"
                        }
                        cases.append(case)
                        
                        # 异常恢复场景
                        recovery_case = {
                            "case_id": self.get_case_id(),
                            "case_name": f"{category}恢复",
                            "case_type": "反向流程",
                            "structure": {
                                "platform": platform,
                                "user_type": user_type,
                                "scenario": f"{exception}异常恢复场景",
                                "product_type": "任意商品",
                                "operation": f"在{exception}异常发生后",
                                "action": "尝试恢复并重新进行BNPL支付",
                                "sub_action": "验证异常恢复后的系统状态",
                                "expected_result": f"{exception}异常恢复后，系统状态正常，可以重新进行BNPL支付，数据一致性保持"
                            },
                            "exception_category": category,
                            "exception_type": f"{exception}_恢复",
                            "priority": "中"
                        }
                        cases.append(recovery_case)
        
        return cases

    def generate_negative_business_cases(self):
        """生成反向业务异常测试用例"""
        cases = []
        
        business_exceptions = [
            ("优惠券过期", "使用过期优惠券进行BNPL支付"),
            ("优惠码无效", "使用无效优惠码进行BNPL支付"),
            ("O币余额不足", "O币余额不足时进行BNPL支付"),
            ("退款金额超限", "退款金额超过订单金额"),
            ("重复退款申请", "对同一订单重复申请退款"),
            ("退款时效过期", "超过退款时效期申请退款")
        ]
        
        for platform in self.platform_users.keys():
            for exception_name, exception_desc in business_exceptions:
                for user_type in self.platform_users[platform][:1]:  # 每平台选一个用户类型
                    case = {
                        "case_id": self.get_case_id(),
                        "case_name": "业务异常处理",
                        "case_type": "反向流程",
                        "structure": {
                            "platform": platform,
                            "user_type": user_type,
                            "scenario": f"{exception_name}场景",
                            "product_type": "任意商品",
                            "operation": exception_desc,
                            "action": "观察系统业务验证和错误处理",
                            "sub_action": "验证BNPL支付流程的异常处理",
                            "expected_result": f"系统正确处理{exception_name}异常，显示友好提示，订单状态保持一致"
                        },
                        "exception_type": exception_name,
                        "priority": "中"
                    }
                    cases.append(case)
        
        return cases

    def generate_all_cases(self):
        """生成所有测试用例"""
        all_cases = {
            "title": "BNPL先享后付功能全面测试用例",
            "version": "v8.0",
            "created_date": datetime.now().strftime("%Y-%m-%d"),
            "total_cases": 0,
            "design_principles": [
                "严格按照V3.0的8层级结构，每个叶子节点都是一个独立用例",
                "重点关注异常流问题，反向用例占60-70%",
                "每个用例都有完整的8层结构：平台→用户→场景→商品类型→操作→动作→子动作→预期结果",
                "精确计数，避免重复计算",
                "异常场景全面覆盖，包含基础场景和恢复场景"
            ],
            "test_modules": []
        }
        
        # 生成各类测试用例
        modules = [
            {
                "module_name": "BNPL正向购买流程测试",
                "description": "验证BNPL支付的正常业务流程，每个操作路径都是独立用例",
                "test_cases": self.generate_positive_purchase_cases()
            },
            {
                "module_name": "BNPL正向优惠叠加测试",
                "description": "验证BNPL与各种优惠活动的叠加使用",
                "test_cases": self.generate_positive_discount_cases()
            },
            {
                "module_name": "BNPL正向退款场景测试",
                "description": "验证不同时机和类型的BNPL订单退款处理",
                "test_cases": self.generate_positive_refund_cases()
            },
            {
                "module_name": "BNPL反向异常流程测试",
                "description": "重点验证各种异常情况下的系统处理能力",
                "test_cases": self.generate_negative_exception_cases()
            },
            {
                "module_name": "BNPL反向业务异常测试",
                "description": "验证业务异常情况下的系统处理能力",
                "test_cases": self.generate_negative_business_cases()
            }
        ]
        
        all_cases["test_modules"] = modules
        all_cases["total_cases"] = sum(len(module["test_cases"]) for module in modules)
        
        return all_cases

def main():
    """主函数"""
    generator = CorrectFormatBNPLGenerator()
    test_cases = generator.generate_all_cases()
    
    # 导出JSON文件
    with open("bnpl_correct_format_cases.json", 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    print(f"✅ BNPL正确格式测试用例已生成: bnpl_correct_format_cases.json")
    print(f"📊 总用例数: {test_cases['total_cases']}个")
    print("\n📋 模块分布:")
    for module in test_cases["test_modules"]:
        case_type_count = {}
        for case in module["test_cases"]:
            case_type = case.get("case_type", "未分类")
            case_type_count[case_type] = case_type_count.get(case_type, 0) + 1
        
        type_info = ", ".join([f"{k}:{v}个" for k, v in case_type_count.items()])
        print(f"  - {module['module_name']}: {len(module['test_cases'])}个用例 ({type_info})")
    
    # 统计正向反向比例
    total_positive = sum(len([case for case in module["test_cases"] if case.get("case_type") == "正向流程"]) 
                        for module in test_cases["test_modules"])
    total_negative = sum(len([case for case in module["test_cases"] if case.get("case_type") == "反向流程"]) 
                        for module in test_cases["test_modules"])
    
    print(f"\n📈 用例类型分布:")
    print(f"  - 正向流程: {total_positive}个 ({total_positive/test_cases['total_cases']*100:.1f}%)")
    print(f"  - 反向流程: {total_negative}个 ({total_negative/test_cases['total_cases']*100:.1f}%)")
    
    print(f"\n🔍 详细统计验证:")
    print(f"  - 每个用例都是独立的叶子节点")
    print(f"  - 严格按照8层级结构生成")
    print(f"  - 重点关注异常流问题")

if __name__ == "__main__":
    main()
